"use client";

import Image from "next/image";
import Link from 'next/link'; // Add this line if needed
import Stats from "../components/Stats";
// Define props type
interface LifeAtJamalProps {
  isModalOpen: boolean;
}

const LifeAtJamal: React.FC<LifeAtJamalProps> = ({ isModalOpen }) => {
  const academicImages = [
    { src: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Academics/01.jpg", alt: "Graduate" },
    { src: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Academics/02.png", alt: "Library" },
    { src: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Academics/03.jpg", alt: "Students studying" },
    { src: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Academics/04.jpg", alt: "Graduation ceremony" },
    { src: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Academics/05.png", alt: "Female graduate" },
    { src: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Academics/06.jpg", alt: "Books" },
    { src: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Academics/01.jpg", alt: "Graduate" }, // Repeated for scroll effect
    { src: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Academics/02.png", alt: "Library" }, // Repeated for scroll effect
    { src: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Academics/03.jpg", alt: "Students studying" }, // Repeated for scroll effect
    { src: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Academics/04.jpg", alt: "Graduation ceremony" }, // Repeated for scroll effect
    { src: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Academics/05.png", alt: "Female graduate" }, // Repeated for scroll effect
    { src: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Academics/06.jpg", alt: "Books" } // Repeated for scroll effect
  ];

  return (
    <section className="pt-24 pb-10 relative">
      <div>
        <Stats isModalOpen={isModalOpen}  />

        {/* Wavy Divider */}
        <div className="w-full mt-16 mb-10">
          <Image
            src="/Vector (Stroke).svg"
            alt="Wavy Divider"
            width={0}
            height={0}
            className="w-full h-auto"
          />
        </div>

        {/* Background Pattern */}
        <div className="absolute top-12 left-0 right-0 bottom-0 w-full h-full z-0 pointer-events-none opacity-100">
          <Image
            src="/homepage/academicsbgimg.png"
            alt="Background Pattern"
            fill
            sizes="100vw"
            priority
            className="object-cover object-center"
          />
        </div>

        {/* Life @ Jamal Section */}
        <div className="px-10 text-center max-w-6xl mx-auto mb-16 relative z-10">
          <h3 className="font-medium text-custom-green text-4xl mb-6">Academics</h3>
          <p className="text-[#565656] font-poppins leading-relaxed">
            Jamal Mohamed College offers a rich and diverse academic environment with a wide range of undergraduate, postgraduate, and research programs across Arts, Science, and Commerce streams. With a strong emphasis on academic excellence, character building, and holistic development, the college nurtures students to achieve both professional success and social responsibility.
          </p>
        </div>

        {/* Academic Images Section */}
        {/* Academic Images Section - Infinite Scroll */}
        <div className="px-4 relative overflow-hidden mb-6 z-10">
          <div className="flex animate-scroll-fast space-x-4 py-4">
            {academicImages.map((image, index) => (
              <div key={`academic-img-${index}`} className="flex-shrink-0 w-40 h-[120px] md:w-52 md:h-[150px] rounded-lg overflow-hidden">
                <Image
                  src={image.src}
                  alt={image.alt}
                  width={208}
                  height={150}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Search Courses Button -> Link */}
        <div className="flex justify-center mt-4 mb-10 relative z-10">
          <Link href="/courses" className="text-xl font-bold border-2 border-custom-green text-custom-green px-8 py-2 rounded-full hover:bg-custom-green hover:text-white transition-colors">
            Search Courses
          </Link>
        </div>

        {/* Grid Section */}
        {/* <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          Dark Green Cards
          <div className="bg-custom-green rounded-lg h-52"></div>
          <div className="bg-custom-green rounded-lg h-52"></div>
          Light Green Cards
          <div className="bg-custom-light-green border-2 border-custom-green rounded-lg h-52"></div>
          <div className="bg-custom-light-green border-2 border-custom-green rounded-lg h-52"></div>
          <div className="bg-custom-light-green border-2 border-custom-green rounded-lg h-52"></div>
          <div className="bg-custom-light-green border-2 border-custom-green rounded-lg h-52"></div>
        </div> */}
      </div>
    </section>
  );
};

export default LifeAtJamal;
