"use client";

const HostelAdmission = ({ rules, messTimings, admissionRules }: any) => {
  return (
    <section className="bg-white py-10 px-4">
      <div className="container max-w-7xl mx-auto space-y-6">
        <div className="rounded-2xl border border-gray-200 p-4 md:p-10 lg:px-16 lg:py-10 shadow-sm bg-white space-y-6">
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              Admission
            </h3>
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              {admissionRules?.map((rule: any, idx: any) => (
                <li key={idx}>{rule}</li>
              ))}
            </ul>
          </div>
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              Rules & Regulations
            </h3>
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              <li>Strict Discipline should be maintained in the Hostel.</li>
              <li>
                Silence should be maintained during the silence hours (06.30
                p.m. to 06.30 a.m.) and compulsory study hours (08.45 p.m. to
                10.00 p.m.).
              </li>
              <li>
                No student is permitted to loiter along the corridor during the
                study hours. Violation of this rule will be viewed seriously.
              </li>
              <li>Inmates must adhere to the mess timings</li>
            </ul>
            <div className="overflow-x-auto flex justify-center pb-8">
              <table className="w-full max-w-4xl mx-auto border-collapse rounded-2xl overflow-hidden font-ramilas font-medium relative border border-custom-green bg-custom-light-green md:shadow-milestone">
                <thead>
                  <tr className="bg-custom-green text-white rounded-t-2xl">
                    <th className="px-6 md:px-8 py-4 text-left">
                      Available Programs
                    </th>
                    <th className="px-6 md:px-8 py-4 text-center">
                      Working Days
                    </th>
                    <th className="px-6 md:px-8 py-4 text-right">Holidays</th>
                  </tr>
                </thead>
                <tbody className="text-custom-green">
                  {messTimings?.map((item: any, index: any) => (
                    <tr
                      key={index}
                      className={
                        index !== messTimings.length - 1
                          ? "border-b border-gray-300"
                          : ""
                      }
                    >
                      <td className="px-6 md:px-8 py-4">{item.program}</td>
                      <td className="px-6 md:px-8 py-4 text-center">
                        {item.workingDays}
                      </td>
                      <td className="px-6 md:px-8 py-4 text-right">
                        {item.holidays}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              {rules.map((rule: any, idx: any) => (
                <li key={idx}>{rule}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HostelAdmission;
