const valueAddedCoursesSchema = {
  name: "valueAddedCourse",
  title: "Value Added Course",
  type: "document",
  fields: [
    {
      name: "title",
      title: "Title / Year",
      type: "string",
      description: "E.g., 'Value Added Course 2023' or just '2023'",
      validation: (Rule: { required: () => any }) => Rule.required(),
    },
    {
      name: "fileUrl",
      title: "File URL",
      type: "url", // Or type: 'file' if you want to upload directly to Sanity
      description: "Link to the PDF or resource",
      validation: (Rule: { required: () => any }) => Rule.required(),
    },
    // Optional: Add a reference back to the course if needed
    // {
    //   name: 'course',
    //   title: 'Associated Course',
    //   type: 'reference',
    //   to: [{ type: 'course' }]
    // }
  ],
  preview: {
    select: {
      title: 'title'
    }
  }
};

export default valueAddedCoursesSchema;