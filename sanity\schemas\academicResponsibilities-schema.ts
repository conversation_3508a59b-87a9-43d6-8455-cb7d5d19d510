import { defineType, defineField } from "sanity";

export default defineType({
  name: "academicResponsibilities",
  title: "Academic Responsibilities",
  type: "document",
  fields: [
    defineField({
      name: "orderIndex",
      title: "Order Index",
      type: "number",
      description: "Used to determine the display order (lower numbers shown first).",
    }),
    defineField({
      name: "category",
      title: "Category",
      type: "string",
      description:
        "The category this responsibility belongs to (e.g., Academic Administration).",
      options: {
        list: [
          {
            title: "Academic Administration",
            value: "Academic Administration",
          },
          {
            title: "Director – Academic Development",
            value: "Director – Academic Development",
          },
          {
            title: "Bursars",
            value: "Bursars",
          },
          {
            title: "Academic Deans",
            value: "Academic Deans",
          },
          {
            title: "Centre for Research & Consultancy",
            value: "Centre for Research & Consultancy",
          },
          {
            title: "Internal Quality Assurance Cell, NAAC Accreditation and NIRF",
            value: "Internal Quality Assurance Cell, NAAC Accreditation and NIRF",
          },
          {
            title: "Registrar of Attendance",
            value: "Registrar of Attendance",
          },
          {
            title: "Controller of Examinations",
            value: "Controller of Examinations",
          },
          {
            title: "Curriculum Development Cell",
            value: "Curriculum Development Cell",
          },
          {
            title: "Hostel Administration",
            value: "Hostel Administration",
          }
        ],
        layout: "dropdown",
      },
    }),
    defineField({
      name: "role",
      title: "Role",
      type: "string",
      description:
        "The role title (e.g., Principal, Vice Principal, Dean, etc.).",
    }),
    defineField({
      name: "name",
      title: "Name",
      type: "string",
      description: "Name of the person in the role.",
    }),
    defineField({
      name: "image",
      title: "Image",
      type: "image",
      options: { hotspot: true },
      description: "Upload an image of the person.",
    })
  ],
});
