"use client";

import React, { useState, useCallback, useEffect } from "react";
import Image from "next/image";
import Link from 'next/link'; // Import Link
import useEmblaCarousel from "embla-carousel-react";
import { ArrowRight } from "lucide-react";
import { getDepartments, Department } from "@/sanity/lib/getDepartments"; // Import the fetch function and type

const DepartmentsSection = () => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true });
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]); // State for fetched data
  const [isLoading, setIsLoading] = useState(true); // Loading state

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const data = await getDepartments();
        console.log("Fetched departments:", data);
        
        setDepartments(data);
      } catch (error) {
        console.error("Failed to fetch departments:", error);
        // Handle error appropriately, maybe set an error state
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, []);

  // --- Carousel logic remains the same --- 
  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  const scrollTo = useCallback(
    (index: number) => emblaApi && emblaApi.scrollTo(index),
    [emblaApi]
  );

  const onPrevious = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const onNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;

    setScrollSnaps(emblaApi.scrollSnapList());
    emblaApi.on("select", onSelect);
    onSelect();

    return () => {
      emblaApi.off("select", onSelect);
    };
  }, [emblaApi, onSelect]);
  // --- End of Carousel logic ---

  // Auto-scroll effect
  useEffect(() => {
    if (!emblaApi || departments.length === 0) return;

    const timer = setInterval(() => {
      emblaApi.scrollNext();
    }, 5000); // Scroll every 5 seconds

    return () => clearInterval(timer); // Clear interval on unmount or dependency change
  }, [emblaApi, departments]); // Rerun if emblaApi or departments change

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto max-w-7xl px-6">
        {/* Row 1: Title and Button */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <h2 className="text-4xl md:text-4xl font-bold text-custom-green mb-4 md:mb-0">
            Departments
          </h2>
          {/* Optional: Link to a page showing all departments */}
          <Link href="/courses" className="hidden md:block bg-transparent text-custom-green px-16 py-2 rounded-full border border-custom-green">
            View All
          </Link>
        </div>

        {/* Row 2: Paragraph */}
        <p className="text-gray-700 max-w-7xl mb-12">
          At Jamal Mohamed College, we offer a wide range of departments across Arts, Science, and Commerce, providing students with diverse academic opportunities.
        </p>

        {/* Row 3: Carousel */}
        <div className="relative">
          {isLoading ? (
            <div className="flex justify-center items-center h-[400px]">Loading departments...</div> // Loading indicator
          ) : departments.length > 0 ? (
            <>
              <div className="overflow-hidden" ref={emblaRef}>
                <div className="flex">
                  {departments.slice(0, 5).map((dept) => ( // Slice the array here
                    <div key={dept._id} className="flex-[0_0_100%] min-w-0"> {/* Use _id as key */}
                      <div className="relative flex flex-col md:flex-row px-1 gap-4 md:gap-0">
                        {/* Left side - Image */}
                        <div className="w-full md:w-3/5 relative h-[250px] md:h-[400px] rounded-2xl overflow-hidden mb-2 md:mb-0">
                          <Image
                            // Provide a fallback URL if dept.imageUrl is undefined
                            src={dept.imageUrl || '/public/placeholder.png'} // Replace with your actual placeholder path
                            alt={dept.name}
                            fill
                            className="object-cover"
                            priority={departments.indexOf(dept) === 0} // Prioritize first image
                          />
                        </div>

                        {/* Right side - Content */}
                        <div className="w-full md:w-3/4 md:absolute md:left-[50%] md:top-[70px] p-6 border-2 border-custom-green rounded-2xl md:rounded-xl bg-white flex flex-col justify-center z-10 md:h-[250px] md:max-w-[45%]">
                          <h3 className="text-xl md:text-2xl font-bold text-custom-green mb-2 md:mb-3">{dept.name}</h3>
                          <p className="text-gray-700 mb-3 md:mb-4 line-clamp-3 overflow-hidden text-ellipsis">
                            {dept.description} {/* Use fetched description */}
                          </p>
                          {/* Link to the specific department page using slug */}
                          <Link href={`/courses/${dept.slug}`} className="text-custom-green font-medium self-start flex items-center gap-1">
                            View More Details
                            <ArrowRight size={16} className="ml-1" />
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Navigation buttons */}
              <div className="hidden md:flex justify-between absolute top-1/2 -translate-y-1/2 left-0 right-0 px-4">
                <button
                  onClick={onPrevious}
                  className="bg-white rounded-full w-10 h-10 flex items-center justify-center shadow-md z-20"
                  disabled={!emblaApi}
                >
              <span className="sr-only">Previous slide</span>
              <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8.84182 3.13514C9.04327 3.32401 9.05348 3.64042 8.86462 3.84188L5.43521 7.49991L8.86462 11.1579C9.05348 11.3594 9.04327 11.6758 8.84182 11.8647C8.64036 12.0535 8.32394 12.0433 8.13508 11.8419L4.38508 7.84188C4.20477 7.64955 4.20477 7.35027 4.38508 7.15794L8.13508 3.15794C8.32394 2.95648 8.64036 2.94628 8.84182 3.13514Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
              </svg>
                </button>
                <button
                  onClick={onNext}
                  className="bg-white rounded-full w-10 h-10 flex items-center justify-center shadow-md z-20"
                  disabled={!emblaApi}
                >
              <span className="sr-only">Next slide</span>
              <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6.1584 3.13514C5.95694 3.32401 5.94673 3.64042 6.13559 3.84188L9.565 7.49991L6.13559 11.1579C5.94673 11.3594 5.95694 11.6758 6.1584 11.8647C6.35986 12.0535 6.67627 12.0433 6.86514 11.8419L10.6151 7.84188C10.7954 7.64955 10.7954 7.35027 10.6151 7.15794L6.86514 3.15794C6.67627 2.95648 6.35986 2.94628 6.1584 3.13514Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path>
              </svg>
                </button>
              </div>

              {/* Dots indicator */}
              <div className="flex justify-center mt-8 gap-2">
                {scrollSnaps.map((_, index) => (
                  <button
                    key={index}
                    className={`w-3 h-3 rounded-full ${selectedIndex === index ? "bg-custom-green" : "bg-gray-300"}`}
                    onClick={() => scrollTo(index)}
                    aria-label={`Go to slide ${index + 1}`}
                  />
                ))}
              </div>
            </>
          ) : (
            <div className="flex justify-center items-center h-[400px]">No departments found.</div> // Handle no data case
          )}

          {/* Mobile View All button */}
          <div className="flex md:hidden justify-center mt-6">
             {/* Optional: Link to a page showing all departments */}
            <Link href="/courses" className="bg-transparent text-custom-green px-24 py-3 rounded-full border-2 border-custom-green text-3xl font-semibold">
              View All
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DepartmentsSection;