"use client";

import React, { useState } from "react";
import Image from "next/image";
import { motion } from "framer-motion";
import Link from 'next/link'; // Import Link here

const AdmissionCategoriesImageCards = () => {
  const [activeCard, setActiveCard] = useState(0); // 0 = Aided, 1 = Unaided Men's, 2 = Unaided Women's

  const handleCardClick = (index: number) => {
    setActiveCard(index);
  };

  return (
    <>
      {/* Row 3: Image Cards */}
      <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
        {/* Left card - Aided */}
        <motion.div
          className={`relative h-[350px] md:h-[400px] rounded-3xl overflow-hidden cursor-pointer border-4 md:border border-custom-green ${
            activeCard === 0
              ? "col-span-12 md:col-span-6"
              : "col-span-12 md:col-span-3"
          }`}
          layout
          transition={{
            type: "spring",
            stiffness: 200,
            damping: 25,
            duration: 0.3,
          }}
          onClick={() => handleCardClick(0)}
        >
          <Image
            src="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Admission/01.jpg" // Updated image URL
            alt="Aided"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-40"></div>
          <motion.div
            className={`absolute inset-0 p-6 flex flex-col ${
              activeCard === 0
                ? "justify-between"
                : "justify-center items-center"
            }`}
            layout
            transition={{
              duration: 0.3,
              ease: "easeInOut",
            }}
          >
            <motion.h3
              className="text-white text-4xl font-bold mb-4"
              layout
              transition={{
                duration: 0.2,
                ease: "easeOut",
              }}
            >
              Aided
            </motion.h3>
            <div className={activeCard === 0 ? "" : "text-center"}>
              <p className="text-white mb-2">Men's Only</p>
              {activeCard === 0 && (
                <Link href="https://jmcerp.in/appliaided/" target="_blank" rel="noopener noreferrer">
                  <motion.button
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="bg-white text-custom-green px-12 py-2 rounded-full text-sm"
                  >
                    View more
                  </motion.button>
                </Link>
              )}
            </div>
          </motion.div>
        </motion.div>

        {/* Unaided - Men's Only */}
        <motion.div
          className={`relative h-[350px] md:h-[400px] rounded-3xl overflow-hidden cursor-pointer border-4 md:border border-custom-green ${
            activeCard === 1
              ? "col-span-12 md:col-span-6"
              : "col-span-12 md:col-span-3"
          }`}
          layout
          transition={{
            type: "spring",
            stiffness: 200,
            damping: 25,
            duration: 0.3,
          }}
          onClick={() => handleCardClick(1)}
        >
          <Image
            src="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Admission/02.jpg"
            alt="Unaided Men's"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-40"></div>
          <motion.div
            className={`absolute inset-0 p-6 flex flex-col ${
              activeCard === 1
                ? "justify-between"
                : "justify-center items-center"
            }`}
            layout
            transition={{
              duration: 0.3,
              ease: "easeInOut",
            }}
          >
            <motion.h3
              className="text-white text-4xl font-bold mb-4"
              layout
              transition={{
                duration: 0.2,
                ease: "easeOut",
              }}
            >
              Unaided
            </motion.h3>
            <div className={activeCard === 1 ? "" : "text-center"}>
              <p className="text-white mb-2">Men's Only</p>
              {activeCard === 1 && (
                <Link href="https://jmcerp.in/applisfm/" target="_blank" rel="noopener noreferrer">
                  <motion.button
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="bg-white text-custom-green px-12 py-2 rounded-full text-sm"
                  >
                    View more
                  </motion.button>
                </Link>
              )}
            </div>
          </motion.div>
        </motion.div>

        {/* Unaided - Women's Only */}
        <motion.div
          className={`relative h-[350px] md:h-[400px] rounded-3xl overflow-hidden cursor-pointer border-4 md:border border-custom-green ${
            activeCard === 2
              ? "col-span-12 md:col-span-6"
              : "col-span-12 md:col-span-3"
          }`}
          layout
          transition={{
            type: "spring",
            stiffness: 200,
            damping: 25,
            duration: 0.3,
          }}
          onClick={() => handleCardClick(2)}
        >
          <Image
            src="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Admission/03.jpg"
            alt="Unaided Women's"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black bg-opacity-40"></div>
          <motion.div
            className={`absolute inset-0 p-6 flex flex-col ${
              activeCard === 2
                ? "justify-between"
                : "justify-center items-center"
            }`}
            layout
            transition={{
              duration: 0.3,
              ease: "easeInOut",
            }}
          >
            <motion.h3
              className="text-white text-4xl font-bold mb-4"
              layout
              transition={{
                duration: 0.2,
                ease: "easeOut",
              }}
            >
              Unaided
            </motion.h3>
            <div className={activeCard === 2 ? "" : "text-center"}>
              <p className="text-white mb-2">Women's Only</p>
              {activeCard === 2 && (
                <Link href="https://jmcerp.in/applisfw/" target="_blank" rel="noopener noreferrer">
                  <motion.button
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="bg-white text-custom-green px-12 py-2 rounded-full text-sm"
                  >
                    View more
                  </motion.button>
                </Link>
              )}
            </div>
          </motion.div>
        </motion.div>
      </div>
      {/* Mobile Know More button */}
      <div className="flex md:hidden justify-center mt-8">
        <button className="bg-transparent text-custom-green px-14 py-3 rounded-full border-2 border-custom-green text-xl font-semibold">
          View Know More
        </button>
      </div>
    </>
  );
};

export default AdmissionCategoriesImageCards;
