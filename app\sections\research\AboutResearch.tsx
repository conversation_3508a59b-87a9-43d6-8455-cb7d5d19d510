import React from "react";
import Image from "next/image";

const AboutResearch = () => {
  return (
    <section className="mb-16 px-4 sm:px-6 md:px-8">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Left side - Full image card */}
        <div className="relative h-[300px] md:h-[500px] rounded-3xl overflow-hidden shadow-lg">
          <Image
            src="/homepage/otrbgimg1.png"
            alt="Research at JMC"
            fill
            className="object-cover"
          />
        </div>

        {/* Right side - Content card */}
        <div className="bg-white p-4 sm:p-6 md:p-8 rounded-3xl shadow-lg border-2 border-custom-green">
          <h2 className="text-2xl md:text-3xl font-bold text-custom-green mb-4 md:mb-6">About research</h2>
          <ul className="space-y-3 md:space-y-4 text-gray-700 text-sm md:text-base">
            <li className="flex items-start">
              <span className="text-custom-green font-bold mr-2 text-lg md:text-xl">•</span>
              <p>To integrate research activities in both basic as well as applied research relating to societal and career development.</p>
            </li>
            <li className="flex items-start">
              <span className="text-custom-green font-bold mr-2 text-lg md:text-xl">•</span>
              <p>To create transparent, effective and efficient systems for innovative research and publications.</p>
            </li>
            <li className="flex items-start">
              <span className="text-custom-green font-bold mr-2 text-lg md:text-xl">•</span>
              <p>To develop research infrastructure and resources to undertake quality research and to enhance to undertake quality research inputs.</p>
            </li>
            <li className="flex items-start">
              <span className="text-custom-green font-bold mr-2 text-lg md:text-xl">•</span>
              <p>To assist and aid external funded projects offered by agencies such as UGC, DST, DBT, etc.</p>
            </li>
            <li className="flex items-start">
              <span className="text-custom-green font-bold mr-2 text-lg md:text-xl">•</span>
              <p>To create awareness on IPR and encourage to get patents.</p>
            </li>
          </ul>
        </div>
      </div>
    </section>
  );
};

export default AboutResearch;
