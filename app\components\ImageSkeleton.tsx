interface ImageSkeletonProps {
  count?: number;
  className?: string;
}

const ImageSkeleton = ({ count = 4, className = "" }: ImageSkeletonProps) => {
  return (
    <div className={`grid gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 ${className}`}>
      {Array.from({ length: count }).map((_, idx) => (
        <div
          key={idx}
          className="h-36 sm:h-48 md:h-60 rounded-2xl overflow-hidden shadow-md bg-gray-200 animate-pulse"
        >
          <div className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-shimmer"></div>
        </div>
      ))}
    </div>
  );
};

export default ImageSkeleton;
