"use client";

import React from 'react';

interface InfoItem {
  type: 'paragraph' | 'bullet' | 'author'; // Added 'author' type
  text: string;
  subtext?: string;
}

interface InfoSection {
  heading?: string;
  items: InfoItem[];
}

interface InfoCardProps {
  cardTitle: string;
  sections: InfoSection[];
}

const InfoCard: React.FC<InfoCardProps> = ({ cardTitle, sections }) => {
  const isCollegeSong = cardTitle === "College Song" && sections.length === 2;

  return (
    <section className="pt-16">
      <div className="relative px-4 md:px-8 mx-auto max-w-7xl">
        {/* Title inside card */}
        <div className="text-center mb-8">
          <h2 className="text-3xl md:text-4xl font-bold text-custom-green">
            {cardTitle}
          </h2>
        </div>
        <div className="bg-white px-6 md:px-12 lg:px-16 py-10 md:py-12 rounded-2xl shadow-[0_0_24px_rgba(0,0,0,0.2)]">
          <div className="space-y-6">


            <div className={isCollegeSong ? "md:grid md:grid-cols-2 md:gap-x-8 lg:gap-x-12" : ""}>
              {sections.map((section, sectionIndex) => (
                <div key={`section-${sectionIndex}`} className={`space-y-4 ${isCollegeSong ? 'mb-6 md:mb-0' : ''}`}>
                  {section.heading && (
                    <h3 className="text-xl md:text-2xl font-bold text-custom-green mb-4">
                      {section.heading}
                    </h3>
                  )}
                  <div className="space-y-3">
                    {section.items.map((item, itemIndex) => {
                      if (item.type === 'author') {
                        return (
                          <div key={`item-${sectionIndex}-${itemIndex}`} className="mt-4">
                            <h4 className="text-lg md:text-xl font-semibold text-custom-green/90">
                              {item.text}
                            </h4>
                          </div>
                        );
                      }
                      return (
                        <div
                          key={`item-${sectionIndex}-${itemIndex}`}
                          className={`flex items-start space-x-3 ${item.type === 'bullet' ? 'ml-4 md:ml-6' : ''}`}
                        >
                          {item.type === 'bullet' && (
                            <span className="text-custom-green font-bold text-lg mt-1">•</span>
                          )}
                          <div className="text-gray-700 leading-relaxed text-base md:text-lg">
                            {item.type === 'paragraph' && typeof item.text === 'string' ? (
                              item.text.split('\\n').map((line, lineIdx) => (
                                <React.Fragment key={lineIdx}>
                                  {line}
                                  {lineIdx < item.text.split('\\n').length - 1 && <br />}
                                </React.Fragment>
                              ))
                            ) : (
                              item.text
                            )}
                            {item.subtext && (
                              <p className="text-sm text-gray-600 mt-1">{item.subtext}</p>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default InfoCard;