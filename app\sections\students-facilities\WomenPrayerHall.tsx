"use client";

import Image from "next/image";
import { useEffect, useState } from "react";
import { client } from "@/lib/sanity";
import { JMC_MOSQUE_QUERY } from "@/sanity/lib/queries";
import ImageSkeleton from "@/app/components/ImageSkeleton";

interface MosqueImage {
  name: string;
  imageUrl: string;
}

interface JmcMosqueData {
  _id: string;
  mosqueImages: MosqueImage[];
  menPrayerHallImages: MosqueImage[];
  womenPrayerHallImages: MosqueImage[];
}

const WomenPrayerHall = () => {
  const [womenPrayerImages, setWomenPrayerImages] = useState<MosqueImage[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    client.fetch(JMC_MOSQUE_QUERY).then((data: JmcMosqueData[]) => {
      if (data.length > 0 && data[0].womenPrayerHallImages?.length > 0) {
        // Filter out images with empty URLs
        const validImages = data[0].womenPrayerHallImages.filter(img =>
          img.imageUrl && img.imageUrl.trim() !== ""
        );
        setWomenPrayerImages(validImages);
      }
      setIsLoading(false);
    }).catch(() => {
      setIsLoading(false);
    });
  }, []);

  return (
    <section className="bg-white pb-10 px-4" id="women-prayer-hall">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green text-center">Prayer Hall for Women</h2>
        {isLoading ? (
          <ImageSkeleton count={1} />
        ) : womenPrayerImages.length > 0 ? (
          womenPrayerImages.length === 1 ? (
            // Single image layout (original design)
            <div className="mx-auto w-full max-w-[600px] rounded-2xl shadow-md overflow-hidden">
              <Image
                src={womenPrayerImages[0].imageUrl}
                alt={womenPrayerImages[0].name}
                width={600}
                height={400}
                className="w-full h-auto object-cover"
                priority
              />
            </div>
          ) : (
            // Multiple images layout (grid like other components)
            <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
              {womenPrayerImages.map((item, idx) => (
                <div
                  key={idx}
                  className="h-36 sm:h-48 md:h-60 rounded-2xl overflow-hidden shadow-md group relative"
                >
                  <Image
                    src={item.imageUrl}
                    alt={item.name}
                    width={500}
                    height={300}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
              ))}
            </div>
          )
        ) : (
          <div className="text-center text-gray-500 py-8">
            <p>No women's prayer hall images available at the moment.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default WomenPrayerHall;
