"use client";

import Image from "next/image";
import Link from 'next/link';
import { useState, useEffect } from "react";

const imageUrls = [
  "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Hero+Section/01.jpg",
  "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Hero+Section/02.jpg",
  "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Hero+Section/03.jpg",
  "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Hero+Section/04.jpg",
  "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Hero+Section/05.jpg",
  "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Hero+Section/06.jpg",
  "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Hero+Section/07.jpg",
];

const HeroSection = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    // Image cycling interval
    const intervalId = setInterval(() => {
      setCurrentImageIndex((prevIndex) => (prevIndex + 1) % imageUrls.length);
    }, 5000); // Change image every 5 seconds

    return () => {
      clearInterval(intervalId); // Clear interval on component unmount
    };
  }, []);

  return (
    <section className="relative">
      {/* Background image with blur effect that fills the entire hero area */}
      <div className="absolute inset-0 overflow-hidden">
        <Image
          src={imageUrls[currentImageIndex]}
          alt="Background"
          fill
          className="object-cover blur-sm brightness-75 scale-105"
          priority
        />
      </div>

      {/* Main content with padding for navbar */}
      <div className="relative pt-[120px] md:pt-[150px] px-4 sm:px-2 md:px-8">
        {/* Main hero image with 16:9 aspect ratio maintained via padding trick */}
        <div className="relative mx-auto max-w-7xl overflow-hidden rounded-t-3xl shadow-2xl border-2 border-white/20">
          {/* 16:9 aspect ratio container using padding-bottom approach */}
          <div className="relative w-full" style={{ paddingBottom: "56.25%" }}>
            <div className="absolute inset-0">
              <Image
                key={currentImageIndex}
                src={imageUrls[currentImageIndex]}
                alt="University campus"
                fill
                className="object-cover object-center transition-opacity duration-1000 ease-in-out"
                priority
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 1200px"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="relative flex flex-col items-center justify-center text-center py-10 px-4 sm:px-6 md:px-8 bg-[#D1D9D1] rounded-bl-3xl border-b-2 border-custom-green shadow-stacked-cards">
        <h3 className="text-4xl sm:text-2xl md:text-5xl font-bold text-custom-green mb-4">
          Build your career Life <br className="md:hidden" /> with JMC Institute 
        </h3>
        <p className="text-custom-new-green font-poppins font-normal max-w-6xl">
          Jamal Mohamed College, was founded in 1951, an autonomous institution, affiliated to the Bharathidasan University, Tiruchirappalli. The college is administered by the Society of Jamal Mohamed College, is established in a sprawling land area of 87 acres, as a minority institution, with the primary objective of providing higher education to the downtrodden and socially backward sections of the society in general. The college is Accredited with A++ Grade by NAAC (4th Cycle) with CGPA 3.69 out of 4.0. The college has been identified as the "College with Potential for Excellence" by the UGC
        </p>
        <div className="mt-6 space-x-4">
          <Link href="/aboutUs" className="hover:bg-custom-green text-custom-green hover:text-white px-8 py-3 rounded-full w-fit font-ramilas font-bold border-2 border-custom-green transition-colors duration-300">
            More Details
          </Link>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;