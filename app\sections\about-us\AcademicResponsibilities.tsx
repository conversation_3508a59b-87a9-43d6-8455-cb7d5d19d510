"use client";

import { useEffect, useState } from "react";
import { ChevronDown } from "lucide-react";
import Image from "next/image";
import { client } from "@/sanity/lib/client";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { ProfileCard } from "@/app/components/ProfileCard";

// Define the order of categories to match schema
const categoryOrder = [
  "Academic Administration",
  "Director – Academic Development",
  "Bursars",
  "Academic Deans",
  "Centre for Research & Consultancy",
  "Internal Quality Assurance Cell, NAAC Accreditation and NIRF",
  "Registrar of Attendance",
  "Controller of Examinations",
  "Curriculum Development Cell",
  "Hostel Administration"
];

const AcademicResponsibilities = () => {
  const [categories, setCategories] = useState<any[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<any | null>(null);
  const [profiles, setProfiles] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const categoriesData = await client.fetch(
          `*[_type == "academicResponsibilities"]{category, orderIndex}`
        );
        
        // Get all unique categories from the data
        const uniqueCategoriesSet = new Set(categoriesData.map((item: any) => item.category));
        const uniqueCategories = Array.from(uniqueCategoriesSet) as string[];
        
        // Sort categories based on the predefined order
        const sortedCategories = uniqueCategories.sort((a: string, b: string) => {
          const indexA = categoryOrder.indexOf(a);
          const indexB = categoryOrder.indexOf(b);
          // If either isn't in the order array, place it at the end
          if (indexA === -1) return 1;
          if (indexB === -1) return -1;
          return indexA - indexB;
        });
        
        setCategories(sortedCategories);
        setSelectedCategory(sortedCategories[0] || null);

        // Fetch profiles based on the first category
        if (sortedCategories.length > 0) {
          fetchProfiles(sortedCategories[0]);
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
      }
    };
    fetchData();
  }, []);

  const fetchProfiles = async (category: any) => {
    try {
      const profilesData = await client.fetch(
        `*[_type == "academicResponsibilities" && category == $category]{
          name, role, "image": image.asset->url, description, orderIndex
        } | order(orderIndex asc)`,
        { category }
      );
      setProfiles(profilesData);
    } catch (error) {
      console.error("Error fetching profiles:", error);
    }
  };

  return (
    <section
      className="pt-12 pb-12 px-10 bg-[rgba(0,46,0,0.03)]"
      id="academic-responsibilites"
    >
      <div className="container mx-auto max-w-7xl">
        <div className="flex flex-col items-center justify-center md:flex-row md:justify-between md:items-center gap-4 mb-8">
          <h2 className="text-custom-green text-center">
            Academic Responsibilities
          </h2>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button className="px-8 py-6 bg-custom-green text-white text-2xl rounded-full font-medium leading-relaxed font-poppins flex items-center gap-2 ">
                {selectedCategory || "Select Category"}
                <ChevronDown size={28} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="center"
              className="bg-custom-green text-white border-none font-poppins  w-[var(--radix-dropdown-trigger-width)] min-w-[300px]"
            >
              {categories.map((category, index) => (
                <DropdownMenuItem
                  key={index}
                  onClick={() => {
                    setSelectedCategory(category);
                    fetchProfiles(category);
                  }}
                >
                  {category}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Profile Cards */}
        <div className="grid gap-4">
          {profiles.length > 0 ? (
            profiles.map((profile, index) =>
              profile && typeof profile === "object" ? (
                <ProfileCard
                  key={index}
                  name={(profile as any).name}
                  role={(profile as any).role}
                  image={(profile as any).image}
                />
              ) : null
            )
          ) : (
            <p className="text-gray-500">
              No profiles available for this category.
            </p>
          )}
        </div>
      </div>
    </section>
  );
};

export default AcademicResponsibilities;
