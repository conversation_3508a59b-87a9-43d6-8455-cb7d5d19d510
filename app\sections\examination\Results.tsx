"use client";

const Results = () => {
  const resultsData = [
    {
      title: "I UG Results (April 2025)",
      period: "The tentative date for result publication is 16–05–2025",
      link: "http://146.190.42.50/jmc/Result/index",
    },
    {
      title: "II UG Results (April 2025)",
      period: "The tentative date for result publication is 15–05–2025",
      link: "http://146.190.42.50/jmc/Result/index",
    },
    {
      title: "III UG Results (April 2025)",
      period: "III UG Results (April 2025)",
      link: "http://146.190.42.50/jmc/Result/index",
    },
    {
      title: "I PG Results (April 2025)",
      period: "I PG Results (April 2025)",
      link: "http://146.190.42.50/jmc/Result/index",
    },
    {
      title: "II PG Results (April 2025)",
      period: "II PG Results (April 2025)",
      link: "http://146.190.42.50/jmc/Result/index",
    },
    {
      title: "Arrear Results (April 2025) – UG",
      period: "The tentative date for result publication is 27–05–2025",
      link: "http://146.190.42.50/jmc/Result/index",
    },
    {
      title: "Arrear Results (April 2025) – PG",
      period: "The tentative date for result publication is 23–05–2025",
      link: "http://146.190.42.50/jmc/Result/index",
    },
    {
      title: "Aalim Sanad Results (April 2025)",
      period: "Announced Later",
      link: "http://146.190.42.50/jmc/Result/index",
    },
    {
      title: "ADCAA Results (April 2025)",
      period: "Announced Later",
      link: "http://146.190.42.50/jmc/Result/index",
    },
    {
      title: "COP Results (April 2025)",
      period: "Announced Later",
      link: "http://146.190.42.50/jmc/Result/index",
    },
    {
      title: "Revaluation Results (April 2025)",
      period: "Announced Later",
      link: "http://146.190.42.50/jmc/Result/index",
    },
    {
      title: "Instant Examination Results (April 2025)",
      period: "Announced Later",
      link: "http://146.190.42.50/jmc/Result/index",
    },
  ];
  return (
    <section
      className="bg-custom-light-green py-6 sm:py-10 px-2 sm:px-4"
      id="results"
    >
      <div className="container max-w-7xl mx-auto overflow-x-auto space-y-6">
        <h2 className="text-custom-green text-center">Results</h2>
        <div className="rounded-full overflow-hidden mb-2">
          <table className="min-w-full table-auto border-collapse">
            <thead>
              <tr className="bg-custom-green text-white text-left text-xs sm:text-sm md:text-base lg:text-xl font-ramilas font-bold">
                <th className="px-2 sm:px-6 py-2 sm:py-3 w-[33%]">Results</th>
                <th className="px-2 sm:px-4 py-2 sm:py-3 w-[50%]">Period</th>
                <th className="px-2 sm:px-4 py-2 sm:py-3 w-[17%] text-center">
                  View
                </th>
              </tr>
            </thead>
          </table>
        </div>
        <table className="min-w-full table-auto border-collapse shadow-md">
          <tbody className="bg-transparent text-xs sm:text-sm md:text-base lg:text-lg text-gray-800">
            {resultsData.map((item, index) => (
              <tr
                key={index}
                className="border-b border-custom-new-green border-opacity-30"
              >
                <td className="px-2 sm:px-6 py-2 sm:py-4 w-[33%] font-poppins">
                  {item.title}
                </td>
                <td className="px-2 sm:px-4 py-2 sm:py-4 w-[50%] font-poppins text-custom-new-green">
                  {item.period}
                </td>
                <td className="px-2 sm:px-4 py-2 sm:py-4 w-[17%] text-center font-ramilas font-bold">
                  <a
                    href={item.link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-block border-2 border-custom-green text-custom-green rounded-full px-1.5 sm:px-4 lg:px-6 py-0.5 sm:py-1 text-[10px] sm:text-sm lg:text-lg hover:bg-custom-green hover:text-white transition-all duration-200 whitespace-nowrap"
                  >
                    View results
                  </a>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </section>
  );
};

export default Results;
