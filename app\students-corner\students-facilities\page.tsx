"use client";

import Footer from "@/app/sections/Footer";
import HostelAdministration from "@/app/sections/students-facilities/HostelAdministration";
import HostelAdmission from "@/app/sections/students-facilities/HostelAdmission";
import HostelFacilities from "@/app/sections/students-facilities/HostelFacilities";
import HostelForMen from "@/app/sections/students-facilities/HostelForMen";
import HostelInfrastructure from "@/app/sections/students-facilities/HostelInfrastructure";
import HostelSportsActivities from "@/app/sections/students-facilities/HostelSportsActivities";
import StudentEnrichmentProgramme from "@/app/sections/students-facilities/StudentEnrichmentProgramme";
import TheWomenHostel from "@/app/sections/students-facilities/TheWomenHostel";
import Navbar from "@/components/Navbar";
import Link from "next/link";
import { wardens } from "@/app/helper/helper";
import {
  mensHostelRules,
  mensHostelMessTimings,
  mensHostelAdmissionRules,
  womensHostelAdmissionRules,
  womenHostelWardens,
  hostelAmenities,
  dailyLifeInTheHostel,
  womensHostelMessTimings,
} from "@/app/helper/helper";
import WomensHostelAdmission from "@/app/sections/students-facilities/WomensHostelAdmission";
import GeneralInformation from "@/app/sections/students-facilities/GeneralInformation";
import Mosque from "@/app/sections/students-facilities/Mosque";
import MenPrayerHall from "@/app/sections/students-facilities/MenPrayerHall";
import WomenPrayerHall from "@/app/sections/students-facilities/WomenPrayerHall";
import StudentCounsellingCentre from "@/app/sections/students-facilities/StudentCounsellingCentre";
import Cafeteria from "@/app/sections/students-facilities/Cafeteria";
import CivilServicesCoachingCentre from "@/app/sections/students-facilities/CivilServicesCoachingCentre";
import DayCareCentre from "@/app/sections/students-facilities/DayCareCentre";
import Transport from "@/app/sections/students-facilities/Transport";
import CommunicationLab from "@/app/sections/students-facilities/CommunicationLab";

const page = () => {
  return (
    <>
      <Navbar fixed={false} border={false} />
      <header
        className="relative mt-4 px-4 py-8 md:py-16 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex justify-between items-center">
          <h1 className="font-ramilas">Student's Corner</h1>
          <ul className="font-poppins flex flex-col md:flex-row md:gap-8 md:pr-16">
            <li className="list-disc">
              <Link href="/">Home</Link>
            </li>
            <li className="list-disc">Student's Facilities</li>
          </ul>
        </div>
      </header>
      <main>
        <div id="hostel-for-men">
          <HostelForMen />
          <HostelInfrastructure />
          <HostelFacilities />
          <StudentEnrichmentProgramme />
          <HostelSportsActivities />
          <HostelAdmission
            rules={mensHostelRules}
            messTimings={mensHostelMessTimings}
            admissionRules={mensHostelAdmissionRules}
          />
          <HostelAdministration wardens={wardens} />
        </div>
        <div id="hostel-for-women">
          <TheWomenHostel />
          <HostelAdministration wardens={womenHostelWardens} />
          <WomensHostelAdmission
            admissionRules={womensHostelAdmissionRules}
            hostelAmenities={hostelAmenities}
            dailyLifeInTheHostel={dailyLifeInTheHostel}
            messTimings={womensHostelMessTimings}
          />
          <GeneralInformation />
        </div>
        <Mosque />
        <MenPrayerHall />
        <WomenPrayerHall />
        <StudentCounsellingCentre />
        <Cafeteria />
        <CivilServicesCoachingCentre />
        <DayCareCentre />
        <Transport />
        <CommunicationLab />
      </main>
      <Footer />
    </>
  );
};

export default page; 
