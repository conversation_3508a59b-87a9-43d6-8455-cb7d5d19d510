import Image from "next/image";

interface MemberCardProps {
  title: string;
  name: string;
  imageUrl: string;
}

const MemberCard = ({ title, name, imageUrl }: MemberCardProps) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-4 flex items-center gap-6 border border-gray-200"> {/* Increased gap */}
      <div className="w-32 h-32 md:w-36 md:h-36 lg:w-44 lg:h-48 relative flex-shrink-0 overflow-hidden rounded-md">
        <Image
          src={imageUrl}
          alt={name}
          fill
          sizes="(max-width: 768px) 128px, 160px" // Adjusted sizes for responsiveness
          className="object-obtain"
        />
      </div>
      <div className="flex flex-col items-start flex-1"> {/* Added flex-1 to take full width */}
        <span className="bg-custom-green text-white text-xs font-semibold px-4 py-2 rounded-full mb-3 uppercase w-full text-center"> {/* Added w-full and text-center */}
          {title}
        </span>
        <p className="text-sm md:text-base font-medium text-gray-700 mt-1 text-center w-full">{name}</p> {/* Added small top margin for name */}
      </div>
    </div>
  );
};

const ManagementCommittee = () => {
  // Dummy data for now, replace with fetched data later
  const committeeMembers = [
    { title: "President", name: "Hajee M. J. Jamal Mohamed Bilal", imageUrl: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/AboutUs/ManagementCommittee/01.jpg" },
    { title: "Secretary & Correspondent", name: "Dr. A.K. Khaja Nazeemudeen", imageUrl: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/AboutUs/ManagementCommittee/02.jpg" },
    { title: "Treasurer", name: "Hajee M. J. Jamal Mohamed", imageUrl: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/AboutUs/ManagementCommittee/03.jpg" },
    { title: "Assistant Secretary", name: "Dr. K. Abdus Samad", imageUrl: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/AboutUs/ManagementCommittee/04.jpg" },
  ];

  return (
    <section className=" bg-gray-50">
      <div className="container mx-auto max-w-7xl">
        <h3 className="text-2xl md:text-3xl font-ramilas text-custom-green text-center mb-8">
          Management committee
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {committeeMembers.map((member, index) => (
            <MemberCard
              key={index}
              title={member.title}
              name={member.name}
              imageUrl={member.imageUrl}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ManagementCommittee;