import type { Rule } from "@sanity/types";

const accreditationSchema = {
  name: "accreditation",
  title: "Accreditation",
  type: "document",
  fields: [
    {
      name: "name",
      title: "Name",
      type: "string",
      validation: (Rule: Rule) => Rule.optional(),
    },
    {
      name: "imageUrl",
      title: "Image URL",
      type: "url",
      description: "Direct URL to the image",
    },
    {
      name: "category",
      title: "Category",
      type: "string",
      validation: (Rule: Rule) => Rule.required().error("Category is required"),
    },
  ],
};

export default accreditationSchema;
