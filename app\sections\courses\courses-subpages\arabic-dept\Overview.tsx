"use client";

import Image from "next/image";

export default function Overview({ overview }: any) {
  console.log("overview details are", overview);
  
  return (
    <section className="px-8 py-10 bg-white">
      <div className="container max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-stretch">
          {/* Image */}
          <div className="h-[400px] md:h-[500px] border-2 border-transparent hover:border-custom-green transition rounded-2xl">
            {overview?.image ? ( // Changed condition to check for overview.image directly
              <Image
                src={overview.image} // Changed src to use overview.image directly
                alt="Department Overview"
                width={700}
                height={600}
                className="rounded-2xl shadow-lg object-cover w-full h-full"
              />
            ) : (
              <div className="w-full h-full bg-gray-100 rounded-2xl flex items-center justify-center">
                <span className="text-gray-400">No image available</span>
              </div>
            )}
          </div>

          {/* Description */}
          <div className="bg-white rounded-2xl p-6 md:p-12 shadow-md h-full flex flex-col border-2 border-transparent hover:border-custom-green transition">
            <div>
              <h3 className="uppercase text-sm text-green-700 font-medium tracking-wider mb-1 font-poppins">
                Department of
              </h3>
              <h2 className="text-3xl font-semibold text-green-900 mb-4">
                {overview?.departmentTitle}
              </h2>
              <p className="text-custom-new-green text-justify">
                {overview?.description || "No description available"}
              </p>
            </div>
          </div>
        </div>

        <div className="mt-10 grid grid-cols-1 md:grid-cols-2 gap-6 items-stretch">
          {/* Mission */}
          <div className="bg-white p-6 md:p-12 rounded-2xl shadow-md h-full flex flex-col border-2 border-transparent hover:border-custom-green transition">
            <h3 className="text-2xl font-semibold text-custom-green mb-3">
              Mission
            </h3>
            <ul className="font-poppins list-disc pl-5 text-custom-new-green space-y-2 flex-grow">
              {overview?.mission?.map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </div>

          {/* Vision */}
          <div className="bg-white p-6 md:p-12 rounded-2xl shadow-md h-full flex flex-col border-2 border-transparent hover:border-custom-green transition">
            <h3 className="text-2xl font-semibold text-custom-green mb-3">
              Vision
            </h3>
            <ul className="font-poppins list-disc pl-5 text-custom-new-green space-y-2 flex-grow">
              {overview?.vision?.map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}
