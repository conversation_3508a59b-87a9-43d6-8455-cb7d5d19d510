"use client";

const HostelAdministration = ({ wardens }: any) => {
  return (
    <section className="bg-white pb-10 px-4">
      <div className="container max-w-7xl mx-auto space-y-4">
        <h2 className="text-custom-green text-center">Administration</h2>
        <p className="text-custom-new-green text-center">
          The Principal of the college is the warden of the hostel. The hostel
          is managed and monitored by the Director and Co-ordinator with the
          help of deputy wardens and sub-wardens
        </p>
        <div className="overflow-x-auto pr-8 pb-8">
          <table className="w-full max-w-7xl mx-auto border-collapse rounded-2xl overflow-hidden font-ramilas font-medium relative border border-custom-green bg-custom-light-green md:shadow-milestone">
            <thead>
              <tr className="bg-custom-green text-white rounded-t-2xl">
                <th className="px-6 md:px-8 py-4 text-left">S.No</th>
                <th className="px-6 md:px-8 py-4 text-left">Name</th>
                <th className="px-6 md:px-8 py-4 text-left">Designation</th>
                <th className="px-6 md:px-8 py-4 text-left">Mobile No.</th>
              </tr>
            </thead>
            <tbody className="text-custom-green">
              {wardens?.map((warden: any, index: any) => (
                <tr
                  key={index}
                  className={
                    index !== wardens.length - 1
                      ? "border-b border-gray-300"
                      : ""
                  }
                >
                  <td className="px-6 md:px-8 py-4">{index + 1}.</td>
                  <td className="px-6 md:px-8 py-4">{warden.name}</td>
                  <td className="px-6 md:px-8 py-4">{warden.designation}</td>
                  <td className="px-6 md:px-8 py-4">{warden.mobile}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </section>
  );
};

export default HostelAdministration;
