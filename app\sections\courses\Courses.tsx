"use client";

import CourseFilters from "@/app/components/CourseFilters";
import Pagination from "@/app/components/Pagination";
// Removed Dialog imports as they are now in the new component
import MobileCourseFilterModal from "@/app/components/MobileCourseFilterModal"; // Import the new component
import { getCourses } from "@/sanity/lib/getCourses";
import { Filter, Search } from "lucide-react"; // Keep Filter icon import if needed elsewhere, or remove if only used in modal
import Image from "next/image";
import { useEffect, useState } from "react";
import Link from "next/link";

const studyLevels = [
  "All",
  "Undergraduate",
  "Postgraduate",
  "M.Phil",
  "phD",
  "PG Diploma",
  "Diploma",
  "Certification Course",
];

const studyPatterns = ["Full time", "Part time"];

const MAX_DESCRIPTION_LENGTH = 350; // Or any length you prefer

export default function Courses() {
  const [expandedDescriptions, setExpandedDescriptions] = useState<{ [key: string]: boolean }>({});
  const [courses, setCourses] = useState<any[]>([]);
  const [selectedLevel, setSelectedLevel] = useState("All");
  const [selectedPattern, setSelectedPattern] = useState("Full time");
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [inputValue, setInputValue] = useState("");
  // const [expandedDescriptions, setExpandedDescriptions] = useState<{ [key: string]: boolean }>({}); // Moved up
  const coursesPerPage = 5;

  useEffect(() => {
    const getData = async () => {
      const data = await getCourses();
      console.log("courses page : ", data);
      
      setCourses(data);
    };
    getData();
  }, []);

  const filteredCourses = courses.filter((course: any) => {
    const levelMatches =
      selectedLevel === "All" || course.studyLevel.includes(selectedLevel);

    const patternMatches =
      course.studyPattern &&
      course.studyPattern.toLowerCase() === selectedPattern.toLowerCase();

    const search = searchTerm.toLowerCase();
    const searchMatches =
      !search.trim() ||
      (course.tags &&
        course.tags.some((tag: string) =>
          tag.toLowerCase().includes(search)
        )) ||
      (course.departmentName &&
        course.departmentName.toLowerCase().includes(search));

    return levelMatches && patternMatches && searchMatches;
  });

  useEffect(() => {
    setCurrentPage(1);
  }, [selectedLevel, selectedPattern, searchTerm]);

  const indexOfLastCourse = currentPage * coursesPerPage;
  const indexOfFirstCourse = indexOfLastCourse - coursesPerPage;
  const currentCourses = filteredCourses.slice(
    indexOfFirstCourse,
    indexOfLastCourse
  );
  const totalPages = Math.ceil(filteredCourses.length / coursesPerPage);

  const handleClear = () => {
    setSelectedLevel("All");
    setSelectedPattern("Full time");
    setSearchTerm("");
  };

  const toggleDescription = (courseId: string) => {
    setExpandedDescriptions(prev => ({
      ...prev,
      [courseId]: !prev[courseId]
    }));
  };

  return (
    <main className="p-4 md:pt-8 md:px-8">
      <div className="container max-w-7xl mx-auto space-y-6">
        {/* Search */}
        {/* Search and Mobile Filter Trigger */}
        <section className="w-full max-w-3xl flex items-center gap-2 font-poppins md:px-2">
          {/* Search Input */}
          <div className="flex-1 flex items-center border border-custom-green rounded-full px-3 sm:px-2 py-2">
            <input
              type="text"
              placeholder="Search Course"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            className="flex-1 bg-transparent outline-none text-sm placeholder:text-[#00000080] px-2"
          />
            <button
              className="bg-custom-green text-white px-6 py-3 rounded-full text-sm font-medium sm:block hidden"
              onClick={() => setSearchTerm(inputValue)}
            >
              Search
            </button>
            <button
              className="bg-custom-green text-white p-3 rounded-full text-sm font-medium sm:hidden block"
              onClick={() => setSearchTerm(inputValue)}
            >
              <Search size={20} />
            </button>
          </div>

          {/* Render Mobile Filter Modal Component */}
          <MobileCourseFilterModal
            studyLevels={studyLevels}
            selectedLevel={selectedLevel}
            setSelectedLevel={setSelectedLevel}
            studyPatterns={studyPatterns}
            selectedPattern={selectedPattern}
            setSelectedPattern={setSelectedPattern}
            handleClear={handleClear}
          />
        </section>

        <section className="flex flex-col md:flex-row gap-6">
          {/* Filters Sidebar (Hidden on Mobile) */}
          <aside className="hidden lg:block w-full md:w-1/4 text-custom-green shadow-sm"> {/* Added hidden md:block */}
            <div className="border border-custom-green rounded-xl p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Filter</h2>
                <button
                  onClick={handleClear}
                  className="bg-custom-green text-white text-sm font-poppins font-semibold px-4 py-1 rounded-full"
                >
                  Clear all
                </button>
              </div>
              <CourseFilters
                studyLevels={studyLevels}
                selectedLevel={selectedLevel}
                setSelectedLevel={setSelectedLevel}
                studyPatterns={studyPatterns}
                selectedPattern={selectedPattern}
                setSelectedPattern={setSelectedPattern}
              />
            </div>
          </aside>

          {/* Course List */}
          <section className="w-full lg:w-3/4 space-y-6 py-4 md:py-8 px-4">
            {currentCourses?.length > 0 ? (
              <>
                {currentCourses.map((course: any) => (
                  <article
                    key={course._id}
                    className="space-y-4 border-b-[2px] border-[#0000004D] pb-8"
                  >
                    <h2 className="text-custom-green text-fluid-h4 font-ramilas font-medium leading-none border-b-2 border-custom-green inline-block">
                      <Link href={`/courses/${course.slug}`}>
                        {course.departmentName}
                      </Link>
                    </h2>
                    <div className="text-custom-new-green">
                      {course.description && course.description.length > MAX_DESCRIPTION_LENGTH && !expandedDescriptions[course._id] ? (
                        <>
                          <p>{`${course.description.substring(0, MAX_DESCRIPTION_LENGTH)}...`}</p>
                          <button 
                            onClick={() => toggleDescription(course._id)} 
                            className="text-custom-green hover:underline font-semibold mt-1"
                          >
                            Read more
                          </button>
                        </>
                      ) : (
                        <>
                          <p>{course.description}</p>
                          {course.description && course.description.length > MAX_DESCRIPTION_LENGTH && (
                            <button 
                              onClick={() => toggleDescription(course._id)} 
                              className="text-custom-green hover:underline font-semibold mt-1"
                            >
                              Read less
                            </button>
                          )}
                        </>
                      )}
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 font-poppins font-medium">
                      <div className="flex items-center gap-2 text-custom-green text-sm rounded-full">
                        <Image
                          src="/courses/study_level.svg"
                          alt="Study Level Icon"
                          width={40}
                          height={40}
                        />
                        <span>
                          Study Level:{" "}
                          <strong>{course.studyLevel.join(", ")}</strong>
                        </span>
                      </div>

                      <div className="flex items-center gap-2 text-custom-green text-sm rounded-full">
                        <Image
                          src="/courses/study_pattern.svg"
                          alt="Study Pattern Icon"
                          width={40}
                          height={40}
                        />
                        <span>
                          Study Pattern: <strong>{course.studyPattern}</strong>
                        </span>
                      </div>
                    </div>
                  </article>
                ))}

                {/* Show pagination only if there are filtered courses */}
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  setCurrentPage={setCurrentPage}
                />
              </>
            ) : (
              <div className="text-center text-lg text-gray-500 font-poppins">
                No courses available for this filter.
              </div>
            )}
          </section>
        </section>
      </div>
    </main>
  );
}
