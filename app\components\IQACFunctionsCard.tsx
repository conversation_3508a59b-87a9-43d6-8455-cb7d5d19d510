import React from "react";

interface IQACSection {
  title: string;
  items: string[];
}

interface IQACFunctionsCardProps {
  title?: string;
  sections: IQACSection[];
}

const IQACFunctionsCard: React.FC<IQACFunctionsCardProps> = ({
  title = "Functions & Objectives",
  sections
}) => {
  return (
    <section className="bg-[#D1D9D1]">
      {/* Main wrapper with white background and padding */}
      <div className="relative px-8 md:px-16 py-8 md:py-4 bg-white">
        {/* Title */}
        <div className="text-center mb-8">
          <h2 className="text-3xl md:text-4xl font-bold text-custom-green mb-6">
            {title}
          </h2>
        </div>

        {/* Card - Single card with glowing shadow */}
        <div className="bg-white px-8 lg:px-24 py-16 rounded-2xl shadow-[0_0_24px_rgba(0,0,0,0.3)]">
          <div className="space-y-8">
            {sections.map((section, sectionIndex) => (
              <div key={sectionIndex} className="space-y-6">
                {/* Section Title */}
                <div className="space-y-3">
                  <h3 className="text-xl md:text-2xl font-bold text-custom-green">
                    {section.title}
                  </h3>
                </div>

                {/* Section Items */}
                <div className="space-y-4">
                  {section.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="flex items-start space-x-3 px-4">
                      <span className="text-custom-green font-bold text-lg mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed text-base md:text-lg">
                        {item}
                      </span>
                    </div>
                  ))}
                </div>

                {/* Add separator between sections except for the last one */}
                {sectionIndex < sections.length - 1 && (
                  <div className="pt-4 border-t border-gray-200"></div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default IQACFunctionsCard;
