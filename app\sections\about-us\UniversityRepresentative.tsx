"use client";

import Image from "next/image";

const UniversityRepresentative = () => {
  return (
    <section
      id="university_representative"
      className="relative text-white bg-cover bg-center px-4 py-8 md:py-12"
      style={{ backgroundImage: "url('/jamal_college.jpeg')" }}
    >
      <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>
      <div className="relative z-10 container mx-auto max-w-6xl lg:max-w-7xl space-y-6">
        <h2 className="text-center">Univeristy Representative</h2>
        <div className="flex flex-col md:flex-row items-center justify-center gap-6 lg:gap-8">
          {/* Image */}
          <div className="w-40 h-52 relative rounded-2xl overflow-hidden shadow-lg">
            <Image
              src="https://jmc.edu/images/jmc/about/univrep.jpg"
              alt="Dr<PERSON> <PERSON><PERSON>"
              layout="fill"
              objectFit="cover"
            />
          </div>

          {/* Info Box */}

          <table className="h-52 w-full max-w-sm rounded-2xl overflow-hidden text-center">
            <thead>
              {" "}
              <tr className="bg-custom-green text-white font-ramilas font-medium">
                <th className="p-4">Dr. K. Premkumar, Professor & Head,</th>
              </tr>
            </thead>
            <tbody className="bg-custom-light-green text-custom-green">
              <tr>
                <td>
                  Dept. of Biomedical Science, <br />
                  Bharathidasan University, <br />
                  Tiruchirappalli – 620 024.
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
  );
};

export default UniversityRepresentative;
