"use client";

import { usePathname } from "next/navigation";
import Navbar, { menuItems } from "@/components/Navbar";
import Link from "next/link";
import Sidebar from "../components/Sidebar";
import Footer from "../sections/Footer";
import ControllerOfExaminations from "../sections/examination/ControllerOfExaminations";
import Results from "../sections/examination/Results";
import StrategicPlan from "../sections/about-us/StrategicPlan";
import HandbookOnAutonomy from "../sections/examination/HandbookOnAutonomy";
import AnnualReportOfExamination from "../sections/examination/AnnualReportOfExamination";
import Calendar from "../sections/examination/Calendar";
import DegreeExaminationSchedule from "../sections/examination/DegreeExaminationSchedule";
import OnlineCertificateVerification from "../sections/examination/OnlineCertificateVerification";

const Page = () => {
  const pathname = usePathname(); // Get current pathname
  // Find the current section based on the pathname
  const currentSection = menuItems.find((item) => item.href === pathname);
  return (
    <>
      <Navbar fixed={true} border={true} />
      <header
        className="relative px-4 md:px-14 xl:px-28 pt-40 pb-16 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex flex-col items-center gap-4 md:flex-row md:justify-between">
          <h1 className="font-ramilas">Examination</h1>
          <ul className="font-poppins flex flex-row justify-center items-center gap-8 md:flex-row md:items-center md:gap-8 md:pr-16">
            <li className="list-disc">
              {" "}
              {/* Restored list-disc */}
              <Link href="/">Home</Link>
            </li>
            <li className="list-disc">
              {" "}
              {/* Restored list-disc */}
              Examination
            </li>
          </ul>
        </div>
      </header>

      <main>
        {currentSection && (
          <Sidebar subsections={currentSection.subSections || []} />
        )}
        <Results />
        <ControllerOfExaminations />
        <HandbookOnAutonomy />
        <AnnualReportOfExamination />
        <Calendar />
        <StrategicPlan
          title="Form Download"
          downloadLabel="Click here to check"
          downloadLink="http://*************/jmc/Hallticket/index"
          sectionId="form-download"
          description="Hall Ticket & CIA Mark April 2025"
        />
        <DegreeExaminationSchedule />
        <OnlineCertificateVerification />
      </main>

      <Footer />
    </>
  );
};

export default Page;
