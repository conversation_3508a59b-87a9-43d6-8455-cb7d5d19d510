const handbook = {
  name: "handbook",
  title: "Examination-Handbook of Autonomy",
  type: "document",
  fields: [
    {
      name: "volume",
      title: "Volume",
      type: "string",
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: "edition",
      title: "Edition",
      type: "string",
    },
    {
      name: "date",
      title: "Date",
      type: "string",
    },
    {
      name: "pdfUrl",
      title: "Handbook of Autonomy (PDF URL)",
      type: "url",
      validation: (Rule: any) =>
        Rule.required().uri({
          allowRelative: false,
          scheme: ["http", "https"],
        }),
    },
  ],
};

export default handbook;
