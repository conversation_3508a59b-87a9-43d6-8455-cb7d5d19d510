import Image from "next/image";

export interface Profile {
  name: string;
  role: string;
  image: string;
  description?: string;
  qualification?: string;
  swapHeaderOrder?: boolean; // new optional prop
}

export const ProfileCard = ({
  name,
  role,
  image,
  swapHeaderOrder = false,
}: Profile) => {
  return (
    <div className="flex flex-col md:flex-row items-center gap-6">
      <div className="w-full h-[350px] md:w-40 md:h-40 lg:w-52 lg:h-52 rounded-lg overflow-hidden">
        <Image
          src={image}
          alt={role}
          width={128}
          height={128}
          className="object-contain w-full h-full"
        />
      </div>
      <div className="flex-1">
        {swapHeaderOrder ? (
          <>
            <h4 className="font-ramilas font-medium text-custom-green mb-2">
              {name}
            </h4>
            <p className="text-custom-new-green text-justify">
              <strong className="font-semibold">{role}</strong>
            </p>
          </>
        ) : (
          <>
            <h4 className="font-ramilas font-medium text-custom-green mb-2">
              {role}
            </h4>
            <p className="text-custom-new-green text-justify">
              <strong className="font-semibold">{name}</strong>
            </p>
          </>
        )}
      </div>
    </div>
  );
};
