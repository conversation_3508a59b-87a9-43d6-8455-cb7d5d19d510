"use client";

import React from "react";
import Sidebar from "@/app/components/Sidebar";
import EContent from "@/app/sections/courses/courses-subpages/arabic-dept/EContent";
import ValueAddedCourses from "@/app/sections/courses/courses-subpages/arabic-dept/ValueAddedCourses";
// import FacultyTable from "@/app/components/FacultyTable"; // Note: FacultyTable might not be needed directly if FacultySection handles it
import Programmes from "@/app/sections/courses/courses-subpages/arabic-dept/Programmes";
import CTAComponent from "@/app/components/CTAComponent";
import Overview from "@/app/sections/courses/courses-subpages/arabic-dept/Overview";
import DepartmentActivities from "@/app/sections/courses/courses-subpages/arabic-dept/DepartmentActivities";
import OurRecruiters from "@/app/sections/courses/courses-subpages/arabic-dept/OurRecruiters";
import Infrastructure from "@/app/sections/courses/courses-subpages/arabic-dept/Infrastructure";
import FacultySection from "@/app/sections/courses/courses-subpages/FacultySection";
import Footer from "@/app/sections/Footer";

// Define the structure for subsections used by the Sidebar
interface Subsection {
  title: string;
  href: string;
}

// Define the expected structure for courseDetails prop
interface CourseDetails {
  departmentName?: string;
  programmes?: any[]; // Replace 'any' with more specific types if available
  programmesAndOutcomes?: {
    title?: string; // Title is optional
    pdfUrl: string;
  };
  faculty?: any[]; // Replace 'any' with more specific types if available
  overview?: any; // Replace 'any' with more specific types if available
  valueAddedCourses?: any[]; // Use specific type if known
  eContent?: any[]; // Use specific type if known
  departmentActivities?: { // Add this field
    _id: string;
    title: string;
    date: string;
    description?: string;
    image?: string; // Changed type to string for URL
  }[];
  recruitersImage?: string[]; // Add new field for recruiter images
}

interface CoursePageClientProps {
  courseDetails: CourseDetails | null;
  subsections: Subsection[];
}

const CoursePageClient: React.FC<CoursePageClientProps> = ({
  courseDetails,
  subsections,
}) => {
  console.log("courseDetails:",courseDetails);
  
  if (!courseDetails) {
    // Handle case where course details might not be found
    // You could return a loading state or a "not found" message
    return <div>Loading course details or course not found...</div>;
  }
  console.log("course client details are", courseDetails);

  return (
    <>
      <div className="flex flex-col md:flex-row relative">
        {/* Sidebar */}
        <Sidebar subsections={subsections} />

        {/* Main Content Area */}
        <main className="flex-grow py-8 px-4"> {/* Adjust margin as needed */}
          {/* Wrap each section with a div and corresponding ID */}
          <div id="overview">
            <Overview overview={courseDetails.overview} />
          </div>
          <div id="programmes">
          <Programmes
              programmes={courseDetails.programmes || []}
              outcomes={courseDetails.programmesAndOutcomes} // Pass the new data
            />
          </div>
          <div id="value-added-courses">
            {/* Pass the fetched data as props */}
            <ValueAddedCourses courses={courseDetails?.valueAddedCourses || []} />
          </div>
          <div id="e-content">
            {/* Pass the fetched data as props */}
            <EContent title="E-Content" content={courseDetails?.eContent || []} />
          </div>
          <div id="faculty">
            <FacultySection faculty={courseDetails.faculty || []} />
          </div>
          <div id="department-activities">
            {/* Pass the fetched data as props */}
            <DepartmentActivities activities={courseDetails.departmentActivities || []} />
          </div>
          <div id="infrastructure">
            <Infrastructure />
          </div>
          <div id="our-recruiters">
            <OurRecruiters recruiters={courseDetails.recruitersImage} />
          </div>

          {/* CTA Section */}
          <section className="bg-white py-10 px-4 mt-8"> {/* Added margin-top */}
            <div className="container max-w-7xl mx-auto">
              <CTAComponent />
            </div>
          </section>
        </main>
      </div>
      <Footer />
    </>
  );
};

export default CoursePageClient;
