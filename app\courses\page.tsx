"use client";

import Navbar from "@/components/Navbar";
import Link from "next/link";
import Footer from "../sections/Footer";
import Courses from "../sections/courses/Courses";

const page = () => {
  return (
    <>
      <Navbar fixed={false} border={false} />
      <header
        className="relative mt-4 px-4 py-8 md:py-16 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex flex-col items-center text-center gap-4 md:flex-row md:justify-between md:items-center md:text-left md:gap-0"> {/* Added mobile styles, adjusted md styles */}
          <h1 className="font-ramilas text-3xl lg:text-base">Courses</h1> {/* Adjusted heading size for consistency */}
          <ul className="font-poppins flex flex-row justify-center gap-8 md:gap-8 md:pr-16"> {/* Changed mobile gap from gap-4 to gap-6 */}
            <li className="list-disc">
              <Link href="/">Home</Link>
            </li>
            <li className="list-disc">Courses</li>
          </ul>
        </div>
      </header>

      <Courses />

      <Footer />
    </>
  );
};

export default page;
