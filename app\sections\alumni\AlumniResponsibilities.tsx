// app/sections/alumni-showcase/AlumniResponsibilities.tsx
import React from 'react';
import Image from 'next/image';

interface ResponsibilityItemProps {
  iconSrc: string;
  iconAlt: string;
  text: string;
  bgColorClass: string; // For the icon background, e.g., 'bg-blue-200'
}

const ResponsibilityItem: React.FC<ResponsibilityItemProps> = ({ iconSrc, iconAlt, text, bgColorClass }) => {
  return (
    <div className="flex items-center space-x-4">
      <div className={`p-4 rounded-full ${bgColorClass}`}> {/* Increased padding for larger icon */}
        <Image src={iconSrc} alt={iconAlt} width={40} height={40} /> {/* Increased icon size */}
      </div>
      <div className="bg-white px-8 py-4 rounded-full shadow-md flex-grow"> {/* Increased padding for larger text */}
        <p className="text-green-800 font-semibold text-xl">{text}</p> {/* Increased text size */}
      </div>
    </div>
  );
};

const AlumniResponsibilities = () => {
  const responsibilities = [
    {
      iconSrc: "/alumni/scholoricon.png", // Placeholder - replace with actual icon path
      iconAlt: "Graduation Cap Icon",
      text: "Our Responsibilities",
      bgColorClass: "bg-cyan-200"
    },
    {
      iconSrc: "/alumni/helpicon.png", // Placeholder - replace with actual icon path
      iconAlt: "People Icon",
      text: "Help Current Students",
      bgColorClass: "bg-yellow-200"
    },
    {
      iconSrc: "/alumni/uniicon.png", // Placeholder - replace with actual icon path
      iconAlt: "Bank Icon",
      text: "Help Our University",
      bgColorClass: "bg-purple-200"
    },
    {
      iconSrc: "/alumni/helpicon.png", // Placeholder - replace with actual icon path
      iconAlt: "Community Icon",
      text: "Build Our Community",
      bgColorClass: "bg-blue-300"
    },
  ];

  return (
    <section 
      className="relative py-12 md:py-20 px-4 bg-cover bg-center bg-green-50" // Added 'relative'
      style={{ backgroundImage: "url('https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/jmcalumni.jpg')" }} // Placeholder background, replace as needed
    >
      <div className="absolute bottom-0 left-0 right-0 h-full bg-custom-green opacity-80 backdrop-blur-sm"></div> {/* Overlay for partial bottom blur effect */}
      <div className="container mx-auto relative z-10">
        <h2 className="text-3xl md:text-4xl font-ramilas font-semibold text-white text-center mb-10 md:mb-16">
          Our Responsibilities
        </h2>
        <div className="grid md:grid-cols-2 gap-x-12 gap-y-8 max-w-4xl mx-auto">
          {responsibilities.map((item, index) => (
            <ResponsibilityItem 
              key={index} 
              iconSrc={item.iconSrc} 
              iconAlt={item.iconAlt} 
              text={item.text} 
              bgColorClass={item.bgColorClass}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default AlumniResponsibilities;