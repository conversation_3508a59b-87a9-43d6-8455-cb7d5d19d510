"use client"
// app/sections/alumni-showcase/AlumniHeader.tsx
import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Menu } from 'lucide-react';
import AlumniMobileNav from './AlumniMobileNav';

const AlumniHeader = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const openMobileMenu = () => setIsMobileMenuOpen(true);
  const closeMobileMenu = () => setIsMobileMenuOpen(false);

  return (
    <header className="bg-white shadow-md sticky top-0 z-30">
      <div className="container mx-auto px-4 py-3 flex justify-between items-center">
        {/* Logo and Title */}
        <div className="flex items-center space-x-2 sm:space-x-3 md:flex-1">
          <Link href="/alumni-showcase/home" legacyBehavior>
            <a>
              <Image src="/alumni/alumnilogo.png" alt="JMC Alumni Association Logo" width={50} height={50} className="sm:w-[60px] sm:h-[60px]" />
            </a>
          </Link>
          <div className="text-center md:flex-grow">
            <h1 className="text-lg sm:text-xl md:text-2xl font-bold">
              <span style={{ color: '#50B64E' }}>Jmc</span> <span style={{ color: '#3C60C9' }}>Alumni Association</span>
            </h1>
            <hr className="my-1 border-black" />
            <p className="text-xs sm:text-sm text-black">Jamal Mohamed College (Autonomous) - Tiruchirappalli</p>
          </div>
          {/* Secondary logo, hidden on very small screens, shown on sm and up. Consider if needed with mobile nav taking space. */}
          <Image src="/jmc_logo 2.svg" alt="JMC Logo 2" width={50} height={50} className="hidden sm:block sm:w-[60px] sm:h-[60px]" />
        </div>

        {/* Desktop Navigation Links - Hidden on small screens (md and below) */}
        <nav className="hidden lg:flex items-center space-x-2">
          {['Home', 'About', 'Chapters', 'Awareness', 'Service', 'Gallery'].map((item) => (
            <Link key={item} href={`/alumni-showcase/${item.toLowerCase().replace(' ', '-')}`} legacyBehavior>
              {/* Adjusted padding and text size for desktop nav links */}
              <a className="px-3 py-2 text-xs xl:px-4 xl:text-sm text-gray-700 hover:bg-gray-100 rounded-full border border-gray-300 transition-colors duration-150">
                {item}
              </a>
            </Link>
          ))}
          <Link href="/alumni-showcase/prominent-alumni" legacyBehavior>
            <a className="px-4 py-2 text-xs xl:px-6 xl:text-sm text-white bg-green-700 hover:bg-green-800 rounded-full font-semibold transition-colors duration-150">
              Prominent Alumni
            </a>
          </Link>
        </nav>

        {/* Burger Menu Icon - Visible on small screens (up to lg) */}
        <div className="lg:hidden">
          <button
            onClick={openMobileMenu}
            aria-label="Open navigation menu"
            className="text-gray-600 hover:text-gray-800 p-2 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-gray-500 rounded-md"
          >
            <Menu size={28} />
          </button>
        </div>
      </div>

      {/* Mobile Navigation Component */}
      <AlumniMobileNav isOpen={isMobileMenuOpen} onClose={closeMobileMenu} />
    </header>
  );
};

export default AlumniHeader;
