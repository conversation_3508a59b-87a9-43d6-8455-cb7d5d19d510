"use client";

import { aboutCivilServices, civilServicesMissions } from "@/app/helper/helper";

const CivilServicesCoachingCentre = () => {
  const civilServicesTeam = [
    {
      sno: 1,
      name: "Dr. <PERSON>",
      designation: "Co-ordinator",
    },
    {
      sno: 2,
      name: "<PERSON><PERSON><PERSON> <PERSON>",
      designation: "UPSC Trainer",
    },
    {
      sno: 3,
      name: "Prof. <PERSON>",
      designation: "English Language Trainer",
    },
    {
      sno: 4,
      name: "<PERSON><PERSON>",
      designation: "UPSC Trainer",
    },
    {
      sno: 5,
      name: [
        "Dr. <PERSON><PERSON> <PERSON>",
        "Dr. <PERSON><PERSON> <PERSON><PERSON>",
        "Dr. <PERSON><PERSON>",
        "Dr. <PERSON>",
        "Mr. P. A<PERSON>",
      ],
      designation: "Core Team Civil Services",
    },
  ];

  return (
    <section className="bg-white pb-10 px-4">
      <div className="container max-w-7xl mx-auto space-y-6">
        <div className="rounded-2xl border border-gray-200 p-4 md:p-10 lg:px-16 lg:py-10 shadow-sm bg-white space-y-6">
          <h2 className="text-custom-green text-center">
            Civil Services Examination Study Centre
          </h2>
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              About Our Civil Services Examination Study Centre
            </h3>
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              {aboutCivilServices?.map((rule: any, idx: any) => (
                <li key={idx}>{rule}</li>
              ))}
            </ul>
          </div>

          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              Vision
            </h3>
            <p className="text-custom-new-green text-justify">
              The centre envisions creating young administrators, law abiding
              officers who will be at the forefront of our country in meeting
              the challenges and to shoulder the responsibility of executing
              schemes, maintaining law and order, formulate, enact and implement
              initiatives for the overall betterment of the society. It is our
              ordent belief that the destiny of this nation will be shaped by
              young men and women who not only possess intellectual vigour and
              administrative brilliance, but also hold values like honesty and
              integrity dearer to their heart. We are sure that the Civil
              Services Centre will dedicate itself to this noble task of
              training architects and builders of Modern India.
            </p>
          </div>
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              Mission
            </h3>
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              {civilServicesMissions?.map((rule: any, idx: any) => (
                <li key={idx}>{rule}</li>
              ))}
            </ul>
          </div>
          <div className="overflow-x-auto flex justify-center pb-8">
            <table className="w-full max-w-4xl mx-auto border-collapse rounded-2xl overflow-hidden font-ramilas font-bold relative border border-custom-green bg-custom-light-green md:shadow-milestone">
              <thead>
                <tr className="bg-custom-green text-white rounded-t-2xl">
                  <th className="px-6 md:px-8 py-4 text-left">S.No</th>
                  <th className="px-6 md:px-8 py-4 text-left">Name</th>
                  <th className="px-6 md:px-8 py-4 text-left">Designation</th>
                </tr>
              </thead>
              <tbody className="text-custom-green">
                {civilServicesTeam.map((member: any, idx: number) => (
                  <tr
                    key={idx}
                    className={
                      idx !== civilServicesTeam.length - 1
                        ? "border-b border-gray-300"
                        : ""
                    }
                  >
                    <td className="px-6 md:px-8 py-4 align-top">
                      {member.sno}.
                    </td>
                    <td className="px-6 md:px-8 py-4 whitespace-pre-line">
                      {Array.isArray(member.name)
                        ? member.name
                            .map((n: string, i: number) => `${i + 1}. ${n}`)
                            .join("\n")
                        : member.name}
                    </td>
                    <td className="px-6 md:px-8 py-4">{member.designation}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CivilServicesCoachingCentre;
