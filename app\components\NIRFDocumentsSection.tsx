import React from "react";

interface DocumentCard {
  type: "report" | "pcs" | "certificate";
  title: string;
  year: string;
  buttonText?: string;
  link?: string;
}

interface DocumentRow {
  year: string;
  title: string;
  documents: DocumentCard[];
}

interface NIRFDocumentsSectionProps {
  title?: string;
  documentRows: DocumentRow[];
}

const DocumentCard: React.FC<{ document: DocumentCard }> = ({ document }) => {
  const handleClick = () => {
    if (document.link) {
      window.open(document.link, '_blank');
    }
  };

  if (document.type === "report") {
    return (
      <div
        className="bg-custom-green rounded-2xl p-4 md:p-6 flex flex-col justify-between h-64 md:h-96 cursor-pointer hover:bg-[#004D00] transition-colors duration-300"
        onClick={handleClick}
      >
        <div className="flex-1 flex flex-col justify-center items-center text-center text-white">
          <h3 className="text-xl sm:text-2xl md:text-3xl font-bold mb-3">{document.title}</h3>
          {/* <p className="text-base sm:text-lg opacity-90">{document.year}</p> */}
        </div>
        {document.buttonText && (
          <button className="w-full bg-white text-custom-green py-2 md:py-4 md:px-6 rounded-xl text-sm md:text-xl font-medium hover:bg-gray-100 transition-colors duration-300">
            {document.buttonText}
          </button>
        )}
      </div>
    );
  }

  return (
    <div
      className="bg-white border-2 border-custom-green rounded-2xl p-6 flex items-center justify-center h-64 md:h-96 cursor-pointer hover:bg-gray-50 transition-colors duration-300"
      onClick={handleClick}
    >
      <h3 className="text-custom-green text-xl sm:text-2xl md:text-3xl font-semibold text-center">
        {document.title}
      </h3>
    </div>
  );
};

const NIRFDocumentsSection: React.FC<NIRFDocumentsSectionProps> = ({
  title = "NIRF Documents",
  documentRows
}) => {
  return (
    <section className="py-8 sm:py-12 md:py-16 bg-white">
      <div className="container max-w-7xl mx-auto px-4 sm:px-6">
        {/* Document Rows */}
        <div className="space-y-8 sm:space-y-12">
          {documentRows.map((row, rowIndex) => (
            <div key={rowIndex} className="space-y-4">
              {/* Row Title */}
              <h3 className="text-xl sm:text-2xl md:text-3xl font-semibold text-center text-[#002E00] mb-6">
                {row.title}
              </h3>

              {/* Document Cards Grid */}
              <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 md:gap-16 max-w-7xl mx-auto">
                {row.documents.map((document, docIndex) => (
                  <DocumentCard key={docIndex} document={document} />
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default NIRFDocumentsSection;
