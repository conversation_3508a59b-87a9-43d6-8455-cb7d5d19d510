import React from 'react'; // Import React
import { defineType, defineField } from "sanity";

export default defineType({
  name: "event",
  title: "Event",
  type: "document",
  fields: [
    defineField({
      name: "title",
      title: "Title",
      type: "string",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "slug",
      title: "Slug",
      type: "slug",
      options: {
        source: "title",
        maxLength: 96,
        slugify: (input: string) =>
          input
            .toLowerCase()
            .replace(/\s+/g, "-")
            .replace(/[^\w\-]+/g, "")
            .slice(0, 96),
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "date",
      title: "Date",
      type: "date",
      options: {
        dateFormat: "YYYY-MM-DD",
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "description",
      title: "Description",
      type: "text",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "imageUrl",
      title: "Image URL (from S3)",
      type: "url",
      description: "Paste the full URL of the image from your S3 bucket.",
      validation: (Rule) => Rule.required().uri({
        scheme: ['http', 'https']
      })
    }),
    defineField({
      name: "imageAlt",
      title: "Image Alt Text",
      type: "string",
      description: "Important for SEO and accessibility.",
      validation: (Rule) => Rule.required(),
    }),
  ],
  preview: {
    select: {
      title: 'title',
      date: 'date',
      imageUrl: 'imageUrl',
      imageAlt: 'imageAlt' // Also select alt text
    },
    prepare(selection) {
      const { title, date, imageUrl, imageAlt } = selection;
      return {
        title: title,
        subtitle: date ? new Date(date).toLocaleDateString() : 'No date set',
        // Use React.createElement instead of JSX for the media preview
        media: imageUrl 
          ? React.createElement('img', { src: imageUrl, alt: imageAlt || title }) 
          : undefined
      }
    }
  }
});