"use client";

import Image from "next/image";

const StudentCounsellingCentre = () => {
  const images = [
    "/students-facilities/counselling_1.jpg",
    "https://jmc.edu/images/facilities/student-counselling-2.jpg",
    "https://jmc.edu/images/facilities/student-counselling-3.jpg",
  ];

  const members = [
    "Dr. <PERSON><PERSON>",
    "Dr. <PERSON><PERSON><PERSON><PERSON>",
    "Dr. <PERSON><PERSON>",
    "Dr<PERSON> <PERSON><PERSON>",
    "Ms. <PERSON><PERSON> Nelofer",
    "<PERSON>. <PERSON><PERSON>",
  ];
  return (
    <section className="bg-white pb-10 px-4" id="students-counselling-centre">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green text-center">
          Student's Counselling Centre / Student's Service Centre (UGC)
        </h2>
        <div className="max-w-5xl mx-auto grid lg:grid-cols-2 gap-6 items-start">
          {/* Left: Images */}
          <div className="space-y-4">
            {images.map((img, idx) => (
              <div
                key={idx}
                className="relative w-full h-[200px] rounded-xl overflow-hidden shadow-md"
              >
                <Image
                  src={img}
                  alt={`Counselling ${idx + 1}`}
                  fill
                  className="w-full object-cover"
                />
              </div>
            ))}
          </div>

          {/* Right: Members */}
          <div className="bg-white border border-gray-200 p-6 rounded-xl flex items-center justify-center h-full">
            <div className="text-center">
              <h3 className="text-left text-lg font-semibold text-custom-green mb-2 font-poppins">
                Members:
              </h3>
              <ol className="font-poppins pl-2 list-decimal list-inside text-custom-new-green space-y-1 text-left inline-block">
                {members.map((member, idx) => (
                  <li key={idx}>{member}</li>
                ))}
              </ol>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default StudentCounsellingCentre;
