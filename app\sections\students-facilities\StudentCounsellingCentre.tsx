"use client";

import Image from "next/image";
import { useEffect, useState } from "react";
import { client } from "@/lib/sanity";
import { JMC_STUDENT_FACILITIES_QUERY } from "@/sanity/lib/queries";
import ImageSkeleton from "@/app/components/ImageSkeleton";

interface FacilityImage {
  name: string;
  imageUrl: string;
}

interface JmcStudentFacilitiesData {
  _id: string;
  studentCounsellingImages: FacilityImage[];
  cafeteriaImages: FacilityImage[];
  dayCareCentreImages: FacilityImage[];
  transportImages: FacilityImage[];
  communicationLabImages: FacilityImage[];
}

const StudentCounsellingCentre = () => {
  const [counsellingImages, setCounsellingImages] = useState<FacilityImage[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    client.fetch(JMC_STUDENT_FACILITIES_QUERY).then((data: JmcStudentFacilitiesData[]) => {
      if (data.length > 0 && data[0].studentCounsellingImages?.length > 0) {
        // Filter out images with empty URLs
        const validImages = data[0].studentCounsellingImages.filter(img =>
          img.imageUrl && img.imageUrl.trim() !== ""
        );
        setCounsellingImages(validImages);
      }
      setIsLoading(false);
    }).catch(() => {
      setIsLoading(false);
    });
  }, []);

  const members = [
    "Dr. A. Syed Zakir Hasan",
    "Dr. B.S. Shayin Sha",
    "Dr. T. Selvaraju",
    "Dr. K. Sheik Fareeth",
    "Ms. M. Nelofer",
    "Ms. I. Rokhiya Begam",
  ];
  return (
    <section className="bg-white pb-10 px-4" id="students-counselling-centre">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green text-center">
          Student's Counselling Centre / Student's Service Centre (UGC)
        </h2>
        <div className="max-w-5xl mx-auto grid lg:grid-cols-2 gap-6 items-start">
          {/* Left: Images */}
          <div className="space-y-4">
            {isLoading ? (
              <div className="space-y-4">
                {Array.from({ length: 3 }).map((_, idx) => (
                  <div
                    key={idx}
                    className="w-full h-[200px] rounded-xl overflow-hidden shadow-md bg-gray-200 animate-pulse"
                  >
                    <div className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-shimmer"></div>
                  </div>
                ))}
              </div>
            ) : counsellingImages.length > 0 ? (
              counsellingImages.map((img, idx) => (
                <div
                  key={idx}
                  className="relative w-full h-[200px] rounded-xl overflow-hidden shadow-md"
                >
                  <Image
                    src={img.imageUrl}
                    alt={img.name}
                    fill
                    className="w-full object-cover"
                  />
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-8">
                <p>No counselling centre images available at the moment.</p>
              </div>
            )}
          </div>

          {/* Right: Members */}
          <div className="bg-white border border-gray-200 p-6 rounded-xl flex items-center justify-center h-full">
            <div className="text-center">
              <h3 className="text-left text-lg font-semibold text-custom-green mb-2 font-poppins">
                Members:
              </h3>
              <ol className="font-poppins pl-2 list-decimal list-inside text-custom-new-green space-y-1 text-left inline-block">
                {members.map((member, idx) => (
                  <li key={idx}>{member}</li>
                ))}
              </ol>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default StudentCounsellingCentre;
