"use client";

import Image from "next/image";
import { useEffect, useState } from "react";
import { client } from "@/lib/sanity";
import { WOMENS_HOSTEL_QUERY } from "@/sanity/lib/queries";

interface WomensHostelData {
  _id: string;
  name?: string;
  description?: string;
  imageUrl?: string;
}

const TheWomenHostel = () => {
  const [hostelData, setHostelData] = useState<WomensHostelData | null>(null);

  useEffect(() => {
    client.fetch(WOMENS_HOSTEL_QUERY).then((data) => {
      console.log("Fetched womens hostel data:", data);
      // Get the first item from the array, or use null if no data
      setHostelData(data.length > 0 ? data[0] : null);
    });
  }, []);

  // Fallback values if no data from Sanity
  const title = hostelData?.name || "The Women's Hostel";
  const description = hostelData?.description ||
    "The College is running an exclusive hostel for women students which is situated inside the college premises with all infrastructural facilities.";
  const imageUrl = hostelData?.imageUrl || "";

  return (
    <section className="bg-white pb-10 px-4">
      <div className="container max-w-7xl mx-auto space-y-4">
        <h2 className="text-custom-green text-center">{title}</h2>
        <p className="text-custom-new-green text-center">
          {description}
        </p>
        <div className="relative mx-auto w-full max-w-[800px] aspect-[800/533] rounded-2xl shadow-md overflow-hidden">
          <Image
            src={imageUrl}
            alt={title}
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-[#002E00]/30" />
        </div>
      </div>
    </section>
  );
};

export default TheWomenHostel;
