'use client';

import React, { useState, useRef, useEffect } from "react";
import Link from 'next/link';
import { getNoticeUpdates, NoticeUpdate } from "../../sanity/lib/getNoticeUpdates";

const NoticeAndUpdates = () => {
  const [selectedIndex, setSelectedIndex] = useState(6);
  const contentRef = useRef<HTMLDivElement>(null);
  const [scrollPosition, setScrollPosition] = useState(0);
  const [contentHeight, setContentHeight] = useState(0);
  const [viewportHeight, setViewportHeight] = useState(0);
  const [allUpdates, setAllUpdates] = useState<NoticeUpdate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const tabNames = [
    "Circular",
    "News",
    "Downloads",
    "For Students",
    "For College Faculty",
    "Digital Initiative",
    "View All",
  ];

  // Add a 'category' field matching tabNames
  const updates = [
    {
      id: 1,
      category: "News", // Example category
      image:
        "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=800&auto=format&fit=crop&q=60",
      text: "More resilient world by exploring practical applications for novel quantum sensors.",
    },
    {
      id: 2,
      category: "Circular", // Example category
      image:
        "https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=800&auto=format&fit=crop&q=60",
      text: "Are building a stronger and more resilient world by exploring practica",
    },
    {
      id: 3,
      category: "For Students", // Example category
      image:
        "https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=800&auto=format&fit=crop&q=60",
      text: "By exploring practical applications for novel quantum sensors.",
    },
    {
      id: 4,
      category: "Downloads", // Example category
      image:
        "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=800&auto=format&fit=crop&q=60",
      text: "More sensors and building a stronger and more resilient world by exploring",
    },
    {
      id: 5,
      category: "News", // Example category
      image:
        "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=800&auto=format&fit=crop&q=60",
      text: "Practical applications for advanced quantum computing and sensor technology.",
    },
  ];

  const handleKeyDown = (event: any) => {
    if (event.key === "ArrowRight") {
      setSelectedIndex((prevIndex) => (prevIndex + 1) % tabNames.length);
    } else if (event.key === "ArrowLeft") {
      setSelectedIndex((prevIndex) =>
        prevIndex === 0 ? tabNames.length - 1 : prevIndex - 1
      );
    }
  };
  // Handle scroll event to update custom scrollbar position
  const handleScroll = () => {
    if (contentRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = contentRef.current;
      setScrollPosition(scrollTop / (scrollHeight - clientHeight));
      setContentHeight(scrollHeight);
      setViewportHeight(clientHeight);
    }
  };

  // Calculate scrollbar thumb height and position
  const getThumbHeight = () => {
    if (contentHeight <= viewportHeight) return '50%'; // Reduced from 100% to 50%
    const height = (viewportHeight / contentHeight) * 30; // Reduced factor from 100 to 60
    return `${Math.max(height, 10)}%`; // Reduced minimum size from 15% to 10%
  };

  const getThumbPosition = () => {
    // Calculate available space for the thumb to move
    const thumbHeight = parseFloat(getThumbHeight()) / 100 * viewportHeight;
    const availableSpace = viewportHeight - thumbHeight;

    // Return position based on scroll percentage, with a max limit to prevent overflow
    return Math.min(scrollPosition * availableSpace, viewportHeight - thumbHeight);
  };

  // Initialize measurements on component mount
  useEffect(() => {
    if (contentRef.current) {
      setContentHeight(contentRef.current.scrollHeight);
      setViewportHeight(contentRef.current.clientHeight);
    }
  }, [updates]);

  // --- Filtering Logic (uses allUpdates now) --- 
  const selectedTabName = tabNames[selectedIndex];
  const filteredUpdates = selectedTabName === "View All"
    ? allUpdates
    : allUpdates.filter(update => update.category === selectedTabName);
    
  // --- Effect to scroll to top on tab change --- 
  useEffect(() => {
    if (contentRef.current) {
      contentRef.current.scrollTop = 0;
      // Recalculate scrollbar position after content potentially changes height
      handleScroll();
    }
  }, [selectedIndex]); // Run when selectedIndex changes

  // --- Fetch data using useEffect --- 
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const data = await getNoticeUpdates();
        setAllUpdates(data);
      } catch (err) {
        console.error(err);
        setError("Failed to load updates.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);
  return (
    <>
      <section className="px-4 pt-8 pb-4 bg-custom-light-green">
        <div className="container mx-auto max-w-7xl">
          <h3 className="font-medium text-center text-custom-green mb-6">
            Notice & Updates
          </h3>
          <p className="text-custom-green font-poppins leading-relaxed mb-8 text-center">
            Stay informed with the latest announcements and important updates from Jamal Mohamed College. All notices regarding academic schedules, events, and other key information will be regularly updated here, ensuring that students, faculty, and staff are always up-to-date with the latest developments.
          </p>
        </div>
      </section>
      <section className=" py-8 bg-white">
        {/* <div className="container mx-auto max-w-7xl"> */}
        <div
          className="px-8 py-4 flex overflow-x-auto pb-2 md:flex md:justify-between lg:justify-between gap-4 sm:gap-4 md:gap-1 text-center scrollbar-hide"
          tabIndex={0}
          onKeyDown={handleKeyDown}
        >
          {tabNames.map((tab, index) => (
            <button
              key={index}
              tabIndex={index === selectedIndex ? 0 : -1}
              className={`flex-shrink-0 px-4 md:px-5 lg:px-8 py-2 text-base md:text-lg font-semibold rounded-full border-2 border-custom-green transition duration-300 whitespace-nowrap mx-0.5 min-w-max ${index === selectedIndex
                ? "bg-custom-green text-white"
                : "bg-white text-custom-green hover:bg-custom-green hover:text-white"
                }`}
              onClick={() => setSelectedIndex(index)}
            >
              {tab}
            </button>
          ))}
        </div>
        <div className="px-8">
          {/* Updates List */} 
          <div className="max-h-[400px] mt-8 border-2 border-custom-green rounded-2xl flex">
            {/* Content Column */} 
            <div
              ref={contentRef}
              className="flex-1 overflow-y-auto scrollbar-hide"
              onScroll={handleScroll}
            >
              <div className="space-y-3 p-3">
                {isLoading ? (
                  <div className="p-4 text-center text-gray-500">Loading updates...</div>
                ) : error ? (
                  <div className="p-4 text-center text-red-500">{error}</div>
                ) : filteredUpdates.length > 0 ? (
                  filteredUpdates.map((update) => (
                    <div
                      key={update._id} // Use _id from Sanity as key
                      className="flex items-center space-x-4 p-3 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer border border-gray-100"
                    >
                      {update.imageUrl && (
                         <div className="flex-shrink-0">
                           <img
                             src={update.imageUrl} // Use imageUrl from Sanity
                             alt={update.imageAlt || ''} // Use imageAlt from Sanity
                             className="w-16 h-16 object-cover rounded-md"
                           />
                         </div>
                      )}
                      {/* Removed line-clamp-2 from the paragraph below */}
                      <p className="text-custom-new-green font-poppins text-sm md:text-lg flex-1">
                        {update.text}
                      </p>
                    </div>
                  ))
                ) : (
                  <div className="p-4 text-center text-gray-500">No updates found for this category.</div>
                )}
              </div>
            </div>

            {/* ... Divider Line and Custom Scrollbar (conditionally rendered) ... */}
            {/* Conditionally render scrollbar based on actual content height vs viewport */}
            {!isLoading && !error && contentHeight > viewportHeight && (
              <>
                <div className="w-0.5 bg-custom-green"></div>
                <div className="w-10 py-2 mb-14 flex items-center relative">
                  <div className="w-2 h-full rounded-full mx-auto">
                    <div
                      className="w-6 bg-custom-green rounded-full absolute left-2 transition-all duration-150"
                      style={{
                        height: getThumbHeight(),
                        transform: `translateY(${getThumbPosition()}px)`
                      }}
                    ></div>
                  </div>
                </div>
              </>
            )}
          </div>


          {/* Banner with Gradient Background */}
          <div className="w-full mt-8 py-16 relative rounded-xl overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-[#002E00] to-[#004D00] z-0"></div>
            <div className="absolute inset-0 bg-cover bg-left z-10" style={{ backgroundImage: 'url("/homepage/contentbgpattern.png")' }}></div>
            <div className="relative z-20 px-4">
              <div className="text-center">
                <h2 className="text-2xl md:text-3xl font-bold mb-2 text-white">Make your career with JMC content now</h2>
                <p className="text-sm md:text-base mb-6 text-white">more resilient world by exploring</p>
                <Link href="/contactUs">
                  <button className="bg-white text-[#004D00] hover:bg-opacity-90 transition-all px-6 py-2 rounded-full font-medium text-sm md:text-base">
                    Contact Us
                  </button>
                </Link>
              </div>
            </div>
          </div>
        </div>
        {/* </div> */}
      </section>
    </>
  );
};

export default NoticeAndUpdates;
