import React from "react";

// Interface for UGC Minor Research Project
export interface UgcMinorProject {
  serialNo: number;
  particulars: string;
  amount: string;
}

// Interface for TNSCST Student Project
export interface TnscstStudentProject {
  serialNo: number;
  guideName: string;
  department?: string;
  projectTitle?: string;
  studentNames: string[];
  amount: string;
}

// Interface for table section
export interface ProjectTableSection {
  id: string;
  title: string;
  type: 'ugc' | 'tnscst';
  year: string;
  data: UgcMinorProject[] | TnscstStudentProject[];
}

interface ProjectsYearlyTableProps {
  sections: ProjectTableSection[];
}

const ProjectsYearlyTable: React.FC<ProjectsYearlyTableProps> = ({
  sections = []
}) => {
  return (
    <div className="">
      {sections.map((section) => (
        <div key={section.id} className="">
          <h3 className="bg-[#D1D9D1] text-lg sm:text-xl font-semibold text-[#002E00] pt-6 sm:pt-8 px-4 sm:px-8">
            {section.title}
          </h3>

          <div className="bg-[#D1D9D1]">
            <div className="rounded-lg overflow-hidden">
              {/* Add overflow container for mobile responsiveness */}
              <div className="overflow-x-auto">
                <div className="min-w-[768px]"> {/* Minimum width to prevent squishing on mobile */}
                  {/* Header with margin */}
                  <div className="mx-4 my-4">
                    <div className="bg-[#002E00] text-white rounded-full">
                      {section.type === 'ugc' ? (
                        <div className="grid grid-cols-12 w-full">
                          <div className="py-3 sm:py-4 px-3 sm:px-6 text-center font-medium col-span-1 text-xs sm:text-sm md:text-base">S. NO.</div>
                          <div className="py-3 sm:py-4 px-3 sm:px-6 text-left font-medium col-span-8 text-xs sm:text-sm md:text-base whitespace-normal break-words">PARTICULARS</div>
                          <div className="py-3 sm:py-4 px-3 sm:px-6 text-center font-medium col-span-3 text-xs sm:text-sm md:text-base">AMOUNT</div>
                        </div>
                      ) : (
                        <div className="grid grid-cols-12 w-full">
                          <div className="py-3 sm:py-4 px-3 sm:px-6 text-center font-medium col-span-1 text-xs sm:text-sm md:text-base">S. NO.</div>
                          <div className="py-3 sm:py-4 px-3 sm:px-6 text-left font-medium col-span-8 text-xs sm:text-sm md:text-base whitespace-normal break-words">PARTICULARS</div>
                          <div className="py-3 sm:py-4 px-3 sm:px-6 text-center font-medium col-span-3 text-xs sm:text-sm md:text-base">SANCTIONED AMOUNT</div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Table content */}
                  <table className="min-w-full">
                    <tbody>
                      {section.type === 'ugc' ? (
                        // UGC Minor Research Projects
                        (section.data as UgcMinorProject[]).map((project) => (
                          <tr
                            key={`ugc-${section.id}-${project.serialNo}`}
                            className="border-b border-[#555555] "
                          >
                            <td className="py-2 sm:py-3 px-3 sm:px-6 text-center text-[#555555] w-[8.33%] text-xs sm:text-sm md:text-base">{project.serialNo}</td>
                            <td className="py-2 sm:py-3 px-3 sm:px-6 text-left text-[#555555] w-[66.67%] text-xs sm:text-sm md:text-base whitespace-normal break-words">{project.particulars}</td>
                            <td className="py-2 sm:py-3 px-3 sm:px-6 text-center text-[#555555] w-[25%] text-xs sm:text-sm md:text-base">{project.amount}</td>
                          </tr>
                        ))
                      ) : (
                        // TNSCST Student Projects
                        (section.data as TnscstStudentProject[]).map((project) => (
                          <tr
                            key={`tnscst-${section.id}-${project.serialNo}`}
                            className="border-b border-[#555555] "
                          >
                            <td className="py-2 sm:py-3 px-3 sm:px-6 text-center text-[#555555] w-[8.33%] text-xs sm:text-sm md:text-base">{project.serialNo}</td>
                            <td className="py-2 sm:py-3 px-3 sm:px-6 text-left text-[#555555] w-[66.67%] text-xs sm:text-sm md:text-base whitespace-normal break-words">
                              <div className="mb-1">Guide Name: {project.guideName}</div>
                              {project.department && (
                                <div className="mb-1">{project.department}</div>
                              )}
                              {project.projectTitle && (
                                <div className="mb-1">Project Title: {project.projectTitle}</div>
                              )}
                              <div>
                                {project.studentNames.length > 1 ? 'Student Names: ' : 'Student Name: '}
                                {project.studentNames.length === 1 ? (
                                  project.studentNames[0]
                                ) : (
                                  project.studentNames.map((name, index) => (
                                    <React.Fragment key={index}>
                                      {name}
                                      {index < project.studentNames.length - 1 && ', '}
                                    </React.Fragment>
                                  ))
                                )}
                              </div>
                            </td>
                            <td className="py-2 sm:py-3 px-3 sm:px-6 text-center text-[#555555] w-[25%] text-xs sm:text-sm md:text-base">{project.amount}</td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProjectsYearlyTable;
