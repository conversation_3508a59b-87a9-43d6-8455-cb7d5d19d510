import React from "react";

// Define interfaces for our data structure
export interface AwardEntry {
  serialNo: number;
  year: string;
  category: string;
  facultyMember: string;
}

interface ResearchExcellenceAwardTableProps {
  awardEntries: AwardEntry[];
  title?: string;
}

const ResearchExcellenceAwardTable: React.FC<ResearchExcellenceAwardTableProps> = ({
  awardEntries,
  title = "JAMAL RESEARCH EXCELLENCE AWARD"
}) => {
  return (
    <div className="pt-6 sm:pt-8 bg-[#D1D9D1]">
      <h3 className="text-lg sm:text-xl font-bold text-[#002E00] mb-3 sm:mb-4 px-4 sm:px-8">
        {title}
      </h3>

      <div className="rounded-lg overflow-hidden">
        {/* Add overflow container for mobile responsiveness */}
        <div className="overflow-x-auto">
          <div className="min-w-[768px]"> {/* Minimum width to prevent squishing on mobile */}
            {/* Header with margin */}
            <div className="mx-4 my-4">
              <div className="bg-[#002E00] text-white rounded-full">
                <div className="grid grid-cols-12 w-full">
                  <div className="py-3 sm:py-4 px-3 sm:px-6 text-center font-medium col-span-1 text-xs sm:text-sm md:text-base">S.NO.</div>
                  <div className="py-3 sm:py-4 px-3 sm:px-6 text-center font-medium col-span-2 text-xs sm:text-sm md:text-base">YEAR</div>
                  <div className="py-3 sm:py-4 px-3 sm:px-6 text-center font-medium col-span-4 text-xs sm:text-sm md:text-base whitespace-normal break-words">CATEGORY</div>
                  <div className="py-3 sm:py-4 px-3 sm:px-6 text-center font-medium col-span-5 text-xs sm:text-sm md:text-base whitespace-normal break-words">NAME OF THE FACULTY MEMBER</div>
                </div>
              </div>
            </div>

            {/* Award Entries */}
            <table className="min-w-full">
              <tbody>
                {awardEntries.map((entry) => (
                  <tr
                    key={`award-${entry.serialNo}`}
                    className="border-b border-[#555555]"
                  >
                    <td className="py-2 sm:py-3 px-3 sm:px-6 text-center text-[#555555] w-[8.33%] text-xs sm:text-sm md:text-base">{entry.serialNo}</td>
                    <td className="py-2 sm:py-3 px-3 sm:px-6 text-center text-[#555555] w-[16.67%] text-xs sm:text-sm md:text-base">{entry.year}</td>
                    <td className="py-2 sm:py-3 px-3 sm:px-6 text-left text-[#555555] w-[33.33%] text-xs sm:text-sm md:text-base whitespace-normal break-words">{entry.category}</td>
                    <td className="py-2 sm:py-3 px-3 sm:px-6 text-left text-[#555555] w-[41.67%] text-xs sm:text-sm md:text-base whitespace-normal break-words">{entry.facultyMember}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResearchExcellenceAwardTable;
