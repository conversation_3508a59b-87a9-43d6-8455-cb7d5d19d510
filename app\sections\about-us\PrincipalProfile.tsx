"use client";

import Image from "next/image";
import { cn } from "@/lib/utils";
import { Download } from "lucide-react";

interface PrincipalProfileProps {
  className?: string;
}

const PrincipalProfile = ({ className }: PrincipalProfileProps) => {
  return (
    <section
      className={cn("bg-[rgba(0,46,0,0.03)] ", className)}
      id="principal-profile"
    >
      <div className="container mx-auto max-w-[2000px]">
        <div className="relative w-full flex flex-col lg:flex-row lg:items-end bg-custom-light-green rounded-b-2xl md:rounded-b-3xl rounded-tr-2xl md:rounded-tr-3xl border-b-2 border-custom-green shadow-stacked-cards">
          <div className="absolute inset-0 w-full h-full z-0">
            <Image
              src="/aboutus/aboutusbgpattern.png"
              alt="Background Pattern"
              fill
              className="object-cover"
              priority
            />
          </div>
          {/* Principal's Image Container */}
          <div className="rounded-bl-2xl md:rounded-bl-3xl relative w-[200px] md:w-80 h-[240px] md:h-[360px] flex items-end overflow-hidden mt-4 md:mt-6">
            <Image
              src="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/AboutUs/PrincipalProfile/01.png"
              alt="Dr. D. I. George Amalarethinam - Principal of Jamal Mohamed College"
              fill
              priority
              sizes="(max-width: 768px) 100vw, 320px"
              className="object-cover object-center"
            />
          </div>

          {/* Content Container */}
          <div className="relative w-[calc(100%-32px)] mx-4 md:mx-6 lg:mx-8 my-8 -mt-20 bg-white p-4 rounded-xl shadow-lg lg:ml-8 lg:absolute lg:left-64 lg:top-1/2 lg:-translate-y-1/2 lg:w-auto lg:max-w-6xl xl:max-w-[1950px] lg:mt-0">
            {/* Quote Icon */}
            <div className="absolute -top-4 md:-top-6 right-4 md:right-8 w-12 h-12 md:w-14 md:h-14">
              <Image
                src="/quote_icon.svg"
                alt="Decorative quote icon"
                fill
                className="object-contain"
              />
            </div>

            {/* Text Content */}
            <div className="space-y-2 p-4">
              <h2 className="text-custom-green font-ramilas font-medium text-3xl">
                Principal Profile
              </h2>
              <div className="text-custom-new-green space-y-2 text-sm">
                <p>
                  <strong className="font-semibold">
                    Dr. D. I. George Amalarethinam
                  </strong>{" "}
                  has been the Principal of Jamal Mohamed College since 2024.
                  With a Ph.D. from Bharathidasan University, he has served in
                  key academic and administrative roles since 1988. His
                  leadership was instrumental in securing an A++ grade (CGPA
                  3.69/4.0) in the Fourth Cycle of NAAC Accreditation. he has
                  served in key academic and administrative roles since 1988.
                  His leadership was instrumental in securing an A++ grade (CGPA
                  3.69/4.0) in the Fourth Cycle of NAAC.
                </p>
                <div className="mt-6">
                  <a
                    href="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/AboutUs/PrincipalProfile/princi-short-profile.pdf"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 bg-custom-green hover:bg-green-800 text-white px-6 py-2 rounded-full transition-colors font-poppins font-semibold"
                  >
                    View Profile
                    <Download className="w-5 h-5" />
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PrincipalProfile;
