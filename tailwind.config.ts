import type { Config } from "tailwindcss";

const config: Config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        poppins: ["Poppins", "system-ui", "sans-serif"],
        ubuntu: ["Ubuntu", "sans-serif"],
        ramilas: ["TT Ramilas", "sans-serif"],
      },
      fontSize: {
        // Fluid typography for paragraphs
        // Body Text (p) - text-base
        "fluid-base": [
          "clamp(1rem, 0.95rem + 0.5vw, 1.125rem)", // 16px to 18px
          { lineHeight: "1.625", fontWeight: "400" }, // leading-relaxed (1.625), font-normal
        ],
        "fluid-lg": [
          "clamp(1.125rem, 1.0875rem + 0.1875vw, 1.25rem)",
          { lineHeight: "1.75" },
        ],
        "fluid-xl": [
          "clamp(1rem, 1.2125rem + 0.1875vw, 1.375rem)",
          { lineHeight: "1.75" },
        ],

        // Fluid typography for headings
        // Main Title (h1) - text-5xl
        "fluid-h1": [
          "clamp(2.5rem, 2rem + 2vw, 3rem)", // 40px to 48px
          { lineHeight: "1.25", fontWeight: "700" }, // leading-tight (1.25), font-bold
        ],

        // Section Title (h2) - text-4xl
        "fluid-h2": [
          "clamp(1.875rem, 1.5rem + 1.25vw, 2.25rem)", // 30px to 36px
          { lineHeight: "1.5", fontWeight: "500" }, // leading-snug (1.375), font-semibold
        ],

        // Subsection Title (h3) - text-3xl
        "fluid-h3": [
          "clamp(1.25rem, 1.125rem + 1vw, 1.625rem)", // 20px to 26px
          { lineHeight: "1.5", fontWeight: "500" },
        ],

        // Subtitle (h4) - text-2xl
        "fluid-h4": [
          "clamp(1.5rem, 1.25rem + 1vw, 1.875rem)", // 24px to 30px
          { lineHeight: "1.5", fontWeight: "500" }, // leading-normal (1.5), font-medium
        ],

        // Minor Title (h5) - text-xl
        "fluid-h5": [
          "clamp(1.25rem, 1.125rem + 0.75vw, 1.5rem)", // 20px to 24px
          { lineHeight: "1.625", fontWeight: "600" }, // leading-relaxed (1.625), font-semibold
        ],

        // Small Text - text-sm
        "fluid-sm": [
          "clamp(0.875rem, 0.8rem + 0.25vw, 0.9375rem)", // 14px to 15px
          { lineHeight: "1.375", fontWeight: "300" }, // leading-snug (1.375), font-light
        ],
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      boxShadow: {
        "stacked-cards": `
        70px 22px 0px 0px #5F765F,
        140px 52px 0px 0px #002E00   
        `,
        "custom-glow": "0px 0px 34px 0px #00000026",
        "blur-glow": "0px 0px 15px 0px rgba(0, 0, 0, 0.4)",
        milestone: "10px 10px 0px 0px #5F765F, 20px 20px 0px 0px #002E00",
        custom: "0 0 15px rgba(0, 0, 0, 0.1)",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        "custom-green": "#002E00",
        "custom-new-green": "#565656",
        "custom-light-green": "#D1D9D1",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    require("tailwind-scrollbar"), // <-- Add this line
  ],
};
export default config;
