"use client";

import Image from "next/image";
import { useEffect, useState } from "react";
import { client } from "@/lib/sanity";
import { JMC_STUDENT_FACILITIES_QUERY } from "@/sanity/lib/queries";

interface FacilityImage {
  name: string;
  imageUrl: string;
}

interface JmcStudentFacilitiesData {
  _id: string;
  studentCounsellingImages: FacilityImage[];
  cafeteriaImages: FacilityImage[];
  dayCareCentreImages: FacilityImage[];
  transportImages: FacilityImage[];
  communicationLabImages: FacilityImage[];
}

const Cafeteria = () => {
  const [cafeteriaImages, setCafeteriaImages] = useState<FacilityImage[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    client.fetch(JMC_STUDENT_FACILITIES_QUERY).then((data: JmcStudentFacilitiesData[]) => {
      if (data.length > 0 && data[0].cafeteriaImages?.length > 0) {
        // Filter out images with empty URLs
        const validImages = data[0].cafeteriaImages.filter(img =>
          img.imageUrl && img.imageUrl.trim() !== ""
        );
        setCafeteriaImages(validImages);
      }
      setIsLoading(false);
    }).catch(() => {
      setIsLoading(false);
    });
  }, []);

  return (
    <section className="bg-white pb-10 px-4" id="cafeteria">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green text-center">Cafeteria</h2>
        {isLoading ? (
          <div className="flex flex-col md:flex-row gap-6">
            <div className="h-[200px] md:h-[350px] border-[3px] border-gray-200 flex-1 rounded-xl overflow-hidden shadow-md bg-gray-200 animate-pulse">
              <div className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-shimmer"></div>
            </div>
            <div className="h-[200px] md:h-[350px] border-[3px] border-gray-200 flex-1 rounded-xl overflow-hidden shadow-md bg-gray-200 animate-pulse">
              <div className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-shimmer"></div>
            </div>
          </div>
        ) : cafeteriaImages.length > 0 ? (
          <div className="flex flex-col md:flex-row gap-6">
            {cafeteriaImages.slice(0, 2).map((img, idx) => (
              <div key={idx} className="relative h-[200px] md:h-[350px] border-[3px] border-custom-green flex-1 rounded-xl overflow-hidden shadow-md">
                <Image
                  src={img.imageUrl}
                  alt={img.name}
                  width={500}
                  height={400}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-8">
            <p>No cafeteria images available at the moment.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default Cafeteria;
