"use client";

import Image from "next/image";

const Cafeteria = () => {
  return (
    <section className="bg-white pb-10 px-4" id="cafeteria">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green text-center">Cafeteria</h2>
        <div className="flex flex-col md:flex-row gap-6">
          <div className="relative h-[200px] md:h-[350px] border-[3px] border-custom-green flex-1 rounded-xl overflow-hidden shadow-md">
            <Image
              src="https://jmc.edu/images/facilities/cafeteria-1.jpg"
              alt="cafeteria image 1"
              width={500}
              height={400}
              className="w-full h-full object-cover"
            />
          </div>

          <div className="h-[200px] md:h-[350px] border-[3px] border-custom-green relative flex-1 rounded-xl overflow-hidden shadow-md">
            <Image
              src="https://jmc.edu/images/facilities/cafeteria-2.jpg"
              alt="cafeteria image 2"
              width={500}
              height={400}
              className="w-full h-full object-cover"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Cafeteria;
