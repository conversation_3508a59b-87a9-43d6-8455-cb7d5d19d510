"use client";

import Navbar from "@/components/Navbar";
import Footer from "../sections/Footer"; // Adjusted path relative to app/feesPaymentProcedure
import Link from "next/link";
import CTAComponent from "../components/CTAComponent"; // Adjusted path
// import { usePathname } from 'next/navigation'; // Not strictly needed if title is static
// import { menuItems } from "@/components/Navbar"; // Not strictly needed if title is static

const FeesPaymentProcedurePage = () => {
  return (
    <>
      <Navbar fixed={true} border={true} />
      <header
        className="relative pt-40 pb-16 px-4 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex flex-col items-center md:flex-row md:justify-between md:items-center">
          <h1 className="font-ramilas text-center md:text-left mb-4 md:mb-0 text-4xl md:text-5xl">
            Fees Payment Procedure
          </h1>
          <ul className="font-poppins flex flex-row justify-center md:justify-end gap-8 md:gap-8 md:pr-16">
            <li className="list-disc">
              <Link href="/">Home</Link>
            </li>
            <li className="list-disc">
              Fees Payment Procedure
            </li>
          </ul>
        </div>
      </header>

      <main className="flex-grow py-10 md:py-16 px-4 bg-gray-50">
        <div className="container max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-custom-green font-poppins">ONLINE FEE PAYMENT</h2>
          </div>

          <div className="bg-white p-6 md:p-10 rounded-lg shadow-xl max-w-4xl mx-auto border border-gray-300">
            <section className="mb-8">
              <h3 className="text-xl font-semibold text-gray-800 mb-4 font-poppins">ONLINE FEE PAYMENT PROCEDURE AND INSTRUCTIONS:</h3>
              <ul className="list-disc list-inside space-y-2 text-gray-700 font-poppins">
                <li>Online fee payment can be made by Net Banking / Credit Cards / Debit Cards/ UPI</li>
                <li>Enter the login ID provided from the college.</li>
                <li>Please verify the name, branch, semester and class of the student.</li>
                <li>Click on "<strong>PayOnline</strong>" to initiate online fee payment.</li>
                <li>Select the billing item to be paid and click the button "<strong>Make Payment</strong>"</li>
                <li>Select the mode of payment such as Net Banking / Credit Cards / Debit Cards/ UPI</li>
                <li>Please follow the instructions and enter the details as applicable to your choice of payment.</li>
                <li>Under Billing Details enter valid and communicable "<strong>Mobile Number</strong>" and "<strong>Email Id</strong>".</li>
                <li>Click "<strong>Pay Now</strong>" button to confirm the online fee payment.</li>
              </ul>
            </section>

            <div className="my-8 text-center">
              <Link href="https://jmcerp.in/onlinepayment/" legacyBehavior>
                <a className="inline-block bg-white text-custom-green font-semibold py-3 px-8 rounded-full hover:bg-custom-green hover:text-white border border-custom-green transition duration-300 font-poppins text-lg" target="_blank" rel="noopener noreferrer">
                  Click here for Online Payment
                </a>
              </Link>
            </div>

            <section className="mb-8">
              <h3 className="text-xl font-semibold text-gray-800 mb-4 font-poppins">TERMS AND CONDITIONS:</h3>
              <ul className="list-disc list-inside space-y-2 text-gray-700 font-poppins">
                <li>Please Check Your Card Limit Before Proceeding To Online Payment.</li>
                <li>If The Transaction Has Failed For Some Reasons, You Are REQUESTED TO WAIT For THREE DAYS Before Trying for Payment Again. Please Contact The ERP Section Of Jamal Mohamed College For Any Discrepancy Of Online Fee Faced By You With Reference To Any Of Your Transaction.</li>
                <li>In Any Case, Make A Note Of Reference/Transaction Details In Case Of Net Banking Or Card Payment Or UPI.</li>
              </ul>
            </section>

            <section className="mb-8">
              <h3 className="text-xl font-semibold text-gray-800 mb-4 font-poppins">PRIVACY POLICY:</h3>
              <p className="text-gray-700 font-poppins leading-relaxed">
                The details provided by you shall be utilized only for the purpose of receiving the payments to be made by you to the Institution. All data shall be kept secure, and shall not be divulged to anyone or utilized for any other purpose.
              </p>
            </section>

            <section className="mb-8">
              <h3 className="text-xl font-semibold text-gray-800 mb-4 font-poppins">CANCELLATION/REFUND POLICY:</h3>
              <ul className="list-disc list-inside space-y-2 text-gray-700 font-poppins">
                <li>There is no cancellation option for the end users.</li>
                <li>In case of duplicate payment, end user to approach ERP Section with proof of the transaction reference/ your bank statement.</li>
              </ul>
            </section>

            <section>
              <h3 className="text-xl font-semibold text-gray-800 mb-4 font-poppins">IMPORTANT:</h3>
              <p className="text-gray-700 font-poppins leading-relaxed">
                By submitting a payment through the online-payments site you are agreeing to these terms and conditions including any updated changes in terms and conditions from time to time through our website.
              </p>
            </section>
          </div>
        </div>
        
        <section className="py-10 px-4 mt-4 md:mt-8">
          <div className="container max-w-7xl mx-auto">
            <CTAComponent />
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
};

export default FeesPaymentProcedurePage;