"use client";

import { generalInformation, visitorsTime } from "@/app/helper/helper";

const WomensHostelAdmission = ({
  admissionRules,
  hostelAmenities,
  dailyLifeInTheHostel,
  messTimings,
}: any) => {
  return (
    <section className="bg-white pb-10 px-4">
      <div className="container max-w-7xl mx-auto space-y-6">
        <div className="rounded-2xl border border-gray-200 p-4 md:p-10 lg:px-16 lg:py-10 shadow-sm bg-white space-y-6">
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              Admission
            </h3>
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              {admissionRules?.map((rule: any, idx: any) => (
                <li key={idx}>{rule}</li>
              ))}
            </ul>
          </div>
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              Amenities in the Hostel
            </h3>
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              {hostelAmenities?.map((rule: any, idx: any) => (
                <li key={idx}>{rule}</li>
              ))}
            </ul>
          </div>
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              Daily Life in the Hostel
            </h3>
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              {dailyLifeInTheHostel?.map((rule: any, idx: any) => (
                <li key={idx}>{rule}</li>
              ))}
            </ul>
          </div>
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              Mess Timings
            </h3>
            <div className="overflow-x-auto flex justify-center pb-8">
              <table className="w-full max-w-4xl mx-auto border-collapse rounded-2xl overflow-hidden font-ramilas font-medium relative border border-custom-green bg-custom-light-green md:shadow-milestone">
                <thead>
                  <tr className="bg-custom-green text-white rounded-t-2xl">
                    <th className="px-6 md:px-8 py-4 text-left">
                      Available Programs
                    </th>
                    <th className="px-6 md:px-8 py-4 text-center">
                      Working Days
                    </th>
                    <th className="px-6 md:px-8 py-4 text-right">Holidays</th>
                  </tr>
                </thead>
                <tbody className="text-custom-green">
                  {messTimings?.map((item: any, index: any) => (
                    <tr
                      key={index}
                      className={
                        index !== messTimings.length - 1
                          ? "border-b border-gray-300"
                          : ""
                      }
                    >
                      <td className="px-6 md:px-8 py-4">{item.program}</td>
                      <td className="px-6 md:px-8 py-4 text-center">
                        {item.workingDays}
                      </td>
                      <td className="px-6 md:px-8 py-4 text-right">
                        {item.holidays}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              Visitor's Time
            </h3>
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              {visitorsTime?.map((rule: any, idx: any) => (
                <li key={idx}>{rule}</li>
              ))}
            </ul>
          </div>
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              Going Home and Coming Back
            </h3>
            <p className="text-custom-new-green text-justify">
              Inmates will be permitted to go home with a person whose name is
              mentioned in her hostel ID. She has to submit a leave form to the
              sub-warden and collect an out pass and entry pass. The out pass
              should be handed over to the security at the college main gate.
              The entry pass should be handed over to the security at hostel
              main gate at the time of returning from home. The inmate should
              also affix the finger print at the hostel main gate while going
              home and coming back to the hostel. An SMS will be delivered to
              the parent mobile immediately stating the time of leaving the
              hostel and entry of the inmate into hostel respectively.
            </p>
          </div>
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              General Information
            </h3>
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              {generalInformation?.map((rule: any, idx: any) => (
                <li key={idx}>{rule}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WomensHostelAdmission;
