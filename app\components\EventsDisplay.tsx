import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import React, { useState, useEffect, TouchEvent } from "react";

interface Event {
  _id?: string; // Add _id if available from Sanity for more stable keys
  image: string;
  title: string;
  date: string;
  description: string;
}

interface EventsDisplayProps {
  events: Event[];
  title?: string;
  description?: string;
}

// Card variants remain the same for the 3D effect
const cardVariants = {
  enter: (direction: number) => ({
    x: direction > 0 ? '60%' : '-60%',
    opacity: 0.3,
    scale: 0.85,
    rotateY: direction > 0 ? -25 : 25,
    zIndex: 20,
  }),
  center: {
    x: 0,
    opacity: 1,
    scale: 1,
    rotateY: 0,
    zIndex: 30,
    transition: {
      duration: 0.5,
      type: "spring",
      stiffness: 300,
      damping: 30
    }
  },
  exit: (direction: number) => ({
    x: direction < 0 ? '60%' : '-60%',
    opacity: 0.3,
    scale: 0.85,
    rotateY: direction < 0 ? -25 : 25,
    zIndex: 20,
  })
};

const desktopCardVariants = {
  hidden: {
    opacity: 0,
    y: 20,
  },
  visible: (index: number) => ({
    opacity: 1,
    y: 0,
    transition: {
      delay: index * 0.15,
      duration: 0.4,
      ease: [0.25, 0.1, 0.25, 1],
    },
  }),
  exit: {
    opacity: 0,
    y: -20,
  }
};

const EventsDisplay: React.FC<EventsDisplayProps> = ({
  events,
  title,
  description,
}) => {
  const eventsPerPageDesktop = 3;
  const [currentIndex, setCurrentIndex] = useState(0); // Renamed from currentPage
  const [isMobile, setIsMobile] = useState(false);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  
  const totalEvents = events.length;
  // Total pages for desktop pagination
  const totalPagesDesktop = Math.ceil(totalEvents / eventsPerPageDesktop);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // --- Touch Handlers --- 
  const handleTouchStart = (e: TouchEvent) => {
    if (totalEvents <= 1) return; // No swipe if only 1 item
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (totalEvents <= 1) return;
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (totalEvents <= 1) return;
    const threshold = 75;
    if (touchStart - touchEnd > threshold) {
      // Swipe left -> Go to next index
      setCurrentIndex((prev) => (prev + 1) % totalEvents);
    } else if (touchEnd - touchStart > threshold) {
      // Swipe right -> Go to previous index
      setCurrentIndex((prev) => (prev - 1 + totalEvents) % totalEvents);
    }
    // Reset touch points
    setTouchStart(0);
    setTouchEnd(0);
  };

  // --- Calculate indices for mobile carousel --- 
  const getMobileIndices = () => {
    if (totalEvents === 0) return [];
    if (totalEvents === 1) return [0]; // Only center index
    
    const prevIndex = (currentIndex - 1 + totalEvents) % totalEvents;
    const nextIndex = (currentIndex + 1) % totalEvents;
    
    // Return indices in order: previous, center, next
    // Handle case with 2 events where prev and next are the same
    if (totalEvents === 2) {
       // Show the other card as both prev and next visually
       return [nextIndex, currentIndex, nextIndex]; 
    }
    
    return [prevIndex, currentIndex, nextIndex];
  };

  const mobileIndices = getMobileIndices();
  const displayedMobileEvents = mobileIndices.map(index => events[index]);

  return (
    <div className="container mx-auto ">
      {title && (
        <h3 className="font-medium text-custom-green text-center mb-8 px-4">
          {title}
        </h3>
      )}
      {description && (
        <p className="px-4 text-[#565656] font-poppins leading-relaxed mb-8 text-center">
          {description}
        </p>
      )}
      
      <div 
        className={`relative ${isMobile ? 'h-[450px] mb-8 overflow-hidden' : 'px-4'}`}
        onTouchStart={isMobile ? handleTouchStart : undefined}
        onTouchMove={isMobile ? handleTouchMove : undefined}
        onTouchEnd={isMobile ? handleTouchEnd : undefined}
      >
        {isMobile ? (
          // --- Mobile: 3D Carousel (One-by-One Swipe) --- 
          <div className="relative w-full h-full flex items-center justify-center">
            {totalEvents === 0 ? (
              <p>No events to display.</p> // Handle empty case
            ) : (
              <AnimatePresence initial={false} custom={currentIndex}> 
                {displayedMobileEvents.map((event, displayIndex) => {
                  // Determine the role based on the original index
                  const originalIndex = events.findIndex(e => (e._id || e.title) === (event._id || event.title));
                  const centerIndexInFullArray = currentIndex;
                  const prevIndexInFullArray = (currentIndex - 1 + totalEvents) % totalEvents;
                  const nextIndexInFullArray = (currentIndex + 1) % totalEvents;

                  let role = 'center';
                  if (originalIndex === prevIndexInFullArray && totalEvents > 1) {
                    role = 'prev';
                  } else if (originalIndex === nextIndexInFullArray && totalEvents > 1) {
                    role = 'next';
                  }
                  
                  // Special handling for 2 items where prev/next are the same visual card
                  if (totalEvents === 2 && displayIndex !== 1) {
                     role = 'side'; // Treat the non-center card as a side card
                  }

                  const isCenter = role === 'center';
                  const isPrev = role === 'prev';
                  const isNext = role === 'next';
                  const isSide = role === 'side'; // For the 2-item case

                  // Use a more stable key if _id is available
                  const key = event._id ? `${event._id}-${displayIndex}` : `${event.title}-${displayIndex}`;

                  return (
                    <motion.div
                      key={key} 
                      // Pass direction based on role relative to center
                      custom={isPrev ? -1 : (isNext ? 1 : 0)} 
                      variants={cardVariants}
                      initial="enter"
                      // Animate to 'center' if it's the center card, otherwise 'enter'
                      animate={isCenter ? "center" : "enter"}
                      exit="exit"
                      style={{
                        position: 'absolute',
                        width: isCenter ? '85%' : '65%',
                        height: isCenter ? '380px' : '330px',
                        transformOrigin: 'center',
                        transformStyle: 'preserve-3d',
                        boxShadow: isCenter 
                          ? '0 10px 30px rgba(0,0,0,0.2)' 
                          : '0 5px 15px rgba(0,0,0,0.1)'
                      }}
                      className="bg-white rounded-2xl overflow-hidden border-2 border-custom-green"
                    >
                      {/* Card Content - Opacity based on being center */}
                      <motion.div 
                        className="px-3 pt-3"
                        animate={{ opacity: isCenter ? 1 : 0.3 }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="relative h-44">
                          <Image
                            src={event.image}
                            alt={event.title}
                            fill
                            className="object-cover rounded-2xl"
                          />
                        </div>
                      </motion.div>
                      <motion.div 
                        className="p-4"
                        animate={{ opacity: isCenter ? 1 : 0.3 }}
                        transition={{ duration: 0.3 }}
                      >
                        <h3 className="text-lg font-poppins font-semibold text-[#555555] mb-2 line-clamp-1">
                          {event.title}
                        </h3>
                        <p className="text-sm text-[#555555] mb-1">{event.date}</p>
                        <p className="text-sm font-poppins font-normal text-[#555555] leading-5 line-clamp-2">
                          {event.description}
                        </p>
                      </motion.div>
                    </motion.div>
                  );
                })}
              </AnimatePresence>
            )}
          </div>
        ) : (
          // --- Desktop: Paginated Grid --- (Using currentIndex for page) 
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {events
              // Use currentIndex for desktop pagination as well, but calculate page based on it
              .slice(currentIndex * eventsPerPageDesktop, (currentIndex + 1) * eventsPerPageDesktop)
              .map((event, index) => (
                <motion.div
                  // Ensure desktop key is unique per page
                  key={`${event._id || event.title}-${currentIndex}-${index}`}
                  variants={desktopCardVariants}
                  initial="hidden"
                  animate="visible"
                  exit="exit"
                  custom={index}
                  className="bg-white rounded-2xl overflow-hidden border-2 border-custom-green 
                    transition-all duration-300 ease-in-out hover:shadow-[10px_10px_0px_0px_#002E00]"
                >
                  {/* ... Desktop card content ... */}
                   <div className="px-4 pt-4">
                     <div className="relative h-52">
                       <Image
                         src={event.image}
                         alt={event.title}
                         fill
                         className="object-cover rounded-2xl"
                       />
                     </div>
                   </div>
                   <div className="p-6">
                     <h3 className="text-xl font-poppins font-semibold text-[#555555] mb-2">
                       {event.title}
                     </h3>
                     <p className="text-[#555555] mb-2">{event.date}</p>
                     <p className="font-poppins font-normal text-[#555555] leading-6">
                       {event.description}
                     </p>
                   </div>
                </motion.div>
              ))}
          </div>
        )}
      </div>

      {/* Pagination Dots - Reflect current index/page */}
      <div className={`
        flex justify-center items-center space-x-2
        ${isMobile ? 'mt-4 mb-4' : 'mt-8'} 
      `}>
        {/* Use totalEvents for mobile dots, totalPagesDesktop for desktop */} 
        {Array.from({ length: isMobile ? totalEvents : totalPagesDesktop }).map((_, index) => (
          <button
            key={index}
            // Set currentIndex directly for mobile, calculate page for desktop
            onClick={() => setCurrentIndex(index)} 
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              // Check against currentIndex for mobile, current page index for desktop
              currentIndex === index
                ? "bg-custom-green w-6"
                : "bg-custom-green bg-opacity-40"
            }`}
            aria-label={`Go to ${isMobile ? 'event' : 'page'} ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};

export default EventsDisplay;
