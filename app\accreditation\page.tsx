"use client";

import Navbar from "@/components/Navbar";
import Link from "next/link";
import Footer from "../sections/Footer";
import ImagePreview from "../components/ImagePreview";
import { useEffect, useState } from "react";
import { client } from "@/lib/sanity";
import { getAccreditation } from "@/sanity/lib/queries";

const Page = () => {
  const [groupedData, setGroupedData] = useState<Map<string, any[]>>(new Map());

  const predefinedCategories = [
    "National Assessment and Accreditation Council - NAAC",
    "University Grants Commission - UGC",
    "BharathiDasan University",
    "All India Survey on Higher Education (AISHE)",
    "NIRF",
    "E-Certificate for ARIIA",
    "Certificate of Appreciation",
  ];

  useEffect(() => {
    const fetchAccreditations = async () => {
      const data = await client.fetch(getAccreditation);

      const filteredData = data.filter((item: any) =>
        predefinedCategories.some(
          (category) =>
            category.toLowerCase() === item.category?.trim().toLowerCase()
        )
      );

      const map = new Map();

      filteredData.forEach((item: any) => {
        const category = item.category?.trim();
        if (!map.has(category)) {
          map.set(category, []);
        }
        map.get(category).push(item);
      });

      setGroupedData(map);
    };

    fetchAccreditations();
  }, []);

  return (
    <>
      <Navbar fixed={false} border={false} />
      <header
        className="relative mt-4 px-4 py-8 md:py-16 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex justify-between items-center">
          <h1 className="font-ramilas">Our Accreditation</h1>
          <ul className="font-poppins flex flex-col md:flex-row md:gap-8 md:pr-16">
            <li className="list-disc">
              <Link href="/">Home</Link>
            </li>
            <li className="list-disc">Our Accreditation</li>
          </ul>
        </div>
      </header>
      <main>
        {predefinedCategories.map((category) => {
          const items = groupedData.get(category);
          if (!items || items.length === 0) return null;

          return (
            <section key={category} className="bg-white py-5 px-4">
              <div className="container max-w-7xl mx-auto">
                <h2 className="text-custom-green">{category}</h2>
                <ImagePreview items={items} />
              </div>
            </section>
          );
        })}
      </main>
      <Footer />
    </>
  );
};

export default Page;
