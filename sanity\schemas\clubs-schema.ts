import type { Rule } from "@sanity/types";
const clubSchema = {
  name: "club",
  title: "Extension Activity Club",
  type: "document",
  fields: [
    {
      name: "name",
      title: "Club Name",
      type: "string",
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: "description",
      title: "Description",
      type: "text",
      rows: 3,
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: "image",
      title: "Club Image URL", // Changed title to reflect it's a URL
      type: "url",            // Changed type from 'image' to 'url'
      validation: (Rule: Rule) => Rule.required(), // Kept validation as URL is still required
    },
    {
      name: "objectives",
      title: "Objectives",
      type: "array",
      of: [{ type: "string" }],
      validation: (Rule: Rule) => Rule.required().min(1),
    },
    {
      name: "members",
      title: "Members",
      type: "array",
      of: [{ type: "string" }],
      validation: (Rule: Rule) => Rule.required().min(1),
    },
    {
      name: "button<PERSON>abel",
      title: "Button Label",
      type: "string",
      validation: (Rule: Rule) => Rule.required(),
    },
  ],
};

export default clubSchema;
