"use client";

import { Download } from "lucide-react";

// Define an interface for the props
interface ValueAddedCourse {
  _id: string; // Assuming Sanity adds an _id
  title: string;
  fileUrl: string;
}

interface ValueAddedCoursesProps {
  courses: ValueAddedCourse[];
}

// Accept 'courses' prop
const ValueAddedCourses = ({ courses }: ValueAddedCoursesProps) => {
  // Handle case where courses might be empty or undefined
  if (!courses || courses.length === 0) {
    return (
      <section className="py-10 px-4 bg-white">
        <div className="container max-w-7xl mx-auto space-y-6 text-center">
          <h3 className="text-custom-green">Value Added Courses</h3>
          <p className="text-[#565656]">No value added courses available at this time.</p>
        </div>
      </section>
    );
  }

  return (
    <section className="py-10 px-4 bg-white">
      <div className="container max-w-7xl mx-auto space-y-6 text-center">
        <h3 className="text-custom-green">Value Added Courses</h3>
        <p className="text-[#565656]">
          Discover our Value Added Courses designed to enhance your skills and knowledge. Download year-wise resources to stay updated and excel in your field.
        </p>
        <div className="flex flex-wrap justify-center gap-4 font-poppins font-semibold">
          {/* Map over the courses prop */}
          {courses.map((course) => (
            <a
              key={course._id} // Use Sanity _id as key
              href={course.fileUrl} // Use fileUrl from props
              download
              target="_blank" // Optional: Open in new tab
              rel="noopener noreferrer" // Security for target="_blank"
              className="flex items-center justify-center gap-8 px-8 py-3 bg-custom-green text-white font-semibold rounded-lg hover:bg-green-800 transition"
            >
              {course.title} {/* Use title from props */}
              <Download size={20} />
            </a>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ValueAddedCourses;
