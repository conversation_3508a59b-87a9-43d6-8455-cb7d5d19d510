import { client } from "@/lib/sanity"; // Adjust path if your client is elsewhere

export interface PolicyData {
  _id: string;
  name: string;
  url: string; // This still correctly represents the PDF URL
}

export async function getPolicies(): Promise<PolicyData[]> {
  // Query to fetch policy documents, ordering by name
  // Select the 'policyUrl' field directly
  const query = `*[_type == "policy"] | order(name asc) {
    _id,
    name,
    "url": policyUrl // Changed from policyFile.asset->url to policyUrl
  }`;

  const results = await client.fetch(query);
  console.log("Fetched policies results: ", results);

  // Mapping remains the same, as we still expect a 'url' property
  return results.map((policy: any) => ({
    _id: policy._id,
    name: policy.name || 'Unnamed Policy',
    url: policy.url || '#', 
  }));
}