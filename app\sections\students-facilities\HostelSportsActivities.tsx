"use client";

import Image from "next/image";
import { useEffect, useState } from "react";
import { client } from "@/lib/sanity";
import { JMC_STUDENT_FACILITIES_QUERY } from "@/sanity/lib/queries";

interface SportsImage {
  name: string;
  imageUrl: string;
  title: string;
  description: string;
}

interface JmcStudentFacilitiesData {
  _id: string;
  studentCounsellingImages: any[];
  cafeteriaImages: any[];
  dayCareCentreImages: any[];
  transportImages: any[];
  communicationLabImages: any[];
  sportsImages: SportsImage[];
}

const HostelSportsActivities = () => {
  const [sportsImages, setSportsImages] = useState<SportsImage[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    client.fetch(JMC_STUDENT_FACILITIES_QUERY).then((data: JmcStudentFacilitiesData[]) => {
      if (data.length > 0 && data[0].sportsImages?.length > 0) {
        // Filter out images with empty URLs
        const validImages = data[0].sportsImages.filter(img =>
          img.imageUrl && img.imageUrl.trim() !== ""
        );
        setSportsImages(validImages);
      }
      setIsLoading(false);
    }).catch(() => {
      setIsLoading(false);
    });
  }, []);

  return (
    <section className="bg-white py-10 px-4">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green text-center">
          Hostel Sports Activities
        </h2>
        <p className="text-custom-new-green text-center">
          Aliquet sed nulla tincidunt pulvinar sed fames sit facilisis dictumst.
          Ornare faucibus quis velit fringilla aliquam ultricies. Malesuada ut
          aliquam at ac est nisi, interdum etiam dignissim. Sed ut vestibulum
          eget purus ornare. Risus elit et fringilla habitant ut facilisi.
        </p>
        {isLoading ? (
          <div className="flex flex-col md:flex-row gap-6">
            <div className="h-[200px] md:h-[350px] border-[3px] border-gray-200 flex-1 rounded-xl overflow-hidden shadow-md bg-gray-200 animate-pulse">
              <div className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-shimmer"></div>
            </div>
            <div className="h-[200px] md:h-[350px] border-[3px] border-gray-200 flex-1 rounded-xl overflow-hidden shadow-md bg-gray-200 animate-pulse">
              <div className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-shimmer"></div>
            </div>
          </div>
        ) : sportsImages.length > 0 ? (
          <div className="flex flex-col md:flex-row gap-6">
            {sportsImages.slice(0, 2).map((sport, idx) => (
              <div key={idx} className="relative h-[200px] md:h-[350px] border-[3px] border-custom-green flex-1 rounded-xl overflow-hidden shadow-md group cursor-pointer">
                <Image
                  src={sport.imageUrl}
                  alt={sport.name}
                  width={500}
                  height={400}
                  className="w-full h-full object-cover"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-green-800 to-transparent p-4 font-ramilas text-white font-bold text-center text-lg">
                  <h3 className="font-semibold mb-2">{sport.title}</h3>
                  <div className="max-h-0 overflow-hidden group-hover:max-h-32 transition-all duration-300">
                    <p className="text-sm leading-relaxed">
                      {sport.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-8">
            <p>No sports activities images available at the moment.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default HostelSportsActivities;
