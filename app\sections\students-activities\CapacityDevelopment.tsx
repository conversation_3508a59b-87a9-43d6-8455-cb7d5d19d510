"use clients";

const CapacityDevelopment = () => {
  const activitiesData = [
    {
      year: "2021 - 2022",
      activities: [
        {
          title: "Awareness of Trends in Technology 2021 - 2022",
          link: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/CapacityDevelopmentActivities/5.1.3.Awareness-of-Trends-in-Technology.pdf",
        },
        {
          title: "Language and Communication Skills 2021 - 2022",
          link: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/CapacityDevelopmentActivities/5.1.3.Language-and-comm-skill.pdf",
        },
        {
          title: "Life Skills 2021 - 2022",
          link: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/CapacityDevelopmentActivities/5.1.3.Life-skill.pdf",
        },
        {
          title: "Soft Skills 2021 - 2022",
          link: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/CapacityDevelopmentActivities/5.1.3.Soft-skill.pdf",
        },
      ],
    },
    {
      year: "2022 - 2023",
      activities: [
        {
          title: "Awareness of Trends in Technology 2022 - 2023",
          link: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/CapacityDevelopmentActivities/5.1.3-2022-2023-Trends-and-Technology.pdf",
        },
        {
          title: "Language and Communication Skills 2022 - 2023",
          link: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/CapacityDevelopmentActivities/5.1.3-2022-2023-Language-and-Communication-Skills.pdf",
        },
        {
          title: "Life Skills 2022 - 2023",
          link: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/CapacityDevelopmentActivities/5.1.3-2022-2023-Life-Skill.pdf",
        },
        {
          title: "Soft Skills 2021 - 2023",
          link: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/CapacityDevelopmentActivities/5.1.3-2022-2023-Soft-Skill.pdf",
        },
      ],
    },
    {
      year: "2023 - 2024",
      activities: [
        {
          title: "Awareness of Trends in Technology 2023 - 2024",
          link: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/CapacityDevelopmentActivities/5.1.3-2023-2024-Trends-and-Technology.pdf",
        },
        {
          title: "Language and Communication Skills 2023 - 2024",
          link: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/CapacityDevelopmentActivities/5.1.3-2023-2024-Language-and-Communication-Skills.pdf",
        },
        {
          title: "Life Skills 2023 - 2024",
          link: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/CapacityDevelopmentActivities/5.1.3-2023-2024-Life-Skill.pdf",
        },
        {
          title: "Soft Skills 2023 - 2024",
          link: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/CapacityDevelopmentActivities/5.1.3-2023-2024-Soft-Skill.pdf",
        },
      ],
    },
  ];

  return (
    <section className="bg-white py-16 md:py-10 px-4">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green">Capacity Development Activities</h2>
        <div className="rounded-2xl border border-gray-200 p-4 md:p-10 shadow-sm bg-white">
          <div className="space-y-8 font-poppins">
            {activitiesData.map(({ year, activities }) => (
              <div key={year}>
                <h3 className="font-semibold text-custom-green">
                  Capacity Development Activities {year}
                </h3>
                <ul className="list-disc pl-4 md:pl-6 list-outside mt-2 space-y-1 font-poppins font-semibold custom-marker">
                  {activities.map((activity, idx) => (
                    <li key={idx}>
                      <a
                        href={activity.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-custom-new-green underline underline-offset-2 hover:text-green-800"
                      >
                        {activity.title}
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default CapacityDevelopment;
