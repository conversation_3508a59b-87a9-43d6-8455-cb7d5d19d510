"use client";
import { useState } from "react";
import { Minus, Plus } from "lucide-react";

export default function CourseFilters({
  studyLevels,
  selectedLevel,
  setSelectedLevel,
  studyPatterns,
  selectedPattern,
  setSelectedPattern,
}: any) {
  const [openSections, setOpenSections] = useState({
    studyLevel: true,
    studyPattern: true,
    fieldOfStudy: true,
  });

  const toggleSection = (section: keyof typeof openSections) => {
    setOpenSections((prev) => ({ ...prev, [section]: !prev[section] }));
  };

  return (
    <div className="space-y-6">
      {/* Study Level */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-bold text-lg">Study Level</h3>
          <button
            onClick={() => toggleSection("studyLevel")}
            className="text-green-900"
          >
            {openSections.studyLevel ? <Minus size={16} /> : <Plus size={16} />}
          </button>
        </div>
        {openSections.studyLevel && (
          <ul className="space-y-2 font-poppins">
            {studyLevels.map((level: string) => (
              <li key={level} className="flex items-center space-x-3">
                <input
                  type="radio"
                  id={level}
                  name="studyLevel"
                  value={level}
                  checked={selectedLevel === level}
                  onChange={() => setSelectedLevel(level)}
                  className="relative appearance-none w-4 h-4 border border-custom-green rounded-full checked:before:absolute checked:before:content-[''] checked:before:w-2 checked:before:h-2 checked:before:bg-custom-green checked:before:rounded-full checked:before:top-1/2 checked:before:left-1/2 checked:before:-translate-x-1/2 checked:before:-translate-y-1/2"
                />
                <label htmlFor={level} className="text-sm">
                  {level}
                </label>
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* Study Pattern */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-bold text-lg">Study Pattern</h3>
          <button
            onClick={() => toggleSection("studyPattern")}
            className="text-green-900"
          >
            {openSections.studyPattern ? (
              <Minus size={16} />
            ) : (
              <Plus size={16} />
            )}
          </button>
        </div>
        {openSections.studyPattern && (
          <ul className="space-y-2 font-poppins">
            {studyPatterns?.map((pattern: string) => (
              <li key={pattern} className="flex items-center space-x-3">
                <input
                  type="radio"
                  id={pattern}
                  name="studyPattern"
                  value={pattern}
                  checked={selectedPattern === pattern}
                  onChange={() => setSelectedPattern(pattern)}
                  className="relative appearance-none w-4 h-4 border border-custom-green rounded-full checked:before:absolute checked:before:content-[''] checked:before:w-2 checked:before:h-2 checked:before:bg-custom-green checked:before:rounded-full checked:before:top-1/2 checked:before:left-1/2 checked:before:-translate-x-1/2 checked:before:-translate-y-1/2"
                />
                <label htmlFor={pattern} className="text-sm">
                  {pattern}
                </label>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}
