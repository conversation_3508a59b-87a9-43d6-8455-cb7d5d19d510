"use client";

import Navbar from "@/components/Navbar";
import Footer from "../sections/Footer";
import Link from "next/link";
import Sidebar from "../components/Sidebar";
import { menuItems } from "@/components/Navbar";
import { usePathname } from "next/navigation";
import CTAComponent from "../components/CTAComponent";
import NIRFDocumentsSection from "../components/NIRFDocumentsSection";

const Page = () => {
  const pathname = usePathname();

  const currentSection = menuItems.find(
    (item) => pathname.startsWith("/iqac") && item?.subSections
  );

  // NIRF Documents Data
  const nirfDocuments = [
    {
      year: "2025",
      title: "NIRF 2025 DCS",
      documents: [
        {
          type: "report" as const,
          title: "NIRF 2025 Report",
          year: "2025",
          buttonText: "Click to view or Download",
          link: "#"
        },
        {
          type: "pcs" as const,
          title: "PCS",
          year: "2025",
          link: "#"
        },
        {
          type: "certificate" as const,
          title: "Certificate",
          year: "2025",
          link: "#"
        }
      ]
    },
    {
      year: "2024",
      title: "NIRF 2024 DCS",
      documents: [
        {
          type: "report" as const,
          title: "NIRF 2024 Report",
          year: "2024",
          buttonText: "Click to view or Download",
          link: "#"
        },
        {
          type: "pcs" as const,
          title: "PCS",
          year: "2024",
          link: "#"
        },
        {
          type: "certificate" as const,
          title: "Certificate",
          year: "2024",
          link: "#"
        }
      ]
    },
    {
      year: "2023",
      title: "NIRF 2023 DCS",
      documents: [
        {
          type: "report" as const,
          title: "NIRF 2023 Report",
          year: "2023",
          buttonText: "Click to view or Download",
          link: "#"
        },
        {
          type: "pcs" as const,
          title: "PCS",
          year: "2023",
          link: "#"
        },
        {
          type: "certificate" as const,
          title: "Certificate",
          year: "2023",
          link: "#"
        }
      ]
    },
    {
      year: "2022",
      title: "NIRF 2022 DCS",
      documents: [
        {
          type: "report" as const,
          title: "NIRF 2022 Report",
          year: "2022",
          buttonText: "Click to view or Download",
          link: "#"
        },
        {
          type: "pcs" as const,
          title: "PCS",
          year: "2022",
          link: "#"
        },
        {
          type: "certificate" as const,
          title: "Certificate",
          year: "2022",
          link: "#"
        }
      ]
    },
    {
      year: "2021",
      title: "NIRF 2021 DCS",
      documents: [
        {
          type: "report" as const,
          title: "NIRF 2021 Report",
          year: "2021",
          buttonText: "Click to view or Download",
          link: "#"
        },
        {
          type: "pcs" as const,
          title: "PCS",
          year: "2021",
          link: "#"
        },
        {
          type: "certificate" as const,
          title: "Certificate",
          year: "2021",
          link: "#"
        }
      ]
    },
    {
      year: "2020",
      title: "NIRF 2020 DCS",
      documents: [
        {
          type: "report" as const,
          title: "NIRF 2020 Report",
          year: "2020",
          buttonText: "Click to view or Download",
          link: "#"
        },
        {
          type: "pcs" as const,
          title: "PCS",
          year: "2020",
          link: "#"
        },
        {
          type: "certificate" as const,
          title: "Certificate",
          year: "2020",
          link: "#"
        }
      ]
    },
    {
      year: "2019",
      title: "NIRF 2019 DCS",
      documents: [
        {
          type: "report" as const,
          title: "NIRF 2019 Report",
          year: "2019",
          buttonText: "Click to view or Download",
          link: "#"
        },
        {
          type: "pcs" as const,
          title: "PCS",
          year: "2019",
          link: "#"
        },
        {
          type: "certificate" as const,
          title: "Certificate",
          year: "2019",
          link: "#"
        }
      ]
    },
    {
      year: "2018",
      title: "NIRF 2018 DCS",
      documents: [
        {
          type: "report" as const,
          title: "NIRF 2018 Report",
          year: "2018",
          buttonText: "Click to view or Download",
          link: "#"
        },
        {
          type: "pcs" as const,
          title: "PCS",
          year: "2018",
          link: "#"
        },
        {
          type: "certificate" as const,
          title: "Certificate",
          year: "2018",
          link: "#"
        }
      ]
    }
  ];

  return (
    <>
      <Navbar fixed={true} border={true} />
      <header
        className="relative pt-40 pb-16 px-4 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex flex-col md:flex-row md:justify-between md:items-center">
          <h1 className="font-ramilas text-center md:text-left mb-4 md:mb-0">
            NIRF
          </h1>
          <ul className="font-poppins flex flex-row justify-center md:justify-end gap-8 md:gap-8 md:pr-16">
            <li className="list-disc">
              <Link href="/">Home</Link>
            </li>
            <li className="list-disc">
              <Link href="/iqac">IQAC</Link>
            </li>
            <li className="list-disc">NIRF</li>
          </ul>
        </div>
      </header>

      <main>
        {currentSection && (
          <Sidebar subsections={currentSection.subSections || []} />
        )}

        {/* NIRF Documents Section */}
        <NIRFDocumentsSection
          title="NIRF Documents"
          documentRows={nirfDocuments}
        />

        <section className="bg-white py-10 px-4">
          <div className="container max-w-7xl mx-auto">
            <CTAComponent />
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
};

export default Page;
