"use client";
import React, { useEffect, useState } from "react";
import { fetchNewsletters } from "@/sanity/lib/fetchNewsletters";

type Newsletter = {
  _id: string;
  name: string;
  category: string;
  url: string;
};

type NewsletterGroup = {
  [category: string]: Newsletter[];
};

export default function Newsletter() {
  const [groupedNewsletters, setGroupedNewsletters] = useState<NewsletterGroup>(
    {}
  );
  const [selectedIndexMap, setSelectedIndexMap] = useState<{
    [category: string]: number | null;
  }>({});

  useEffect(() => {
    async function load() {
      const data = await fetchNewsletters();
      setGroupedNewsletters(data);
      console.log("Newsletter Data: ", data);

      // Initialize selected index map
      const initialSelectedMap: { [category: string]: number | null } = {};
      for (const category of Object.keys(data)) {
        initialSelectedMap[category] = null;
      }
      setSelectedIndexMap(initialSelectedMap);
    }
    load();
  }, []);

  const handleSelect = (category: string, index: number) => {
    setSelectedIndexMap((prev) => ({ ...prev, [category]: index }));
  };

  return (
    <section className="relative bg-white py-16 px-4">
      <div className="relative z-10 max-w-7xl mx-auto text-center text-white space-y-12">
        {Object.entries(groupedNewsletters).map(([category, issues]) => (
          <div key={category} className="space-y-10">
            <h2 className="text-custom-green">{category}</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {issues.map((item, index) => {
                const selected = selectedIndexMap[category] === index;
                return (
                  <div
                    key={item._id}
                    className={`group border p-4 rounded-2xl transition-all duration-300 flex flex-col items-center justify-center h-[300px] cursor-pointer max-w-[250px] w-full mx-auto
                      ${selected
                        ? "bg-custom-green text-white border-white"
                        : "bg-white text-custom-green border-2 border-custom-green hover:bg-custom-green hover:text-white hover:border-custom-green"
                      }
                    `}
                    onClick={() => handleSelect(category, index)}
                  >
                    <h3 className="text-xl md:text-2xl font-medium mb-2">
                      {item.name}
                    </h3>

                    <a
                      href={item.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`${selected ? "flex" : "hidden group-hover:flex"
                        } bg-white text-custom-green hover:bg-white hover:text-custom-green px-8 py-2 rounded-full font-ramilas font-bold transition-all duration-300`}
                    >
                      Download
                    </a>
                  </div>
                );
              })}
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
