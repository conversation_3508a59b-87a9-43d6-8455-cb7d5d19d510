"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { SlidersHorizontal, X } from "lucide-react"; // Import X icon
import CourseFilters from "./CourseFilters"; // Assuming CourseFilters is in the same directory or adjust path

interface MobileCourseFilterModalProps {
  studyLevels: string[];
  selectedLevel: string;
  setSelectedLevel: (level: string) => void;
  studyPatterns: string[];
  selectedPattern: string;
  setSelectedPattern: (pattern: string) => void;
  handleClear: () => void;
}

export default function MobileCourseFilterModal({
  studyLevels,
  selectedLevel,
  setSelectedLevel,
  studyPatterns,
  selectedPattern,
  setSelectedPattern,
  handleClear,
}: MobileCourseFilterModalProps) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <button className="lg:hidden  text-custom-green p-3 rounded-full border border-custom-green">
          <SlidersHorizontal  size={34} />
        </button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[400px] rounded-lg p-6 w-[90%]">
        <DialogHeader>
          <DialogTitle className="text-xl">Filter Courses</DialogTitle>
          {/* Default close button is rendered by DialogContent */}
        </DialogHeader>
        {/* Filter Content */}
        <div className="text-custom-green">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-4xl font-semibold">Filter</h2>
            <button
              onClick={handleClear}
              className="bg-custom-green text-white text-sm font-poppins font-semibold px-6 py-2 rounded-full"
            >
              Clear all
            </button>
          </div>
          <CourseFilters
            studyLevels={studyLevels}
            selectedLevel={selectedLevel}
            setSelectedLevel={setSelectedLevel}
            studyPatterns={studyPatterns}
            selectedPattern={selectedPattern}
            setSelectedPattern={setSelectedPattern}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}