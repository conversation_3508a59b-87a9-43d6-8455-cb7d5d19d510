import React from "react";
import Image from "next/image";

interface Product {
  id: number;
  imagePath: string;
}

interface DBTProductsProps {
  products?: Product[];
}

const DBTProducts: React.FC<DBTProductsProps> = ({ products }) => {
  // Default products data if none provided
  const defaultProducts: Product[] = [
    {
      id: 1,
      imagePath: "/DBT/dbtproduct3.png"
    },
    {
      id: 2,
      imagePath: "/DBT/dbtproduct2.png"
    },
    {
      id: 3,
      imagePath: "/DBT/dbtproduct1.png"
    }
  ];

  const productsToDisplay = products || defaultProducts;

  return (
    <section className="pt-8 bg-white">
      <div className="container max-w-7xl mx-auto md:px-4">
        {/* Main Title */}
        <div className="text-center mb-4">
          <h2 className="text-3xl md:text-4xl font-bold text-custom-green mb font-ramilas">
            Our Products
          </h2>
        </div>

        {/* Products - Big Poster Images Row by Row */}
        <div className="space-y-8">
          {productsToDisplay.map((product) => (
            <div key={product.id} className="w-full">
              <div className="w-full">
                <Image
                  src={product.imagePath}
                  alt={`Product ${product.id}`}
                  width={1200}
                  height={800}
                  className="w-full h-auto object-contain"
                  sizes="100vw"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default DBTProducts;
