import "./globals.css";
import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { Poppins } from "next/font/google";

const inter = Inter({ subsets: ["latin"] });

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  display: "swap",
  preload: true,
  fallback: ["system-ui", "arial"],
});

export const metadata: Metadata = {
  title: "University Website",
  description: "Welcome to our university - Building futures through education",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      {/* Add flex, flex-col, and min-h-screen */}
      <body className={`${poppins.className} flex flex-col min-h-screen`}>
        {children}
      </body>
    </html>
  );
}
