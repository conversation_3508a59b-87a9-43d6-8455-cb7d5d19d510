import React from "react";

// Define interfaces for our data structure
export interface Project {
  serialNo: number;
  investigator: string;
  title: string;
  amount: string | number;
  isStudent?: boolean;
  mentor?: string;
  student?: string;
}

export interface YearlyProjects {
  year: string;
  projects: Project[];
}

interface SeedMoneyProjectsTableProps {
  yearlyProjects: YearlyProjects[];
}

const SeedMoneyProjectsTable: React.FC<SeedMoneyProjectsTableProps> = ({ yearlyProjects }) => {
  return (
    <>
      {/* Render tables for each year */}
      {yearlyProjects.map((yearData, index) => (
        <div key={index} className="pt-6 sm:pt-8 bg-[#D1D9D1]">
          <h3 className="text-lg sm:text-xl font-bold text-[#002E00] mb-3 sm:mb-4 px-4 sm:px-8">
            {index + 1}. List of Selected Proposals for Financial Assistance during {yearData.year}
          </h3>

          <div >
            <div className="rounded-lg overflow-hidden">
              {/* Add overflow container for mobile responsiveness */}
              <div className="overflow-x-auto">
                <div className="min-w-[768px]"> {/* Minimum width to prevent squishing on mobile */}
                  {/* Header with margin */}
                  <div className="mx-4 my-4">
                    <div className="bg-[#002E00] text-white rounded-full">
                      <div className="grid grid-cols-12 w-full">
                        <div className="py-3 sm:py-4 px-3 sm:px-6 text-left font-medium col-span-1 text-xs sm:text-sm md:text-base">S.NO.</div>
                        <div className="py-3 sm:py-4 px-3 sm:px-6 text-left font-medium col-span-3 text-xs sm:text-sm md:text-base whitespace-normal break-words">NAME OF INVESTIGATOR</div>
                        <div className="py-3 sm:py-4 px-3 sm:px-6 text-left font-medium col-span-5 text-xs sm:text-sm md:text-base whitespace-normal break-words">TITLE OF THE PROJECTS</div>
                        <div className="py-3 sm:py-4 px-3 sm:px-6 text-left font-medium col-span-3 text-xs sm:text-sm md:text-base whitespace-normal break-words">AMOUNT SANCTIONED (INR)</div>
                      </div>
                    </div>
                  </div>

                  {/* Regular Projects */}
                  <table className="min-w-full">
                    <tbody>
                      {yearData.projects.filter(project => !project.isStudent).map((project, idx, arr) => (
                        <tr
                          key={`regular-${project.serialNo}`}
                          className={idx === arr.length - 1 ? "border-0" : "border-b border-[#555555]"}
                        >
                          <td className="pl-8 text-center text-[#555555] w-[8.33%] text-xs sm:text-sm md:text-base">{project.serialNo}</td>
                          <td className="py-2 sm:py-3 px-3 sm:px-6 text-left text-[#555555] w-[25%] text-xs sm:text-sm md:text-base whitespace-normal break-words">{project.investigator}</td>
                          <td className="py-2 sm:py-3 px-3 sm:px-6 text-left text-[#555555] w-[42%] text-xs sm:text-sm md:text-base whitespace-normal break-words">{project.title}</td>
                          <td className="py-2 sm:py-3 px-3 sm:px-6 text-center text-[#555555] w-[25%] text-xs sm:text-sm md:text-base">{project.amount}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>

                  {/* Student Projects Section - only if student projects exist */}
                  {yearData.projects.some(project => project.isStudent) && (
                    <>
                      <div className="px-4 my-3">
                        <div className="bg-[#002E00] text-white text-center py-2 sm:py-3 rounded-full text-xs sm:text-sm md:text-base">
                          Student Project
                        </div>
                      </div>

                      <table className="min-w-full">
                        <tbody>
                          {yearData.projects.filter(project => project.isStudent).map((project) => (
                            <tr key={`student-${project.serialNo}`} className="border-b border-[#555555]">
                              <td className="pl-8 text-center text-[#555555] w-[8.33%] text-xs sm:text-sm md:text-base">{project.serialNo}</td>
                              <td className="py-2 sm:py-3 px-3 sm:px-6 text-left text-[#555555] w-[25%] text-xs sm:text-sm md:text-base whitespace-normal break-words">
                                {project.investigator}
                                <br />
                                {project.student}
                              </td>
                              <td className="py-2 sm:py-3 px-3 sm:px-6 text-left text-[#555555] w-[42%] text-xs sm:text-sm md:text-base whitespace-normal break-words">{project.title}</td>
                              <td className="py-2 sm:py-3 px-3 sm:px-6 text-center text-[#555555] w-[25%] text-xs sm:text-sm md:text-base">{project.amount}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </>
  );
};

export default SeedMoneyProjectsTable;
