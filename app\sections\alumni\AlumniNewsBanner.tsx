"use client";
// app/sections/alumni-showcase/AlumniNewsBanner.tsx
import React, { useCallback, useEffect, useState } from 'react';
import Image from 'next/image'; // Image import is not used in the provided snippet, but keeping it in case it's used elsewhere in the full component
import useEmblaCarousel from 'embla-carousel-react';

// Placeholder data - this would ideally come from props or a CMS
const newsItems = [
  {
    id: 1,
    title: 'Annual Alumni Get-together Chennai Chapter',
    date: '23-11-2024',
    link: '#',
  },
  {
    id: 2,
    title: 'Upcoming Webinar: Career Development',
    date: '15-12-2024',
    link: '#',
  },
  {
    id: 3,
    title: 'Alumni Contribution Drive Success',
    date: '01-01-2025',
    link: '#',
  },
];

const AlumniNewsBanner = () => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true });
  const [selectedIndex, setSelectedIndex] = useState(0);

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi, setSelectedIndex]);

  useEffect(() => {
    if (!emblaApi) return;
    onSelect(); // Initialize selectedIndex
    emblaApi.on('select', onSelect);
    emblaApi.on('reInit', onSelect);
    return () => { // Cleanup
      emblaApi.off('select', onSelect);
      emblaApi.off('reInit', onSelect);
    };
  }, [emblaApi, onSelect]);

  // Ensure newsItems[selectedIndex] is always valid, providing a fallback.
  const currentNewsItem = newsItems[selectedIndex] || newsItems[0];

  return (
    <section 
      className="relative bg-cover bg-center py-16 md:py-24 text-white"
      style={{ backgroundImage: "url('https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/jmcalumni.jpg')" }}
    >
      <div className="absolute bottom-0 left-0 right-0 h-full bg-gradient-to-t from-custom-green to-transparent "></div> 
      
      <div className="container mx-auto px-4 relative z-10 flex flex-col md:flex-row items-center"> {/* Added justify-center for the static card */}
        {/* Static Card Frame */}
        <div className="w-full max-w-md bg-gradient-to-r from-[#0A430A] to-green-800 p-6 md:p-8 rounded-2xl border-2 border-white shadow-[15px_15px_0px_0px_rgba(0,0,0,0.4)] m-2">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Alumni News</h2>
          
          {/* Dynamic Content Area */}
          {currentNewsItem ? (
            <>
              <p className="text-lg mb-1 h-14 overflow-hidden text-ellipsis">{currentNewsItem.title}</p>
              <p className="text-sm mb-4 opacity-90">{currentNewsItem.date}</p>
            </>
          ) : (
            // Fallback if currentNewsItem is somehow undefined (should not happen with the || newsItems[0] logic)
            <p>Loading news...</p>
          )}
          
          {/* Controls */}
          <div className="flex items-center justify-between mt-6">
            <div className="flex space-x-2">
              {newsItems.map((_, index) => (
                <button
                  key={index}
                  onClick={() => emblaApi && emblaApi.scrollTo(index)}
                  className={`block w-3 h-3 rounded-full transition-opacity duration-300 ${selectedIndex === index ? 'bg-white opacity-90' : 'bg-white opacity-50'}`}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
            <button 
              onClick={scrollNext} 
              className="bg-white text-green-700 p-3 rounded-full hover:bg-gray-200 transition-colors duration-300"
              aria-label={`View next news item`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2.5} stroke="currentColor" className="w-6 h-6">
                <path strokeLinecap="round" strokeLinejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
              </svg>
            </button>
          </div>
        </div>

        {/* Minimal Embla Carousel for state management (hidden) */}
        <div className="overflow-hidden w-0 h-0" ref={emblaRef}> {/* Make it take no visual space */}
          <div className="flex">
            {newsItems.map((newsItem) => (
              <div key={newsItem.id} className="flex-[0_0_100%]">
                {/* This slide is not visible. Its purpose is for Embla to count and manage index. */}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default AlumniNewsBanner;