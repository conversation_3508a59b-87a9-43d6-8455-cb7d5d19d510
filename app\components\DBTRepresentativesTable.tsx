import React from "react";

interface DBTRepresentative {
  sno: number;
  nameDesignation: string;
  category: string;
}

interface DBTRepresentativesTableProps {
  title?: string;
  representatives: DBTRepresentative[];
}

const DBTRepresentativesTable: React.FC<DBTRepresentativesTableProps> = ({
  title = "DBT Representatives",
  representatives
}) => {
  return (
    <div>
      <h2 className="text-2xl md:text-3xl font-bold text-center text-[#002E00] mb-6 md:mb-8 mt-8 md:mt-12 px-4">
        {title}
      </h2>

      <div className="bg-[#D1D9D1]">
        <div className="overflow-x-auto">
          <div className="bg-[#D1D9D1] rounded-lg overflow-hidden">
            {/* Header with margin */}
            <div className="mx-2 sm:mx-4 my-2 sm:my-4">
              <div className="bg-[#002E00] text-white rounded-full">
                <div className="flex w-full">
                  <div className="py-3 md:py-4 px-2 sm:px-4 md:px-8 xl:px-16 text-left font-medium w-[15%] sm:w-auto text-xs sm:text-base">S.No</div>
                  <div className="py-3 md:py-4 px-2 sm:px-4 md:px-8 xl:px-16 text-left font-medium w-[35%] sm:flex-1 text-xs sm:text-base">Name & Designation</div>
                  <div className="py-3 md:py-4 px-2 sm:px-4 md:px-8 text-left font-medium w-[50%] sm:flex-1 text-xs sm:text-base">Category</div>
                </div>
              </div>
            </div>

            {/* Table content */}
            <table className="min-w-full">
              <tbody>
                {representatives.map((representative) => (
                  <tr key={representative.sno} className="border-b border-[#555555]">
                    <td className="text-center py-3 px-2 sm:px-4 md:px-8 text-[#002E00] font-medium w-[15%] sm:w-auto">{representative.sno}</td>
                    <td className="py-3 px-2 sm:px-4 text-[#555555] w-[35%] sm:w-auto text-xs sm:text-base">{representative.nameDesignation}</td>
                    <td className="py-3 px-2 sm:px-4 text-[#555555] w-[50%] sm:w-auto text-xs sm:text-base">{representative.category}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DBTRepresentativesTable;
