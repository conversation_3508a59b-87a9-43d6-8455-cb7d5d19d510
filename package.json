{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "typegen": "npx sanity@latest schema extract && npx sanity@latest typegen generate"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@next/font": "^14.2.15", "@next/swc-wasm-nodejs": "13.5.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@sanity/client": "^6.15.1", "@sanity/icons": "^3.7.0", "@sanity/image-url": "^1.1.0", "@sanity/vision": "^3.79.0", "@types/node": "20.6.2", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "autoprefixer": "10.4.15", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "eslint": "8.49.0", "eslint-config-next": "^14.2.15", "framer-motion": "^11.0.3", "input-otp": "^1.2.4", "lucide-react": "^0.446.0", "next": "^14.2.15", "next-sanity": "^9.9.4", "next-themes": "^0.3.0", "postcss": "8.4.30", "react": "18.3.1", "react-day-picker": "^8.10.1", "react-dom": "18.3.1", "react-hook-form": "^7.53.0", "react-is": "^18.3.1", "react-resizable-panels": "^2.1.3", "recharts": "^2.12.7", "sanity": "^3.79.0", "sonner": "^1.5.0", "styled-components": "^6.1.15", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "typescript": "5.7.3", "vaul": "^0.9.9", "zod": "^3.23.8"}, "devDependencies": {"tailwind-scrollbar": "^4.0.2"}}