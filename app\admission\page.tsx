"use client";

import Navbar from "@/components/Navbar";
import Footer from "../sections/Footer";
import Link from "next/link";
import GeneralInstructions from "../sections/admission/GeneralInstructions";
import ProgramsOffered from "../sections/admission/ProgramsOffered";
import Admission from "../sections/admission/Admission";
import OnlineAdmission from "../sections/admission/OnlineAdmission";
import Image from "next/image";
import DownloadBrochure from "../sections/admission/DownloadBrochure";
import DownloadFeesParticulars from "../sections/admission/DownloadFeesParticulars";
import Note from "../sections/admission/Note";
import CTAComponent from "../components/CTAComponent";
import Sidebar from "../components/Sidebar"; // Import Sidebar
import { menuItems } from "@/components/Navbar"; // Import menuItems
import { usePathname } from "next/navigation";
import AdmissionQueryCards from "../components/AdmissionQueryCards";

const Page = () => {
  const pathname = usePathname(); // Get current pathname
  // Find the current section based on the pathname
  const currentSection = menuItems.find((item) => item.href === pathname);

  return (
    <>
      <Navbar fixed={true} border={true} />
      <header
        className="relative px-4 md:px-14 xl:px-28 pt-40 pb-16 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex flex-col items-center gap-4 md:flex-row md:justify-between">
          <h1 className="font-ramilas">Admission</h1>
          <ul className="font-poppins flex flex-row justify-center items-center gap-8 md:flex-row md:items-center md:gap-8 md:pr-16">
            <li className="list-disc"> {/* Restored list-disc */}
              <Link href="/">Home</Link>
            </li>
            <li className="list-disc"> {/* Restored list-disc */}
              Admission
            </li>
          </ul>
        </div>
      </header>

      <main>
        {currentSection && (
          <Sidebar subsections={currentSection.subSections || []} />
        )}
        <GeneralInstructions />
        <ProgramsOffered />
        <div className="relative overflow-hidden">
          {/* Background Image */}
          <div className="absolute top-0 left-0 right-0 bottom-0 w-full h-full z-0 pointer-events-none opacity-100">
            <Image
              src="/homepage/academicsbgimg.png"
              alt="Background Pattern"
              fill
              sizes="100vw"
              priority
              className="object-cover object-center"
            />
          </div>
          <Admission />
          <OnlineAdmission />
        </div>
        <DownloadBrochure />
        <DownloadFeesParticulars />
        <Note />
        <AdmissionQueryCards />
        <section className="bg-white py-10 px-4">
          <div className="container max-w-7xl mx-auto">
            <CTAComponent />
          </div>
        </section>
      </main>

      <Footer />
    </>
  );
};

export default Page;
