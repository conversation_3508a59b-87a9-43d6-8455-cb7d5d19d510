import React from "react";
import FacultyTable from "@/app/components/FacultyTable";

interface FacultySectionProps {
  faculty: any[]; // Replace 'any[]' with the actual type for faculty if available
}

const FacultySection = ({ faculty }: FacultySectionProps) => {
  return (
    <section className="px-4 pt-8">
      <div className="container max-w-7xl mx-auto space-y-6 text-center ">
        <h3 className="text-custom-green">Faculty</h3>
        <p className="text-[#565656] lg:px-4">
        Meet our distinguished faculty, dedicated to academic excellence and mentorship. Explore detailed profiles to learn more about their expertise and contributions.
        </p>
        <FacultyTable faculty={faculty} />
      </div>
    </section>
  );
};

export default FacultySection;