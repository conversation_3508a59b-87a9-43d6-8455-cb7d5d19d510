import React from "react";
import Image from "next/image";

const TopResearch = () => {
  // Array of research cards
  const researchCards = [
    {
      image: "/homepage/otrbgimg1.png",
      name: "Biotechnology Research",
      description: "Our cutting-edge biotechnology research focuses on sustainable solutions for agricultural challenges, with emphasis on drought-resistant crop varieties and eco-friendly pest management systems."
    },
    {
      image: "/homepage/otrbgimg3.png",
      name: "Environmental Science",
      description: "Our environmental research team is pioneering studies on local ecosystem preservation, water quality improvement, and developing innovative approaches to reduce carbon footprint on campus."
    },
    {
      image: "/homepage/otrbgimg2.png",
      name: "Computer Science Innovation",
      description: "Our computer science department is developing advanced machine learning algorithms to address real-world problems, including healthcare diagnostics and educational technology solutions."
    }
  ];

  return (
    <section className="pt-16 pb-4 bg-white">
      <div className="container mx-auto max-w-7xl px-8">
        {/* Row 1: Title */}
        <h2 className="text-4xl font-bold text-center text-custom-green mb-8">
          Our Top Research
        </h2>

        {/* Row 2: Paragraph */}
        <p className="text-gray-700 text-center max-w-6xl mx-auto mb-12">
          At Jamal Mohamed College, research drives innovation and excellence. Our faculty and students are actively involved in impactful research across various fields. Here are a few of our top research highlights that reflect our commitment to academic and societal growth
        </p>

        {/* Row 3: Research Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 md:gap-6 mb-12">
          {researchCards.map((card, index) => (
            <div
              key={index}
              className="relative h-[450px] sm:h-[450px] md:h-[480px] w-full max-w-[350px] mx-auto rounded-3xl overflow-hidden shadow-md border border-custom-green"
              style={{
                backgroundImage: `url(${card.image})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat'
              }}
            >
              {/* Dark overlay for better text visibility */}
              {/* <div className="absolute inset-0 bg-gradient-to-t from-black via-black/20 to-transparent"></div> */}

              {/* Content container positioned at bottom */}
              <div className="absolute bottom-0 left-0 w-full p-3 sm:p-4 text-white">
                <h3 className="text-xl sm:text-xl font-semibold mb-1 sm:mb-4">{card.name}</h3>
                <p className="text-white/90 text-md mb-3 sm:mb-4 line-clamp-3">{card.description}</p>
                <button className="bg-white text-custom-green px-14 py-2 rounded-full text-xl sm:text-base font-bold">
                  View more
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Row 4: View All Button */}
        <div className="flex justify-center">
          <button className="text-custom-green px-8 py-3 rounded-full font-bold  border-2 border-custom-green hover:text-white hover:bg-custom-green">
            View All researches
          </button>
        </div>
      </div>
    </section>
  );
};

export default TopResearch;