import { defineType, defineField } from "sanity";

export default defineType({
  name: "courses",
  title: "Courses",
  type: "document",
  fields: [
    defineField({
      name: "departmentName",
      title: "Department Name",
      type: "string",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "slug",
      title: "Slug",
      type: "slug",
      options: {
        source: "departmentName",
        maxLength: 96,
        slugify: (input: string) =>
          input
            .toLowerCase()
            .replace(/\s+/g, "-")
            .replace(/[^\w\-]+/g, "")
            .slice(0, 96),
      },
      validation: (Rule) => Rule.required(),
    }),
    {
      name: "studyLevel",
      title: "Study Level",
      type: "array",
      of: [{ type: "string" }],
      options: {
        list: [
          "Undergraduate",
          "Postgraduate",
          "M.Phil",
          "PhD",
          "PG Diploma",
          "Diploma",
          "Certification Course",
        ],
        layout: "list",
      },
      validation: (Rule) => Rule.required(),
    },
    {
      name: "studyPattern",
      title: "Study Pattern",
      type: "string",
      options: {
        list: ["Full Time", "Part Time"],
        layout: "radio",
      },
    },
    defineField({
      name: "fieldOfStudy",
      title: "Field of Study",
      type: "string",
      validation: (Rule) => Rule.required(),
    }),
    {
      name: "tags",
      title: "Tags",
      type: "array",
      of: [{ type: "string" }],
      options: {
        layout: "tags",
      },
    },
    // Adding programmes directly within courses
    {
      name: "programmes",
      title: "Programmes",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            defineField({
              name: "name",
              title: "Programme Name",
              type: "string",
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: "sections",
              title: "Sections",
              type: "string",
              validation: (Rule) => Rule.required(),
            }),
            defineField({
              name: "syllabus",
              title: "Syllabus",
              type: "array", // Changed from 'url' to 'array'
              of: [           // Defines the items of the array
                {
                  type: "object",
                  fields: [
                    defineField({
                      name: "year", // Or 'name', 'title', etc.
                      title: "Syllabus Year/Name",
                      type: "string",
                      validation: (Rule) => Rule.required(),
                    }),
                    defineField({
                      name: "link",
                      title: "PDF Link",
                      type: "url",
                      validation: (Rule) => Rule.required(),
                    }),
                  ],
                  // Optional: Preview configuration for Sanity Studio
                  preview: {
                    select: {
                      title: 'year',
                      subtitle: 'link'
                    }
                  }
                }
              ],
              validation: (Rule) => Rule.required().min(1), // Ensure at least one syllabus entry
            }),
          ],
        },
      ],
    },
    // New field for Programme and Course Outcomes
    defineField({
      name: "programmesAndOutcomes",
      title: "Programme and Course Outcomes",
      type: "object",
      fields: [
        defineField({
          name: "title",
          title: "Title",
          type: "string",
          // Removed validation: (Rule) => Rule.required() to make it optional
        }),
        defineField({
          name: "pdfUrl",
          title: "PDF URL",
          type: "url",
          validation: (Rule) => Rule.required(),
        }),
      ],
      // Optional: Add a condition to show this field only if needed, or make it non-required
      // validation: (Rule) => Rule.custom((value, context) => {
      //   // Example: make it required only if some other condition is met
      //   // or simply Rule.required() if it should always be present
      //   return true; 
      // }),
    }),
    {
      name: "overview",
      title: "Overview",
      type: "reference",
      to: [{ type: "overview" }],
    },
    // Adding faculty reference to courses
    {
      name: "faculty",
      title: "Faculty Members",
      type: "array",
      of: [
        {
          type: "reference",
          to: [{ type: "faculty" }],
        },
      ],
    },
    {
      name: "valueAddedCourses",
      title: "Value Added Courses",
      type: "array",
      of: [
        {
          type: "reference",
          to: [{ type: "valueAddedCourse" }], // Reference the new schema
        },
      ],
    },
    {
      name: "eContent",
      title: "E-Content",
      type: "array",
      of: [
        {
          type: "reference",
          to: [{ type: "eContent" }], // Reference the new schema
        },
      ],
    },
    {
      name: "departmentActivities", // Add this new field
      title: "Department Activities",
      type: "array",
      of: [
        {
          type: "reference",
          to: [{ type: "departmentActivity" }], // Reference the new schema
        },
      ],
    },
    {
      name: "recruitersImage",
      title: "Recruiters Images",
      type: "array",
      description: "Paste full S3 image URLs (including https://)",
      of: [{
        type: "url",
        validation: Rule => Rule.uri({
          scheme: ['http', 'https']
        })
      }],
      options: {
        layout: "tags"
      }
    },
  ],
});
