"use client";

import Image from "next/image";

const DayCareCentre = () => {
  const images = [
    "https://jmc.edu/images/facilities/care.jpg",
    "https://jmc.edu/images/facilities/care2.jpg",
  ];

  const daycareFeatures = [
    "Neat and Clean Environment",
    "Age Limit ( 1 to 3 years)",
    "Safe and Playable Environment",
    "Three dedicated Staff",
    "Main Special function in providing mother room for feeding",
    "Special care is given to each and every individual kid",
  ];
  return (
    <section className="bg-white pb-10 px-4" id="students-counselling-centre">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green text-center">Day Care Centre</h2>
        <div className="max-w-5xl mx-auto grid lg:grid-cols-2 gap-6 items-start">
          {/* Left: Images */}
          <div className="space-y-4">
            {images.map((img, idx) => (
              <div
                key={idx}
                className="relative w-full h-[300px] rounded-xl overflow-hidden shadow-md"
              >
                <Image
                  src={img}
                  alt={`daycare ${idx + 1}`}
                  fill
                  className="w-full object-cover"
                />
              </div>
            ))}
          </div>

          <div className="bg-white border border-gray-200 p-6 rounded-xl flex items-center justify-center h-full">
            <div className="text-center">
              <h3 className="text-left text-lg font-semibold text-custom-green mb-2 font-poppins">
                Key Features and Facilities
              </h3>
              <ul className="font-poppins pl-6 list-disc list-outside text-custom-new-green space-y-1 text-left inline-block">
                {daycareFeatures.map((features, idx) => (
                  <li key={idx}>{features}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DayCareCentre;
