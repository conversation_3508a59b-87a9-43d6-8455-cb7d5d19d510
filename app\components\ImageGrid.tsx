"use client";

import Image from "next/image";

interface ImageGridItem {
  name: string;
  alt: string;
  image: {
    asset: {
      url: string;
    };
  };
}

interface ImageGridProps {
  title?: string;
  description?: string;
  items: ImageGridItem[];
}

const ImageGrid = ({ title, description, items }: ImageGridProps) => {
  return (
    <div className="container max-w-7xl mx-auto space-y-6">
      <h2 className="text-custom-green text-center">{title}</h2>
      {description && (
        <p className="text-custom-new-green text-center">{description}</p>
      )}
      <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3">
        {items.map((item, idx) => (
          <div
            key={idx}
            className="w-full lg:w-96 h-48 sm:h-60 md:h-72 rounded-2xl overflow-hidden shadow-md group relative"
          >
            <Image
              src={item?.image?.asset?.url}
              alt={item?.alt || item.name}
              width={500}
              height={300}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-green-800 to-transparent p-4 font-ramilas text-white font-bold text-center text-lg">
              {item.name}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ImageGrid;
