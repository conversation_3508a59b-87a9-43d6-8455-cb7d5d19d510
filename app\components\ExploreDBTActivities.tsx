import React from "react";

interface ExploreDBTActivitiesProps {
  title?: string;
  description?: string;
  buttonText?: string;
  buttonLink?: string;
  backgroundColor?: string;
}

const ExploreDBTActivities: React.FC<ExploreDBTActivitiesProps> = ({
  title = "Explore DBT Activities",
  description = "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.",
  buttonText = "Click Here To Check",
  buttonLink = "#",
  backgroundColor = "bg-[#D1D9D1]"
}) => {
  const handleButtonClick = () => {
    if (buttonLink) {
      window.open(buttonLink, '_blank');
    }
  };

  return (
    <section className={`pt-16 pb-8 my-12 ${backgroundColor} relative`}>
      <div className="container max-w-7xl mx-auto px-4">
        {/* Title and Description */}
        <div className="text-center mb-8">
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-custom-green mb-6">
            {title}
          </h2>
          <p className="text-gray-700 text-base md:text-lg leading-relaxed max-w-7xl mx-auto lg:mx-0">
            {description}
          </p>
        </div>
      </div>

      {/* Button positioned absolutely at the bottom edge, above component joining */}
      <div className="absolute bottom-0 left-0 right-0 z-10 transform translate-y-1/2">
        <div className="container max-w-7xl mx-auto px-4">
          <div className="flex justify-end lg:justify-end lg:pr-8">
            <button
              onClick={handleButtonClick}
              className="bg-custom-green text-white px-8 py-4 md:px-16 md:py-6 rounded-lg font-semibold text-lg hover:bg-[#004D00] transition-all duration-300 hover:scale-105 shadow-md hover:shadow-lg"
            >
              {buttonText}
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ExploreDBTActivities;
