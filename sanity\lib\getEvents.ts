import { client } from "@/lib/sanity"; // Adjust path if necessary

// No need for urlFor if using direct URLs

export interface Event {
  _id: string;
  title: string;
  slug: string;
  date: string;
  description: string;
  imageUrl: string; // This will now directly hold the S3 URL
  altText: string;
}

export async function getEvents(): Promise<Event[]> {
  // Updated query to fetch imageUrl and imageAlt directly
  const query = `*[_type == "event"] | order(date desc) {
    _id,
    title,
    "slug": slug.current,
    date,
    description,
    imageUrl, // Fetch the URL field
    imageAlt  // Fetch the alt text field
  }`;

  const results = await client.fetch(query);
  console.log("Fetched events results (with direct URLs): ", results);

  // Map the results - simpler now as fields match interface
  return results.map((event: any) => ({
    _id: event._id,
    title: event.title || 'Untitled Event',
    slug: event.slug || '',
    date: event.date,
    description: event.description || 'No description available.',
    imageUrl: event.imageUrl || '/placeholder.png', // Use the fetched URL, provide fallback
    altText: event.imageAlt || event.title || 'Event image' // Use the fetched alt text
  }));
}