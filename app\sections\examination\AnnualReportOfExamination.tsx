"use client";

import { client } from "@/lib/sanity";
import { ANNUAL_REPORTS_QUERY } from "@/sanity/lib/queries";
import { useEffect, useState } from "react";

type AnnualReport = {
  _id: string;
  period: string;
  pdfUrl: string;
};

const AnnualReportOfExamination = () => {
  const [annualReports, setAnnualReports] = useState<AnnualReport[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(0);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const data = await client.fetch(ANNUAL_REPORTS_QUERY);
        setAnnualReports(data);
      } catch (error) {
        console.error("Error fetching annual reports:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, []);

  return (
    <section
      className="py-10 px-4 bg-[#F7F8F7]"
      id="annual-report-of-examination"
    >
      <div className="container mx-auto max-w-7xl space-y-10">
        <h2 className="text-[#032E02] text-center">
          Annual Report Of Examination
        </h2>
        {isLoading ? (
          <div className="text-center text-custom-green text-xl font-poppins">
            Loading annual reports...
          </div>
        ) : annualReports.length === 0 ? (
          <div className="text-center text-custom-green text-xl font-poppins">
            No annual reports available at the moment.
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-10">
            {annualReports.map((report, index) => (
              <div
                key={report._id}
                className={`group border-2 p-4 rounded-2xl transition-all duration-300 flex flex-col items-center justify-center gap-4 h-[260px] sm:h-[300px] md:h-[320px] lg:h-[340px] cursor-pointer max-w-[220px] sm:max-w-[320px] w-full mx-auto
                  ${
                    selectedIndex === index
                      ? "bg-custom-green text-white border-white"
                      : "bg-[#F7F8F7] text-custom-green shadow-blur-glow hover:bg-custom-green hover:text-white hover:border-custom-green"
                  }
                `}
                onClick={() => setSelectedIndex(index)}
              >
                <div className="space-y-1 text-center">
                  <p className="lg:text-2xl font-poppins font-bold">
                    ANNUAL REPORT
                  </p>
                  <p className="lg:text-2xl font-poppins font-bold">
                    {report.period}
                  </p>
                </div>
                {report.pdfUrl && (
                  <a
                    href={report.pdfUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`${
                      selectedIndex === index
                        ? "flex"
                        : "hidden group-hover:flex"
                    } bg-white text-custom-green hover:bg-white hover:text-custom-green px-12 py-2 rounded-full font-ramilas font-bold transition-all duration-300 mt-6`}
                  >
                    Download
                  </a>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default AnnualReportOfExamination;
