import React from "react";
import Image from "next/image";

const OurRecruiters = () => {
  // const recruiters = [
  //   { name: "Infosys", logo: "/recruiters/infosys_logo,svg" },
  //   { name: "<PERSON>", logo: "/recruiters/microsoft_logo.svg" },
  //   { name: "<PERSON><PERSON><PERSON>", logo: "/recruiters/zoho_logo.svg" },
  //   { name: "Wipro", logo: "/recruiters/wipro.png" },
  //   { name: "Cognizant", logo: "/recruiters/cognizant_logo.svg" },
  //   { name: "TCS", logo: "/recruiters/tcs.png" },
  //   { name: "Accenture", logo: "/recruiters/accenture.png" },
  //   { name: "<PERSON>", logo: "/recruiters/ibm.png" },
  // ];

  // // Duplicate the array for seamless infinite scroll
  // const duplicatedRecruiters = [...recruiters, ...recruiters];

  return (
    <section className="py-8 md:py-16 ">
      {/* Title and Description with padding */}
      <div className="px-4 md:px-8 text-center mb-12">
        {/* Title */}
        <h2 className="text-3xl md:text-5xl font-ramilas text-custom-green mb-6">
          Our Recruiters
        </h2>
        
        {/* Description */}
        <p className="text-gray-700 text-base md:text-lg leading-relaxed px-8">
          At Jamal Mohamed College, we are deeply committed to the career success of our students. Our dedicated placement cell 
          works tirelessly to ensure that students are well-prepared for the professional world.
        </p>
      </div>

      {/* Infinite Scrolling Logos - No padding */}
      {/* <div className="overflow-hidden">
        <div className="flex animate-scroll">
          {duplicatedRecruiters.map((recruiter, index) => (
            <div
              key={index}
              className="flex-shrink-0 mx-8 flex items-center justify-center"
              style={{ minWidth: "150px" }}
            >
              <div className="bg-white rounded-lg p-4 md:p-6 shadow-md hover:shadow-lg transition-shadow duration-300 w-full h-20 md:h-24 flex items-center justify-center">
                <Image
                  src={recruiter.logo}
                  alt={recruiter.name}
                  width={120}
                  height={60}
                  className="object-contain max-w-full max-h-full"
                />
              </div>
            </div>
          ))}
        </div>
      </div> */}

      {/* Big Poster Image */}
      <div className="mb-8 text-center">
        <div className="max-w-4xl mx-auto px-4 md:px-12">
          <Image
            src="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Placement/OurRecruiters/recruiters-2.jpg" // Update with actual image path
            alt="Recruitment Poster"
            width={800}
            height={600}
            priority
            quality={100}
            sizes="(max-width: 768px) 100vw, 800px"
            className="rounded-lg shadow-[0_0_24px_rgba(0,0,0,0.3)] w-full h-auto"
          />
        </div>
      </div>
    </section>
  );
};

export default OurRecruiters;
