"use client";

const GeneralInstructions = () => {
  const instructions = [
    {
      id: 1,
      text: "Please read the Prospectus and Eligibility Criteria before filling the application form and make sure that you are aware of the rules and regulations of the College.",
    },
    {
      id: 2,
      text: "Applicants and their parents/ guardians are requested to familiarize themselves fully with the contents of the prospectus so as to abide by the terms and conditions.",
    },
    {
      id: 3,
      text: "Separate Application Forms may be submitted for each programme in Govt. Aided (Men) or Self Finance (Men) or Self Finance (Women). However, Women Applicants will not be admitted in Govt. Aided Stream.",
    },
    {
      id: 4,
      text: "The college does not collect capitation fee or donation for admission to any courses offered in the college. If anyone promises to get admission on payment of donation, the matter should be brought to the notice of the Principal.",
    },
  ];

  return (
    <section className="px-4 py-8" id="general-instructions">
      <div className="container max-w-5xl mx-auto">
        <h2 className=" text-custom-green text-center mb-8">
          General Instructions Before Filling up the Application Form
        </h2>

        <div className="grid gap-6 md:grid-cols-2">
          {instructions.map((item) => (
            <div
              key={item.id}
              className="rounded-xl p-6 shadow-lg bg-white text-custom-new-green border border-gray-200 transition-all duration-300 hover:bg-custom-green hover:text-white"
            >
              <div className="flex items-baseline gap-2">
                <p className="text-sm md:text-base leading-relaxed">
                  {item.id}.
                </p>
                <p className="text-sm md:text-base leading-relaxed">
                  {item.text}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default GeneralInstructions;
