"use client";
import { useState } from "react";
import Image from "next/image";
import { ChevronDown, ChevronUp } from "lucide-react";

interface FacultyMember {
  _id: string;
  name: string;
  qualifications: string;
  designation?: string;
  email: string;
  profileUrl?: string;
  image?: string; // Changed from object to string?
  category: string;
}

const FacultyTable = ({ faculty }: { faculty: FacultyMember[] }) => {
  const groupedFaculty = faculty.reduce(
    (acc: Record<string, FacultyMember[]>, member) => {
      const category = member.category || "Uncategorized";
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(member);
      return acc;
    },
    {}
  );

  const categories = Object.keys(groupedFaculty);

  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>(
    categories.reduce(
      (acc, category, index) => {
        acc[category] = index === 0; // Only the first category is expanded initially
        return acc;
      },
      {} as Record<string, boolean>
    )
  );

  const toggleGroup = (category: string) => {
    setExpandedGroups((prev) => {
      const newState: Record<string, boolean> = {};
      for (const key of Object.keys(prev)) {
        newState[key] = key === category ? !prev[key] : false;
      }

      return newState;
    });
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      {categories.map((category) => (
        <div
          key={category}
          className="mb-6 border border-custom-green rounded-xl shadow-lg overflow-hidden"
        >
          {/* Category Header (Accordion Trigger) */}
          <div
            className={`flex justify-between items-center px-4 sm:px-6 py-3 sm:py-4 font-semibold text-base sm:text-lg cursor-pointer font-ramilas ${
              expandedGroups[category]
                ? "bg-custom-green text-white"
                : "bg-custom-light-green text-custom-green"
            }`}
            onClick={() => toggleGroup(category)}
          >
            <span>{category}</span>
            {expandedGroups[category] ? (
              <ChevronUp size={20} />
            ) : (
              <ChevronDown size={20} />
            )}
          </div>

          {/* Faculty Grid (Accordion Content) */}
          <div
            className={`transition-all duration-500 ease-in-out overflow-hidden bg-white ${
              expandedGroups[category]
                ? "max-h-[5000px] py-4 sm:py-6 px-4 sm:px-6"
                : "max-h-0 px-4 sm:px-6 py-0"
            }`}
          >
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
              {groupedFaculty[category].map((facultyMember) => (
                <div
                  key={facultyMember._id}
                  className="flex flex-col sm:flex-row gap-4 sm:gap-6 p-4 border rounded-lg transition-all h-full"
                  style={{
                    boxShadow: "0 0 12px rgba(0, 0, 0, 0.2)",
                  }}
                >
                  {/* Image */}
                  <div className="relative w-48 h-48 border rounded-md overflow-hidden flex-shrink-0 mx-auto sm:mx-0">
                    <Image
                      src={
                        facultyMember.image
                          ? facultyMember.image
                          : facultyMember.category === "MEN-AIDED" ||
                              facultyMember.category === "MEN-UNAIDED" // Corrected case here
                            ? "/faculty/men-aided.jpg"
                            : facultyMember.category === "WOMEN-UNAIDED"
                              ? "/faculty/women-unaided.webp"
                              : "/default-image.jpg" // Fallback default
                      }
                      alt={facultyMember.name}
                      fill
                      className="object-cover"
                    />
                  </div>

                  {/* Info */}
                  <div className="flex-1 flex flex-col gap-2 justify-center text-center sm:text-left items-center sm:items-start">
                    <h3 className="font-semibold text-xl sm:text-lg text-gray-800">
                      {facultyMember.name}
                    </h3>
                    <p className="text-sm sm:text-base text-custom-new-green">
                      {facultyMember.qualifications}
                    </p>
                    <p className="text-sm sm:text-base text-custom-new-green">
                      {facultyMember.designation || "Designation not available"}
                    </p>
                    <p className="text-sm sm:text-base text-custom-new-green">
                      e-mail: {facultyMember.email}
                    </p>

                    {facultyMember.profileUrl && (
                      <a
                        href={facultyMember.profileUrl}
                        download
                        className="mt-3 sm:mt-2 px-6 py-2 sm:px-8 sm:py-2 bg-custom-green text-white text-sm sm:text-base rounded-full hover:bg-green-800 transition self-center sm:self-start font-ramilas font-semibold"
                      >
                        View Details
                      </a>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default FacultyTable;
