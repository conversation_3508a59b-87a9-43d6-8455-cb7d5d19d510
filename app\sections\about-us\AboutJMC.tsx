"use client";

import Milestones from "@/app/components/Milestones";
import Stats from "@/app/components/Stats";

const AboutJMC = () => {
  return (
    <section className="py-12" id="about_jmc">
      <div className="space-y-12">
        <div className="px-4 container mx-auto max-w-6xl text-center space-y-4">
          <h2 className="text-custom-green">
            About <PERSON>
            <br className="md:hidden" /> Mohamed College
          </h2>
          <p className="text-[#565656]">
            Founded in 1951, Jamal Mohamed College, Tiruchirappalli, began as an affiliated institution of the University of Madras and later joined Bharathidasan University in 1982. Established as a minority institution, its mission has been to provide quality education to
            the underprivileged. Spanning 87 acres, the college was founded by <PERSON><PERSON> and <PERSON><PERSON><PERSON>. Over the decades, it has earned prestigious recognitions, including:
          </p>
        </div>
        <Milestones className="md:mb-16" />
        <Stats className="mt-10" text_size="md:text-6xl" isModalOpen={false} />
      </div>
    </section>
  );
};

export default AboutJMC;
