"use client";

import CTAComponent from "@/app/components/CTAComponent";
import Footer from "@/app/sections/Footer";
import Scholarships from "@/app/sections/students-amenities/Scholarships";
import Navbar from "@/components/Navbar";
import Image from "next/image";
import Link from "next/link";

const page = () => {
  return (
    <>
      <Navbar fixed={false} border={false} />
      <header
        className="relative mt-4 px-4 py-8 md:py-16 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex justify-between items-center">
          <h1 className="font-ramilas">Student's Corner</h1>
          <ul className="font-poppins flex flex-col md:flex-row md:gap-8 md:pr-16">
            <li className="list-disc">
              <Link href="/">Home</Link>
            </li>
            <li className="list-disc">Student's Amenities</li>
          </ul>
        </div>
      </header>

      <main>
        <section className="py-10 px-4">
          <div className="container max-w-7xl mx-auto text-center space-y-6">
            <h2 className="text-custom-green">Photo Copying Centre</h2>
            <p className="text-custom-new-green">
              At Jamal Mohamed College, we understand the importance of
              providing students with quick and affordable access to essential
              academic resources. Our Photo Copying Centre offers high-quality
              printing, photocopying, and document scanning services at minimal
              costs. Conveniently located on campus, it ensures that students
              can access study materials, notes, and project documentation
              without any hassle.
            </p>
          </div>
        </section>

        {/* stationery section */}
        <section className="bg-custom-green text-white py-10 px-4">
          <div className="max-w-7xl mx-auto flex flex-col md:flex-row items-start md:items-center gap-12">
            {/* Image Section */}
            <div className="flex-shrink-0">
              <div className="rounded-xl overflow-hidden shadow-md">
                <div className="relative w-[350px] md:w-[400px] h-[200px] md:h-[250px]">
                  <Image
                    src="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/StudentsCorner/StudentsAmenities/stationery_store.webp"
                    alt="Stationery Items"
                    fill
                    className="object-cover rounded-lg"
                    sizes="(max-width: 768px) 100vw, 400px"
                  />
                </div>
              </div>
            </div>

            {/* Text Section */}
            <div className="flex-1 space-y-4 text-white">
              <h2>Stationary</h2>
              <p className="text-justify">
                At JMC, we ensure that students have easy access to all the
                essential stationery supplies needed for their academic journey.
                Our stationery amenities are designed to support your day-to-day
                academic, creative, and project requirements.
              </p>
            </div>
          </div>
        </section>

        {/* Internet browsing center */}

        <section className="bg-white py-12 px-4">
          <div className="container max-w-7xl mx-auto text-center space-y-6">
            {/* Title */}
            <h2 className="text-custom-green">Internet Browsing Centre</h2>

            {/* Image */}
            <div className="rounded-xl overflow-hidden shadow-md">
              <div className="relative w-full h-[200px] md:h-[350px]">
                <Image
                  src="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/StudentsCorner/StudentsAmenities/browsing_center.webp"
                  alt="Internet Browsing Centre"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 1024px"
                />
              </div>
            </div>

            <p className="text-custom-new-green">
              In addition to the internet connectivity provided in all
              departments, a dedicated Internet Browsing Centre is available
              adjacent to the Hussainuddin Hall. The centre is equipped with
              high-speed internet and modern computer systems, allowing students
              and staff to research, access academic resources, and stay
              connected.
            </p>
          </div>
        </section>
        <section className="bg-[#CFD7CF] text-white py-10 px-4">
          <div className="max-w-7xl mx-auto flex flex-col md:flex-row items-start md:items-center gap-12">
            {/* Image Section */}
            <div className="flex-shrink-0">
              <div className="rounded-xl overflow-hidden shadow-md">
                <div className="relative w-[350px] md:w-[400px] h-[200px] md:h-[250px]">
                  <Image
                    src="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/StudentsCorner/StudentsAmenities/wifi.webp"
                    alt="free wifi"
                    fill
                    className="object-cover rounded-lg"
                    sizes="(max-width: 768px) 100vw, 400px"
                  />
                </div>
              </div>
            </div>

            {/* Text Section */}
            <div className="flex-1 space-y-4 text-custom-green">
              <h2>Free Wifi</h2>
              <p className="text-justify">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
                eiusmod tempor incididunt ut labore et To support our students'
                learning and digital engagement, Jamal Mohamed College provides
                free campus-wide WiFi access. With reliable and fast internet
                speeds, students can stay connected, access online study
                materials, and collaborate on academic projects from anywhere on
                campus.
              </p>
            </div>
          </div>
        </section>
        <section className="bg-white py-10 px-4">
          <div className="container max-w-7xl mx-auto">
            <CTAComponent />
          </div>
        </section>
        <Scholarships />
      </main>

      <Footer />
    </>
  );
};

export default page;
