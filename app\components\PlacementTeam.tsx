import React from "react";
import Image from "next/image";

const PlacementTeam = () => {
  // Helper function to get appropriate image
  const getImageSrc = (member: any) => {
    if (member.image && member.image.trim() !== "") {
      return member.image;
    } else {
      return member.gender === "female" ? "/faculty/women-unaided.webp" : "/faculty/men-aided.jpg";
    }
  };

  const placementTeam = [
    {
      name: "Dr. PEERBASHA S",
      role: "PLACEMENT COORDINATOR",
      email: "<EMAIL>",
      contact: "Contact No. +91 96004 66890",
      image: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Placement/PlacementTeam/beerbasha.jpg",
      gender: "male"
    },
    {
      name: "Mr. RIYAZ AHMED S",
      role: "PLACEMENT OFFICER",
      email: "<EMAIL>",
      contact: "Contact No. +91 98948 88949",
      image: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Placement/PlacementTeam/Niyas.jpg",
      gender: "male"
    }
  ];

  const placementCellMembers = [
    {
      name: "Mr. MOHAMMED IQBAL Y",
      image: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Placement/PlacementTeam/Iqbal.jpg",
      gender: "male"
    },
    {
      name: "Dr. DEBORAH S",
      image: "", // No image URL - will use placeholder
      gender: "female"
    },
    {
      name: "Dr. MARIS SHINIA CLARISSA",
      image: "", // No image URL - will use placeholder
      gender: "female"
    }
  ];

  const communicationTrainingMembers = [
    {
      name: "Mr. MOHAMED UBADA J",
      role: "PLACEMENT MEMBER",
      image: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Placement/PlacementTeam/Ubada.jpg",
      gender: "male"
    },
    {
      name: "Mr. MOHAMED FAZIL C",
      role: "COMMUNICATION TRAINER",
      image: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Placement/PlacementTeam/Fazil.jpg",
      gender: "male"
    }
  ];

  return (
    <section className="bg-[#D1D9D1]">
      {/* Main wrapper with full width and padding */}
      <div className="relative px-8 md:px-16 py-8 md:py-4 bg-white ">

        {/* Placement Team Section */}
        <div className="mb-12">
          <h2 className="text-3xl md:text-4xl font-ramilas text-custom-green text-center mb-8">
            Placement Team
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 ">
            {placementTeam.map((member, index) => (
              <div key={index} className="bg-white rounded-lg border border-gray-200 px-12 py-6" style={{boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 0 0 1px rgba(0, 0, 0, 0.05)'}}>
                <div className="flex flex-col lg:flex-col xl:flex-row items-center xl:items-center space-y-4 lg:space-y-4 xl:space-y-0 xl:space-x-4">
                  <div className="w-54 h-64 xl:w-54 xl:h-64 rounded-lg overflow-hidden flex-shrink-0">
                    <Image
                      src={getImageSrc(member)}
                      alt={member.name}
                      width={128}
                      height={128}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1 space-y-2 text-center xl:text-left">
                    <div className="w-full bg-custom-green text-center text-white px-3 py-1 rounded-full text-lg font-medium mb-2 inline-block">
                      {member.role}
                    </div>
                    <h3 className="font-bold text-gray-800 text-lg mb-1">{member.name}</h3>
                    <p className="text-gray-600 text-md mb-1">{member.email}</p>
                    <p className="text-gray-600 text-md">{member.contact}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Placement Cell Members Section */}
        <div className="mb-12">
          <h2 className="text-3xl md:text-4xl font-ramilas text-custom-green text-center mb-8">
            Placement Cell Members
          </h2>
          <div >
            {/* First Row - 2 members */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              {placementCellMembers.slice(0, 2).map((member, index) => (
                <div key={index} className="bg-white rounded-lg border border-gray-200 px-12 py-6" style={{boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 0 0 1px rgba(0, 0, 0, 0.05)'}}>
                  <div className="flex flex-col lg:flex-col xl:flex-row items-center xl:items-center space-y-4 lg:space-y-4 xl:space-y-0 xl:space-x-4">
                    <div className="w-54 h-64 rounded-lg overflow-hidden flex-shrink-0 bg-gray-100">
                      <Image
                        src={getImageSrc(member)}
                        alt={member.name}
                        width={128}
                        height={128}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1 space-y-2 text-center xl:text-left">
                      <h3 className="font-bold text-gray-800 text-lg">{member.name}</h3>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Second Row - 1 member centered */}
            <div className="flex justify-center">
              <div className="w-full md:w-1/2">
                <div className="bg-white rounded-lg border border-gray-200 px-12 py-6" style={{boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 0 0 1px rgba(0, 0, 0, 0.05)'}}>
                  <div className="flex flex-col lg:flex-col xl:flex-row items-center xl:items-center space-y-4 lg:space-y-4 xl:space-y-0 xl:space-x-4">
                    <div className="w-54 h-64 rounded-lg overflow-hidden flex-shrink-0 bg-gray-100">
                      <Image
                        src={getImageSrc(placementCellMembers[2])}
                        alt={placementCellMembers[2].name}
                        width={128}
                        height={128}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1 space-y-2 text-center xl:text-left">
                      <h3 className="font-bold text-gray-800 text-lg">{placementCellMembers[2].name}</h3>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Placement Member Communication Training Section */}
        <div className="mb-12">
          <h2 className="text-3xl md:text-4xl font-ramilas text-custom-green text-center mb-8">
            Placement Member Communication Training
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 ">
            {communicationTrainingMembers.map((member, index) => (
              <div key={index} className="bg-white rounded-lg border border-gray-200 px-12 py-6" style={{boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 0 0 1px rgba(0, 0, 0, 0.05)'}}>
                <div className="flex flex-col lg:flex-col xl:flex-row items-center xl:items-center space-y-4 lg:space-y-4 xl:space-y-0 xl:space-x-4">
                  <div className="w-54 h-64 rounded-lg overflow-hidden flex-shrink-0">
                    <Image
                      src={getImageSrc(member)}
                      alt={member.name}
                      width={128}
                      height={128}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="flex-1 space-y-2 text-center xl:text-left">
                    <div className="w-full bg-custom-green text-center text-white px-3 py-1 rounded-full text-lg font-medium mb-2 inline-block">
                      {member.role}
                    </div>
                    <h3 className="font-bold text-gray-800 text-lg mb-1">{member.name}</h3>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

      </div>
    </section>
  );
};

export default PlacementTeam;
