"use client";

import { useState } from "react";
import Link from 'next/link'; // Import Link

const DownloadBrochure = () => {
  const [selectedIndex, setSelectedIndex] = useState(0);

  const cards = [
    {
      title: "Programs Offered",
      year: "2025-2026",
      link: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Admission/Brouchers/PROSPECTUS-2025-2026.pdf",
    },
    {
      title: "Prospectus",
      year: "2025-2026",
      link: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Admission/Brouchers/PROGRAMMES-OFFERED-2025-2026.pdf",
    },
    {
      title: "Eligibility Criteria",
      year: "2025-2026",
      link: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Admission/Brouchers/EILIGIBILITY-CRITERIA-2025-2026.pdf",
    },
  ];

  return (
    <section
      className="relative bg-cover bg-center py-16 px-4"
      style={{ backgroundImage: "url('/download_brochure_bgimage.webp')" }}
    >
      <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>
      <div className="relative z-10 max-w-7xl mx-auto text-center text-white space-y-12">
        <h2 className="text-white">Download Brochure</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {cards.map((card, index) => (
            <div
              key={index}
              className={`group border p-4 rounded-2xl transition-all duration-300 flex flex-col items-center justify-center h-[350px] md:h-[400px] cursor-pointer max-w-[320px] w-full mx-auto
                ${
                  selectedIndex === index
                    ? "bg-white text-custom-green border-custom-green"
                    : "bg-custom-green text-white border-white hover:bg-white hover:text-custom-green hover:border-custom-green"
                }
              `}
              onClick={() => setSelectedIndex(index)}
            >
              <h3 className="text-xl md:text-2xl font-medium mb-2">
                {card.title}
              </h3>
              <p className="font-ramilas text-fluid-h2 mb-6">{card.year}</p>

              {/* Download button: show always when selected, or on hover */}
              {/* Replace <a> with Link */}
              <Link
                href={card.link}
                target="_blank" // Open in new tab
                rel="noopener noreferrer" // Security measure
                className={`${ // Keep existing className logic
                  selectedIndex === index ? "flex" : "hidden group-hover:flex"
                } bg-custom-green text-white hover:bg-white hover:text-custom-green px-8 py-2 rounded-full font-ramilas font-bold transition-all duration-300`}
              >
                Download
              </Link>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default DownloadBrochure;
