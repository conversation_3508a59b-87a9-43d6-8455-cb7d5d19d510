import React from "react";
import Image from "next/image";

const PlacementSection = () => {
    // Array of company logos for the infinite scroll
    const companyLogos = [
        "/homepage/pcompimg1.png",
        "/homepage/pcompimg2.png",
        "/homepage/pcompimg3.png",
        "/homepage/pcompimg4.png",
        "/homepage/pcompimg5.png",
        // "/homepage/pcompimg6.png",
    ];

    return (
        <section className="py-16 bg-custom-light-green">
            <div >
                {/* Row 1: Title */}
                <div className="px-4 md:px-8 mb-6">
                    <h2 className="text-3xl md:text-4xl font-bold text-center text-custom-green">
                        PLACEMENT OPPORTUNITIES
                    </h2>
                </div>

                {/* Row 2: Paragraph */}
                <div className="px-6 md:px-12 mb-12">
                    <p className="text-gray-700 text-center max-w-4xl mx-auto">
                        At Jamal Mohamed College, we are deeply committed to the career success of our students. Our dedicated placement cell works tirelessly to ensure that students are well-prepared for the professional world. We are proud to have strong partnerships with some of the top names in the industry, including Wipro, Zoho, Infosys, and Cognizant. These collaborations provide our students with exclusive placement opportunities, internships, and hands-on industry exposure. With a focus on skill development, career counseling, and interview preparation, we ensure that our students are ready to excel in their chosen fields and take on leadership roles in the global job market.
                    </p>
                </div>

                {/* Row 3: Brand Company Infinite Scroll */}
                <div className="relative overflow-hidden mb-12">
                    <div className="flex animate-scroll space-x-8 py-4">
                        {/* First set of logos */}
                        {companyLogos.map((logo, index) => (
                            <div key={`logo-1-${index}`} className="flex-shrink-0 h-16 w-32 relative">
                                <Image
                                    src={logo}
                                    alt={`Company logo ${index + 1}`}
                                    fill
                                    className="object-contain"
                                />
                            </div>
                        ))}
                        {/* Duplicate set for seamless scrolling */}
                        {companyLogos.map((logo, index) => (
                            <div key={`logo-2-${index}`} className="flex-shrink-0 h-16 w-32 relative">
                                <Image
                                    src={logo}
                                    alt={`Company logo ${index + 1}`}
                                    fill
                                    className="object-contain"
                                />
                            </div>
                        ))}
                        {/* Duplicate set for seamless scrolling */}
                        {companyLogos.map((logo, index) => (
                            <div key={`logo-2-${index}`} className="flex-shrink-0 h-16 w-32 relative">
                                <Image
                                    src={logo}
                                    alt={`Company logo ${index + 1}`}
                                    fill
                                    className="object-contain"
                                />
                            </div>
                        ))}
                    </div>
                </div>

                {/* Row 4: Button */}
                <div className="flex justify-center">
                    <button className="bg-custom-light-green text-custom-green px-12 py-4  w-60 rounded-full font-bold md:font-medium border-2 md:border border-custom-green text-xl">
                        Know More
                    </button>
                </div>
            </div>
        </section>
    );
};

export default PlacementSection;