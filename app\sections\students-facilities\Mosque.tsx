"use client";

import Image from "next/image";

const Mosque = () => {
  const collegeMosque = [
    {
      name: "College Mosque Image 1",
      imageUrl: "https://jmc.edu/images/facilities/jmc-mosque-1.jpg",
    },
    {
      name: "College Mosque Image 2",
      imageUrl: "https://jmc.edu/images/facilities/jmc-mosque-2.jpg",
    },
    {
      name: "College Mosque Image 3",
      imageUrl: "https://jmc.edu/images/facilities/jmc-mosque-3.jpg",
    },
    {
      name: "College Mosque image 4",
      imageUrl: "https://jmc.edu/images/facilities/jmc-mosque-4.jpg",
    },
  ];
  return (
    <section className="bg-white pb-10 px-4" id="mosque">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green text-center">
          Jamal <PERSON> College - Mosque
        </h2>
        <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          {collegeMosque.map((item, idx) => (
            <div
              key={idx}
              className="h-36 sm:h-48 md:h-60 rounded-2xl overflow-hidden shadow-md group relative"
            >
              <Image
                src={item?.imageUrl}
                alt={item?.name}
                width={500}
                height={300}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Mosque;
