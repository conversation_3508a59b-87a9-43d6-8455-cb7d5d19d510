"use client";

import Image from "next/image";
import { useEffect, useState } from "react";
import { client } from "@/lib/sanity";
import { JMC_MOSQUE_QUERY } from "@/sanity/lib/queries";

interface MosqueImage {
  name: string;
  imageUrl: string;
}

interface JmcMosqueData {
  _id: string;
  mosqueImages: MosqueImage[];
  menPrayerHallImages: MosqueImage[];
  womenPrayerHallImages: MosqueImage[];
}

const Mosque = () => {
  const [mosqueImages, setMosqueImages] = useState<MosqueImage[]>([]);

  // Fallback data if no Sanity data
  const fallbackImages = [
    {
      name: "College Mosque Image 1",
      imageUrl: "https://jmc.edu/images/facilities/jmc-mosque-1.jpg",
    },
    {
      name: "College Mosque Image 2",
      imageUrl: "https://jmc.edu/images/facilities/jmc-mosque-2.jpg",
    },
    {
      name: "College Mosque Image 3",
      imageUrl: "https://jmc.edu/images/facilities/jmc-mosque-3.jpg",
    },
    {
      name: "College Mosque image 4",
      imageUrl: "https://jmc.edu/images/facilities/jmc-mosque-4.jpg",
    },
  ];

  useEffect(() => {
    client.fetch(JMC_MOSQUE_QUERY).then((data: JmcMosqueData[]) => {
      if (data.length > 0 && data[0].mosqueImages?.length > 0) {
        // Filter out images with empty URLs
        const validImages = data[0].mosqueImages.filter(img =>
          img.imageUrl && img.imageUrl.trim() !== ""
        );
        setMosqueImages(validImages.length > 0 ? validImages : fallbackImages);
      } else {
        setMosqueImages(fallbackImages);
      }
    });
  }, []);

  const collegeMosque = mosqueImages;
  return (
    <section className="bg-white pb-10 px-4" id="mosque">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green text-center">
          Jamal Mohamed College - Mosque
        </h2>
        <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          {collegeMosque.map((item, idx) => (
            <div
              key={idx}
              className="h-36 sm:h-48 md:h-60 rounded-2xl overflow-hidden shadow-md group relative"
            >
              <Image
                src={item?.imageUrl}
                alt={item?.name}
                width={500}
                height={300}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Mosque;
