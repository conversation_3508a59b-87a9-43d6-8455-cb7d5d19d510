"use client";

import Image from "next/image";
import { useEffect, useState } from "react";
import { client } from "@/lib/sanity";
import { JMC_MOSQUE_QUERY } from "@/sanity/lib/queries";
import ImageSkeleton from "@/app/components/ImageSkeleton";

interface MosqueImage {
  name: string;
  imageUrl: string;
}

interface JmcMosqueData {
  _id: string;
  mosqueImages: MosqueImage[];
  menPrayerHallImages: MosqueImage[];
  womenPrayerHallImages: MosqueImage[];
}

const Mosque = () => {
  const [mosqueImages, setMosqueImages] = useState<MosqueImage[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    client.fetch(JMC_MOSQUE_QUERY).then((data: JmcMosqueData[]) => {
      if (data.length > 0 && data[0].mosqueImages?.length > 0) {
        // Filter out images with empty URLs
        const validImages = data[0].mosqueImages.filter(img =>
          img.imageUrl && img.imageUrl.trim() !== ""
        );
        setMosqueImages(validImages);
      }
      setIsLoading(false);
    }).catch(() => {
      setIsLoading(false);
    });
  }, []);
  return (
    <section className="bg-white pb-10 px-4" id="mosque">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green text-center">
          Jamal Mohamed College - Mosque
        </h2>
        {isLoading ? (
          <ImageSkeleton count={4} />
        ) : mosqueImages.length > 0 ? (
          <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
            {mosqueImages.map((item, idx) => (
              <div
                key={idx}
                className="h-36 sm:h-48 md:h-60 rounded-2xl overflow-hidden shadow-md group relative"
              >
                <Image
                  src={item.imageUrl}
                  alt={item.name}
                  width={500}
                  height={300}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-8">
            <p>No mosque images available at the moment.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default Mosque;
