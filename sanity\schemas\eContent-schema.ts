const eContentSchema = {
  name: "eContent",
  title: "E-Content",
  type: "document",
  fields: [
    {
      name: "title",
      title: "Title",
      type: "string",
      description: "Title of the E-Content resource",
      validation: (Rule: { required: () => any }) => Rule.required(),
    },
    {
      name: "contentUrl",
      title: "Content URL / Link",
      type: "url", // Or type: 'file'
      description: "Link to the E-Content resource (PDF, webpage, video, etc.)",
      validation: (Rule: { required: () => any }) => Rule.required(),
    },
    {
      name: "author", // New field
      title: "Author",
      type: "string",
      description: "Name of the author or creator",
      // validation: Rule => Rule.required() // Uncomment if author is mandatory
    },
    // Optional: Add a reference back to the course if needed
    // {
    //   name: 'course',
    //   title: 'Associated Course',
    //   type: 'reference',
    //   to: [{ type: 'course' }]
    // }
  ],
  preview: {
    select: {
      title: 'title',
      subtitle: 'author' // Show author in preview subtitle
    }
  }
};

export default eContentSchema;