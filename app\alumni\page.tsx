// app/alumni-showcase/page.tsx
import React from 'react';

import AlumniHeader from '@/app/sections/alumni/AlumniHeader';
import AlumniNewsBanner from '@/app/sections/alumni/AlumniNewsBanner';
import AlumniWelcomeSection from '@/app/sections/alumni/AlumniWelcomeSection';
import AlumniResponsibilities from '@/app/sections/alumni/AlumniResponsibilities';
import AlumniChapters from '@/app/sections/alumni/AlumniChapters';
import ContactUsBanner from '@/app/sections/alumni/ContactUsBanner';
import AlumniFooter from '@/app/sections/alumni/AlumniFooter';
import AlumniStats from '@/app/sections/alumni/AlumniStats';


const AlumniShowcasePage = () => {
  return (
    <>
      <AlumniHeader />
      <main className="flex-grow">
        <AlumniNewsBanner />
        <AlumniWelcomeSection />
        <AlumniResponsibilities />
        <AlumniStats />
        <AlumniChapters />
        <ContactUsBanner />
      </main>
      <AlumniFooter />
    </>
  );
};

export default AlumniShowcasePage;