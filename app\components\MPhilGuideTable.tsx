import React from "react";

export interface MPhilGuideData {
  id: number;
  name: string;
  qualification: string;
  designation: string;
  communicationNo: string;
}

export interface DepartmentData {
  departmentName: string;
  members: MPhilGuideData[];
}

interface MPhilGuideTableProps {
  departments: DepartmentData[];
}

const MPhilGuideTable: React.FC<MPhilGuideTableProps> = ({
  departments = [],
}) => {
  // Define column widths as variables to ensure consistency
  const columnWidths = {
    id: "8%",
    name: "22%",
    qualification: "22%",
    designation: "18%",
    communication: "30%",
  };

  return (
    <div className="space-y-8">
      {departments.map((department, index) => (
        <div key={`${department.departmentName}-${index}`} className="mb-8">
          <div className="text-[#002E00] text-center py-4 font-semibold text-xl">
            {department.departmentName}
          </div>

          <div className="bg-[#D1D9D1] rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <div className="min-w-[768px] lg:min-w-0">
                {/* Header with rounded style */}
                <div className="mx-2 sm:mx-4 mb-2">
                  <div className="bg-[#002E00] text-white rounded-full overflow-hidden">
                    <div className="grid grid-cols-5" style={{
                      gridTemplateColumns: `${columnWidths.id} ${columnWidths.name} ${columnWidths.qualification} ${columnWidths.designation} ${columnWidths.communication}`
                    }}>
                      <div className="py-3 sm:py-4 px-2 sm:px-6 text-left font-medium text-xs sm:text-base">
                        S.NO
                      </div>
                      <div className="py-3 sm:py-4 px-2 sm:px-6 text-left font-medium text-xs sm:text-base whitespace-normal break-words">
                        NAME OF THE FACULTY
                      </div>
                      <div className="py-3 sm:py-4 px-2 sm:px-6 text-left font-medium text-xs sm:text-base whitespace-normal break-words">
                        QUALIFICATION
                      </div>
                      <div className="py-3 sm:py-4 px-2 sm:px-6 text-left font-medium text-xs sm:text-base whitespace-normal break-words">
                        DESIGNATION
                      </div>
                      <div className="py-3 sm:py-4 px-2 sm:px-6 text-left font-medium text-xs sm:text-base whitespace-normal break-words">
                        M.PHIL GUIDESHIP COMMUNICATION NO.
                      </div>
                    </div>
                  </div>
                </div>

                {/* Table content */}
                {department.members.map((member) => (
                  <div
                    key={member.id}
                    className="grid grid-cols-5 border-b border-[#555555]"
                    style={{
                      gridTemplateColumns: `${columnWidths.id} ${columnWidths.name} ${columnWidths.qualification} ${columnWidths.designation} ${columnWidths.communication}`
                    }}
                  >
                    <div className="py-2 sm:py-3 px-4 sm:px-12 text-left text-[#002E00] font-medium text-xs sm:text-base">
                      {member.id}
                    </div>
                    <div className="py-2 sm:py-3 px-2 sm:px-6 text-left text-[#555555] text-xs sm:text-base whitespace-normal break-words">
                      {member.name}
                    </div>
                    <div className="py-2 sm:py-3 px-2 sm:px-6 text-left text-[#555555] text-xs sm:text-base whitespace-normal break-words">
                      {member.qualification}
                    </div>
                    <div className="py-2 sm:py-3 px-2 sm:px-6 text-left text-[#555555] text-xs sm:text-base whitespace-normal break-words">
                      {member.designation}
                    </div>
                    <div className="py-2 sm:py-3 px-2 sm:px-6 text-left text-[#555555] text-xs sm:text-base whitespace-normal break-words">
                      {member.communicationNo}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default MPhilGuideTable;
