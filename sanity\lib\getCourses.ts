import { cache } from "react";
import { client } from "@/lib/sanity";

export const getCourses = cache(async () => {
  return await client.fetch(`*[_type == "courses"]{
    _id,
    departmentName,
    "description": overview->description,
    "slug": slug.current,
    studyLevel,
    studyPattern,
    fieldOfStudy,
    tags
  }`);
});

export async function getCourseBySlug(slug: string) {
  const query = `*[_type == "course" && slug.current == $slug][0]{
    _id,
    departmentName,
    description,
    studyLevel,
    studyPattern,
    tags,
    slug
  }`;

  const course = await client.fetch(query, { slug });
  return course;
}
