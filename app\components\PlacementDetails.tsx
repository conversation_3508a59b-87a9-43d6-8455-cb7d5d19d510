"use client";

import React from "react";

const PlacementDetails = () => {
  const yearButtons = [
    { year: "2017 - 2018", path: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Placement/PlacementDetails/Placement-Companies-Details-17-18.pdf" },
    { year: "2018 - 2019", path: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Placement/PlacementDetails/Placement-Companies-Details-18-19.pdf" },
    { year: "2019 - 2020", path: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Placement/PlacementDetails/Placement-Companies-Details-19-20.pdf" },
    { year: "2020 - 2021", path: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Placement/PlacementDetails/Placement-Companies-Details-20-21.pdf" },
    { year: "2021 - 2022", path: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Placement/PlacementDetails/Placement-Companies-Details-21-22.pdf" },
  ];

  const handleButtonClick = (path: string) => {
    window.open(path, "_blank");
  };

  return (
    <section className="pb-12 bg-white">
      <div className="px-4 md:px-8 text-center">
        {/* Title */}
        <h2 className="text-3xl md:text-4xl font-ramilas text-custom-green mb-12">
          Placement Details
        </h2>
        
        {/* Description */}
        <p className="text-gray-700 text-base md:text-lg leading-relaxed mb-12 px-8">
          Discover our Value Added Courses designed to enhance your skills and knowledge. Download year-wise resources from 2017 
          to 2024 to stay updated and excel in your field.
        </p>
        
        {/* Year Buttons */}
        <div className="flex flex-wrap justify-center gap-4">
          {yearButtons.map((year, index) => (
            <button
              key={index}
              className="px-8 py-4 bg-custom-green text-white font-medium rounded-lg hover:bg-green-800 transition-colors duration-200 text-sm md:text-lg"
              onClick={() => handleButtonClick(year.path)}
            >
              {year.year}
            </button>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PlacementDetails;
