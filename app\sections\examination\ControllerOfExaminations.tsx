"use client";

import { ProfileCard } from "@/app/components/ProfileCard";
import Image from "next/image";

const ControllerOfExaminations = () => {
  const profiles = [
    {
      name: "Dr. <PERSON><PERSON>",
      role: "Controller of Examinations",
      image: "https://jmc.edu/images/jmc/examination/coe.jpg",
    },
    {
      name: "<PERSON><PERSON> <PERSON><PERSON>",
      role: "Deputy Controller of Examinations",
      image: "https://jmc.edu/images/jmc/examination/coe1.jpg",
    },
    {
      name: "Dr. <PERSON><PERSON><PERSON><PERSON>",
      role: "Assistant Controller of Examinations",
      image: "https://jmc.edu/images/jmc/examination/coe2.jpg",
    },
  ];
  return (
    <section
      className="bg-[#F7F8F7] py-10 px-4"
      id="controller-of-examinations"
    >
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green text-center">
          Controller of Examinations
        </h2>
        <div className="grid gap-12">
          {profiles.length > 0 ? (
            profiles.map((profile, index) =>
              profile && typeof profile === "object" ? (
                <div
                  key={index}
                  className="flex flex-col md:flex-row items-center text-left gap-6 md:gap-10"
                >
                  <div className="w-52 h-52 shrink-0 rounded-xl overflow-hidden mx-auto md:mx-0">
                    <Image
                      src={profile.image}
                      alt={profile.name}
                      width={192}
                      height={192}
                      className="object-cover object-top w-full h-full"
                    />
                  </div>
                  <div className="flex-1 space-y-2 text-center md:text-justify">
                    <h3 className="text-custom-green font-ramilas font-medium">
                      {profile.name}
                    </h3>
                    <p className="text-custom-new-green">
                      <strong className="font-semibold">{profile.role}</strong>
                    </p>
                  </div>
                </div>
              ) : null
            )
          ) : (
            <p className="text-gray-500">
              No profiles available for this category.
            </p>
          )}
        </div>
      </div>
    </section>
  );
};

export default ControllerOfExaminations;
