import { client } from "./client";

// Interface matching the schema fields you want to fetch
export interface NoticeUpdate {
  _id: string;
  text: string;
  category: string;
  imageUrl?: string; // Optional
  imageAlt?: string; // Optional
  // publishedDate?: string; // Optional
}

// Async function to fetch notices/updates
export async function getNoticeUpdates(): Promise<NoticeUpdate[]> {
  const query = `*[_type == "noticeUpdate"] | order(_createdAt desc) {
    _id,
    text,
    category,
    imageUrl,
    imageAlt
    // publishedDate
  }`;

  try {
    const updates = await client.fetch(query);
    return updates;
  } catch (error) {
    console.error("Failed to fetch notice updates:", error);
    return []; // Return empty array on error
  }
}