import { Play } from "lucide-react";
import { useState } from "react";

const CollegeTourSection = () => {
  const [isPlaying, setIsPlaying] = useState(false);

  const handlePlay = () => {
    setIsPlaying(true);
  };

  return (
    <section
      className="relative px-6 py-28 text-white bg-cover bg-center"
      style={{ backgroundImage: "url('/jamal_college.jpeg')" }}
    >
      {/* Overlay */}
      <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

      <div className="relative max-w-7xl container mx-auto z-10">
        <div className="flex flex-col items-center text-center md:text-left md:items-start md:flex-row justify-between ">
          <div>
            <h3 className="mb-4 font-ramilas text-4xl sm:text-3xl md:text-4xl font-bold">College Tour</h3>
            <p className="max-w-xl mb-8 md:mb-0 font-poppins">
              Here's a quick tour of Jamal Mohamed College experience our vibrant campus and inspiring environment.
            </p>
          </div>
          <div className="flex justify-center items-center gap-4">
            <h3 className="font-ramilas">Play now</h3>
            <button
              className="p-6 rounded-full border-4 border-white"
              onClick={handlePlay}
            >
              <Play size={32} fill="white" />
            </button>
          </div>
        </div>
        {isPlaying && (
          <div className="mt-8 w-full aspect-video">
            <iframe
              className="w-full h-full"
              src="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Videos/Home/JMC.mp4"
              title="Sample Video"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            ></iframe>
          </div>
        )}
      </div>
    </section>
  );
};

export default CollegeTourSection;
