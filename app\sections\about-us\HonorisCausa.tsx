"use client";
import TableCommon from "@/app/components/TableCommon";
import Image from "next/image";

const HonorisCausa = () => {
  const presidents = {
    title: "Our Presidents",
    headers: ["Presidents", "Year"] as [string, string],
    members: [
      { name: "<PERSON><PERSON><PERSON>", year: "1951 to 1954" },
      { name: "<PERSON><PERSON><PERSON>", year: "1954 to 1975" },
      { name: "<PERSON><PERSON>", year: "1975 to 1989" },
      { name: "<PERSON><PERSON>", year: "1989 to 1990" },
      { name: "<PERSON><PERSON>", year: "1990 to 1995" },
      { name: "<PERSON><PERSON>", year: "1995 to 2014" },
    ],
  };

  const secretaries = {
    title: "Our Secretaries",
    headers: ["Secretaries", "Year"] as [string, string],
    members: [
      { name: "<PERSON><PERSON> <PERSON><PERSON><PERSON>", year: "1951 to 1966" },
      { name: "<PERSON><PERSON> <PERSON><PERSON>", year: "1966 to 1973" },
      { name: "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", year: "1975 to 1989" },
      { name: "<PERSON><PERSON> <PERSON><PERSON>", year: "1973 to 1988" },
      { name: "<PERSON><PERSON> <PERSON>.<PERSON>. <PERSON>", year: "1988 to 1989" },
      { name: "<PERSON><PERSON> <PERSON>.<PERSON>.<PERSON>. <PERSON> <PERSON>af<PERSON> <PERSON>", year: "1989 to 2011" },
    ],
  };

  const treasurers = {
    title: "Our Treasurers",
    headers: ["Treasurers", "Year"] as [string, string],
    members: [
      { name: "<PERSON>b N.M.<PERSON>. <PERSON> Samad <PERSON>", year: "1951 to 1964" },
      { name: "<PERSON>jee N.<PERSON>.K. Abdul Kader Sahib", year: "1964 to 1973" },
      { name: "Hajee M.J.M. Abdul Gafoor Sahib", year: "1973 to 1989" },
      { name: "Hajee K.A. Khaleel Ahamed Sahib", year: "1989 to 2017" },
    ],
  };

  const assistantSecretaries = {
    title: "Our Assistant Secretaries",
    headers: ["Assistant Secretaries", "Year"] as [string, string],
    members: [
      { name: "Dr. A.K. Khaja Nazeemudeen", year: "2003 to 2011" },
      { name: "Hajee M.J. Jamal Mohamed", year: "2011 to 2017" },
    ],
  };

  const principalsList = {
    title: "Principals",
    headers: ["Principals", "Year"] as [string, string],
    members: [
      {
        name: "Prof. Hajee M.J. Mohamed Sayeed Sahib, M.A.L.T",
        year: "1951 to 1970",
      },
      { name: "Prof. E.W.P. Thomas, M.A.,", year: "1970 to 1971" },
      { name: "Prof. E.P. Mohamed Ismail, M.Com.,", year: "1971 to 1985" },
      { name: "Dr. C. Nainar Mohamed, M.A., Ph.D.", year: "1985 to 1989" },
      {
        name: "Prof. Hajee N. Abdul Samadh, M.A., M.Phil.,",
        year: "1989 to 1998",
      },
      {
        name: "Dr. K. Abdullah Basha, M.A., M.Phil., Ph.D.,",
        year: "1998 to 2003",
      },
      {
        name: "Dr. M. Sheik Mohamed, M.Com., M.Phil., Ph.D., FICWA, PGDCA, PGDCM, Dip.MA., MBA., M.Phil.,",
        year: "2003 to 2011",
      },
      {
        name: "Dr. R. Khader Mohideen, M.Com., M.Phil., Ph.D., M.B.A.,",
        year: "2011 to 2013",
      },
      {
        name: "Dr. A.M. Mohamed Sindhasha, M.Com., M.Sc., (Psy)., M.Phil., M.B.A., Ph.D.,",
        year: "2013 to 2014",
      },
      {
        name: "Dr. S. Mohamed Saliqu, M.Sc., B.Ed., Dip. in Arabic, Ph.D.,",
        year: "2014 to 2017",
      },
      {
        name: "Dr. S. Ismail Mohideen, M.Sc., M.Phil., P.G.D.C.A., Ph.D.,",
        year: "2017 to 2024",
      },
    ],
  };

  return (
    <section id="honoris_causa">
      <section
        className="relative text-white bg-cover bg-center px-4 py-8"
        style={{ backgroundImage: "url('/jamal_college.jpeg')" }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative p-4 container mx-auto z-10 max-w-6xl lg:max-w-7xl">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div>
              <h2 className="mb-4">Honoris Causa</h2>
              <p className="max-w-3xl mb-8 md:mb-0 font-poppins">
                Founded in 1951, Jamal Mohamed College, Tiruchirappalli, began
                as an affiliated institution of the University of Madras and
                later joined Bharathidasan University in 1982. Established as a
                minority institution, its mission has been to provide quality
                education to the underprivileged.
              </p>
            </div>
            {/* Images Section */}
            <div className="flex flex-row justify-center gap-8">
              <div className="flex flex-col items-center">
                <Image
                  src="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/AboutUs/HonorisCausa/01.jpeg"
                  alt="Hajee M. Jamal Mohamed Sahib"
                  width={160}
                  height={200}
                  className="rounded-lg shadow-lg object-cover"
                />
                <p className="mt-2 text-sm md:text-base text-center whitespace-pre-line">
                  Hajee M. Jamal{"\n"}Mohamed Sahib
                </p>
              </div>

              <div className="flex flex-col items-center">
                <Image
                  src="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/AboutUs/HonorisCausa/02.jpeg"
                  alt="Janab N.M. Khajamian Rowther"
                  width={160}
                  height={200}
                  className="rounded-lg shadow-lg object-cover"
                />
                <p className="mt-2 text-sm md:text-base text-center whitespace-pre-line">
                  Janab N.M.{"\n"}Khajamian Rowther
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section className="bg-white px-4 py-8 space-y-8">
        <TableCommon data={presidents} />
        <TableCommon data={secretaries} />
        <TableCommon data={treasurers} />
        <TableCommon data={assistantSecretaries} />
        <TableCommon data={principalsList} />
      </section>
    </section>
  );
};

export default HonorisCausa;
