import React from 'react';

interface AdmissionQueryCardsProps {
  className?: string;
}

const AdmissionQueryCards: React.FC<AdmissionQueryCardsProps> = ({ className = "" }) => {
  return (
    <div className={`w-full ${className}`}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">

        {/* First Card */}
        <div className="bg-custom-light-green rounded-2xl p-6 sm:p-8 shadow-lg border border-custom-green">
          <h3 className="text-xl sm:text-2xl font-bold text-custom-green mb-4 sm:mb-6 font-ramilas">
            For Admission Related Query
          </h3>

          <div className="space-y-3 sm:space-y-4">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-5 h-5 mt-0.5">
                <svg className="w-full h-full text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M2 3.5A1.5 1.5 0 013.5 2h1.148a1.5 1.5 0 011.465 1.175l.716 3.223a1.5 1.5 0 01-1.052 1.767l-.933.267c-.41.117-.643.555-.48.95a11.542 11.542 0 006.254 6.254c.395.163.833-.07.95-.48l.267-.933a1.5 1.5 0 011.767-1.052l3.223.716A1.5 1.5 0 0118 15.352V16.5a1.5 1.5 0 01-1.5 1.5H15c-1.149 0-2.263-.15-3.326-.43A13.022 13.022 0 012.43 8.326 13.019 13.019 0 012 5V3.5z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <p className="text-sm sm:text-base text-gray-700 font-medium">Phone:</p>
                <p className="text-sm sm:text-base text-gray-600">0431 - 2331135</p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-5 h-5 mt-0.5">
                <svg className="w-full h-full text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M7 2a2 2 0 00-2 2v12a2 2 0 002 2h6a2 2 0 002-2V4a2 2 0 00-2-2H7zm3 14a1 1 0 110-2 1 1 0 010 2z" />
                </svg>

              </div>
              <div>
                <p className="text-sm sm:text-base text-gray-700 font-medium">Mobile:</p>
                <p className="text-sm sm:text-base text-gray-600">9894738035, 9360983301</p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-5 h-5 mt-0.5">
                <svg className="w-full h-full text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <p className="text-sm sm:text-base text-gray-700 font-medium">Time:</p>
                <p className="text-sm sm:text-base text-gray-600">10 am to 5:30 pm</p>
              </div>
            </div>
          </div>
        </div>

        {/* Second Card */}
        <div className="bg-custom-light-green rounded-2xl p-6 sm:p-8 shadow-lg border border-custom-green">
          <h3 className="text-xl sm:text-2xl font-bold text-custom-green mb-4 sm:mb-6 font-ramilas">
            For Admission Related Query
          </h3>

          <div className="space-y-3 sm:space-y-4">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-5 h-5 mt-0.5">
                <svg className="w-full h-full text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <p className="text-sm sm:text-base text-gray-700 font-medium">Contact:</p>
                <p className="text-sm sm:text-base text-gray-600">9360983102</p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-5 h-5 mt-0.5">
                <svg className="w-full h-full text-custom-green" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <p className="text-sm sm:text-base text-gray-700 font-medium">Time:</p>
                <p className="text-sm sm:text-base text-gray-600">10 am to 5:30 pm</p>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  );
};

export default AdmissionQueryCards;
