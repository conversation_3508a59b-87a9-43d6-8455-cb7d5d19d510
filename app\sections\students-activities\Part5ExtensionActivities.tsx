"use client";

import { useEffect, useState } from "react";
import { Search } from "lucide-react";
import Image from "next/image";
import { client } from "@/lib/sanity";
import { clubsQuery } from "@/sanity/lib/queries";
import ElectoralLiteracyClub from "./ElectoralLiteracyClub";

interface Club {
  _id: string;
  name: string;
  description: string;
  image: string;
  objectives: string[];
  members: string[];
  buttonLabel: string;
}

const Part5ExtensionActivities = () => {
  const [clubsData, setClubsData] = useState<Club[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      const data = await client.fetch(clubsQuery);
      setClubsData(data);
      console.log("part5 clubs data", data);
      
    };
    fetchData();
  }, []);

  return (
    <section className="bg-[#F7F8F7] py-10 px-4">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green text-center">
          Part V Extension Activities
        </h2>

        {clubsData.map((club) => (
          <div
            key={club._id}
            className="bg-white border border-custom-green rounded-xl p-4 md:p-6 flex flex-col md:flex-row gap-6 md:items-start"
          >
            {/* Left: Image and title */}
            <div className="w-full md:w-1/3 space-y-4 flex flex-col h-full md:h-auto">
              <h3 className="text-custom-green">{club.name}</h3>
              <div className="flex-1">
                <div className="h-full">
                  <Image
                    src={club.image}
                    alt={club.name}
                    width={400}
                    height={400}
                    className="w-full h-full object-contain rounded-lg border border-custom-green"
                  />
                </div>
              </div>
            </div>

            {/* Right: Text Content */}
            <div className="flex-1 space-y-4 md:pl-4">
              <div>
                <h4 className="font-ramilas font-medium text-custom-green">
                  Members
                </h4>
                <ul className="list-decimal pl-5 mt-1 text-custom-new-green text-base font-poppins space-y-1">
                  {club.members.map((member, idx) => (
                    <li key={idx}>{member}</li>
                  ))}
                </ul>
              </div>

              <p className="text-custom-new-green text-base">
                {club.description}
              </p>

              <div>
                <h4 className="font-ramilas font-medium text-custom-green">
                  Objectives
                </h4>
                <ol className="list-decimal pl-5 mt-1 text-custom-new-green text-base font-poppins space-y-1">
                  {club.objectives.map((point, idx) => (
                    <li key={idx}>{point}</li>
                  ))}
                </ol>
              </div>

              <button className="min-w-[10rem] sm:w-auto inline-flex items-center justify-between gap-2 bg-white text-custom-green border border-custom-green px-4 py-2 rounded-full shadow hover:bg-green-50 transition text-sm sm:text-lg font-bold font-ramilas">
                <span
                  className="truncate whitespace-nowrap overflow-hidden text-ellipsis block max-w-full"
                  title={club.buttonLabel}
                >
                  {club.buttonLabel}
                </span>
                <Search className="w-4 h-4 flex-shrink-0" />
              </button>
            </div>
          </div>
        ))}

        <ElectoralLiteracyClub />
      </div>
    </section>
  );
};

export default Part5ExtensionActivities;
