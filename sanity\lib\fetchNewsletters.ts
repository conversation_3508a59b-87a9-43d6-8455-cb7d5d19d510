import { client } from "@/lib/sanity";
import { getNewslettersQuery } from "./queries";

export interface Newsletter {
  _id: string;
  name: string;
  category: string;
  url: string;
}

export interface NewsletterGroup {
  [category: string]: Newsletter[];
}

export async function fetchNewsletters(): Promise<NewsletterGroup> {
  const data: Newsletter[] = await client.fetch(getNewslettersQuery);

  return data.reduce<NewsletterGroup>((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = [];
    }
    acc[item.category].push(item);
    return acc;
  }, {});
}
