import { defineType, defineField } from "sanity"; 
import React from 'react';

// Define the category options (excluding "View All")
const categoryOptions = [
  "Circular",
  "News",
  "Downloads",
  "For Students",
  "For College Faculty",
  "Digital Initiative",
];

// Define a type for the parent object to help TypeScript
interface ParentType {
  imageUrl?: string;
}

export default defineType({
  name: "noticeUpdate",
  title: "Notice/Update",
  type: "document",
  fields: [
    defineField({
      name: "text",
      title: "Text/Description",
      type: "string",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "category",
      title: "Category",
      type: "string",
      options: {
        list: categoryOptions.map(category => ({ title: category, value: category })),
        layout: 'dropdown' // Use dropdown input
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "imageUrl",
      title: "Image URL (e.g., from S3)",
      type: "url",
      description: "Paste the full URL of the image.",
      validation: (Rule) => Rule.uri({
        scheme: ['http', 'https']
      })
      // Make it optional if not all updates have images
      // validation: (Rule) => Rule.uri({ scheme: ['http', 'https'] }).optional()
    }),
    defineField({
      name: "imageAlt",
      title: "Image Alt Text",
      type: "string",
      description: "Important for SEO and accessibility. Required if Image URL is provided.",
      // Type hint for parent in hidden remains the same
      hidden: ({ parent }: { parent?: ParentType }) => !parent?.imageUrl,
      // Remove type hint from context parameter, use type assertion inside
      validation: (Rule) => Rule.custom((value, context) => {
        // Assert the type of context.parent here
        const parent = context.parent as ParentType | undefined;
        if (parent?.imageUrl && !value) {
          return "Alt text is required when an image URL is provided.";
        }
        return true;
      })
    }),
    // Optional: Add a date field if needed
    // defineField({
    //   name: 'publishedDate',
    //   title: 'Published Date',
    //   type: 'date',
    //   options: {
    //     dateFormat: 'YYYY-MM-DD',
    //   },
    //   initialValue: () => new Date().toISOString().split('T')[0] // Default to today
    // }),
  ],
  preview: {
    select: {
      title: 'text',
      category: 'category',
      imageUrl: 'imageUrl',
      imageAlt: 'imageAlt'
    },
    prepare(selection) {
      const { title, category, imageUrl, imageAlt } = selection;
      return {
        title: title ? title.substring(0, 50) + '...' : 'No text',
        subtitle: category || 'No category',
        media: imageUrl
          ? React.createElement('img', { src: imageUrl, alt: imageAlt || title || '' })
          : undefined
      }
    }
  }
});