// app/sections/alumni-showcase/AlumniMobileNav.tsx
import React from 'react';
import Link from 'next/link';
import { X } from 'lucide-react';

interface AlumniMobileNavProps {
  isOpen: boolean;
  onClose: () => void;
}

const navItems = [
  { name: 'Home', href: '/alumni-showcase/home', isComingSoon: true },
  { name: 'About', href: '/alumni-showcase/about', isComingSoon: true },
  { name: 'Chapters', href: '/alumni-showcase/chapters', isComingSoon: true },
  { name: 'Awareness', href: '/alumni-showcase/awareness', isComingSoon: true },
  { name: 'Service', href: '/alumni-showcase/service', isComingSoon: true },
  { name: 'Gallery', href: '/alumni-showcase/gallery', isComingSoon: true },
  { name: 'Prominent Alumni', href: '/alumni-showcase/prominent-alumni', isComingSoon: true }, // Assuming Prominent Alumni is ready
];

const AlumniMobileNav: React.FC<AlumniMobileNavProps> = ({ isOpen, onClose }) => {
  // No need to return null here, the parent component AlumniHeader handles conditional rendering.
  // The transform and opacity will handle visibility.

  return (
    <>
      {/* Backdrop - still useful for click-outside-to-close and visual separation */}
      <div
        className={`fixed inset-0 bg-black bg-opacity-60 z-40 transition-opacity duration-300 ease-in-out md:hidden ${
          isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={onClose}
      ></div>

      {/* Mobile Navigation Panel - Full Width, Slides from Top, Full Screen Height */}
      <div
        className={`fixed top-0 left-0 right-0 w-full bg-white shadow-xl z-50 transform transition-all duration-300 ease-in-out md:hidden ${
          isOpen ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
        } h-screen overflow-y-auto`}
      >
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <span className="text-lg font-semibold text-gray-700">Navigation</span>
          <button
            onClick={onClose}
            aria-label="Close navigation menu"
            className="text-gray-600 hover:text-gray-800 p-1 rounded-md hover:bg-gray-100"
          >
            <X size={28} />
          </button>
        </div>
        <nav className="flex flex-col p-6 space-y-3">
          {navItems.map((item) =>
            item.isComingSoon ? (
              <div
                key={item.name}
                className="block px-4 py-3 text-lg text-center text-gray-400 opacity-70 cursor-not-allowed rounded-lg select-none"
              >
                {item.name}{' '}
                <span className="text-sm align-middle">(Coming Soon)</span>
              </div>
            ) : (
              <Link key={item.name} href={item.href} legacyBehavior>
                <a
                  onClick={onClose} // Close nav on link click
                  className="block px-4 py-3 text-lg text-center text-gray-700 hover:bg-green-500 hover:text-white rounded-lg transition-all duration-200 ease-in-out transform hover:scale-105"
                >
                  {item.name}
                </a>
              </Link>
            )
          )}
        </nav>
        <div className="p-6 text-center text-sm text-gray-500">
          <p>&copy; {new Date().getFullYear()} JMC Alumni Association</p>
        </div>
      </div>
    </>
  );
};

export default AlumniMobileNav;
