"use client";

import Link from 'next/link';

const ProgramsOffered = () => {
  return (
    <section
      className="bg-[#CFD7CF] py-10 px-4 shadow-custom-glow relative shadow-stacked-cards rounded-xl border-b border-custom-green"
      id="programs-offered2025-2026"
    >
      <div className="container max-w-7xl mx-auto space-y-6 text-center mb-4 ">
        <h2 className="text-custom-green">Programs Offered 2025-2026</h2>
        <p className="text-custom-new-green">
          Admissions Open 2025-2026 | <PERSON> (Autonomous),
          Tiruchirappalli Accredited with NAAC A++ (CGPA 3.69/4.0), Jamal
          Mohamed College offers a wide range of UG, PG, MBA, and MCA programmes
          across Arts, Science, Commerce, Management, and Computer Science
          streams. Benefit from top-notch academics, scholarships, excellent
          infrastructure, and strong placement support.
        </p>
        <div className="flex justify-center">
          <Link
            href="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Admission/ProgramsOffered2025-2026/PROGRAMMES-OFFERED-2025-2026.pdf"
            target="_blank"
            rel="noopener noreferrer"
          >
            <button className="bg-transparent text-custom-green px-12 py-2 w-60 rounded-full font-semibold border-2 border-custom-green text-xl font-ramilas">
              Know More
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ProgramsOffered;