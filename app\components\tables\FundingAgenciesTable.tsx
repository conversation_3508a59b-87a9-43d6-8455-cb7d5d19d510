import React from "react";

// Define interfaces for our data structure
export interface FundingAgency {
  serialNo: number;
  agencyName: string;
  contactDetails: string;
}

interface FundingAgenciesTableProps {
  agencies: FundingAgency[];
  title?: string;
}

const FundingAgenciesTable: React.FC<FundingAgenciesTableProps> = ({
  agencies,
  title = "Funding Agencies"
}) => {
  return (
    <div className="pt-6 sm:pt-8 bg-[#D1D9D1]">
      <h3 className="text-lg sm:text-xl font-bold text-[#002E00] mb-3 sm:mb-4 px-4 sm:px-8">
        {title}
      </h3>

      <div className="rounded-lg overflow-hidden">
        {/* Add overflow container for mobile responsiveness */}
        <div className="overflow-x-auto">
          <div className="min-w-[768px]"> {/* Minimum width to prevent squishing on mobile */}
            {/* Header with margin */}
            <div className="mx-4 my-4">
              <div className="bg-[#002E00] text-white rounded-full">
                <div className="grid grid-cols-12 w-full">
                  <div className="py-3 sm:py-4 px-3 sm:px-6 text-center font-medium col-span-1 text-xs sm:text-sm md:text-base">SL.NO.</div>
                  <div className="py-3 sm:py-4 px-3 sm:px-6 text-center font-medium col-span-6 text-xs sm:text-sm md:text-base whitespace-normal break-words">FUNDING AGENCY NAME</div>
                  <div className="py-3 sm:py-4 px-3 sm:px-6 text-center font-medium col-span-5 text-xs sm:text-sm md:text-base whitespace-normal break-words">CONTACT DETAILS & WEB SITE</div>
                </div>
              </div>
            </div>

            {/* Agency Entries */}
            <div className="">
              {agencies.map((agency) => (
                <div
                  key={`agency-${agency.serialNo}`}
                  className="grid grid-cols-12 w-full border-b border-[#555555]"
                >
                  <div className="py-2 sm:py-3 pl-8 text-center text-[#555555] col-span-1 text-xs sm:text-sm md:text-base">{agency.serialNo}</div>
                  <div className="py-2 sm:py-3 pl-8 text-left text-[#555555] col-span-6 text-xs sm:text-sm md:text-base whitespace-normal break-words">{agency.agencyName}</div>
                  <div className="py-2 sm:py-3 px-3 sm:px-6 text-left text-[#555555] col-span-5 text-xs sm:text-sm md:text-base whitespace-normal break-words">{agency.contactDetails}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FundingAgenciesTable;
