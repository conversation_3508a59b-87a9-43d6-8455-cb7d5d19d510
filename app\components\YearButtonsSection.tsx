import React from "react";

interface YearButton {
  year: string;
  link?: string;
  disabled?: boolean;
}

interface YearButtonsSectionProps {
  title: string;
  description: string;
  yearButtons: YearButton[];
  backgroundColor?: string;
}

const YearButtonsSection: React.FC<YearButtonsSectionProps> = ({
  title,
  description,
  yearButtons,
  backgroundColor = "bg-white"
}) => {
  const handleButtonClick = (button: YearButton) => {
    if (button.link && !button.disabled) {
      window.open(button.link, '_blank');
    }
  };

  return (
    <section className={`pt-12 lg:pt-16 ${backgroundColor}`}>
      <div className="px-4 sm:px-6">
        {/* Title */}
        <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-center text-[#002E00] mb-4 sm:mb-6">
          {title}
        </h2>

        {/* Description */}
        <p className="text-sm sm:text-base text-center text-[#555555] mb-8 sm:mb-12 max-w-4xl mx-auto leading-relaxed">
          {description}
        </p>

        {/* Year Buttons Grid */}
        <div className="w-full max-w-7xl mx-auto">
          <div className="flex flex-wrap justify-center items-center gap-4 sm:gap-6 md:gap-6">
            {yearButtons.map((button, index) => {
              // Check if this is a long text button (like Download Links)
              const isLongText = button.year.length > 15;

              return (
                <button
                  key={index}
                  onClick={() => handleButtonClick(button)}
                  disabled={button.disabled}
                  className={`
                    px-6 sm:px-8 md:px-8 py-3 sm:py-4 md:py-5
                    rounded-lg
                    font-semibold
                    text-sm sm:text-base md:text-lg
                    transition-all duration-300
                    ${button.disabled
                      ? 'bg-gray-400 text-gray-600 cursor-not-allowed opacity-50'
                      : 'bg-[#002E00] text-white hover:bg-[#004D00] hover:scale-105 cursor-pointer shadow-md hover:shadow-lg'
                    }
                    whitespace-nowrap
                    group
                    relative
                    min-w-fit
                    flex-shrink-0
                    w-[calc(50%-0.5rem)] sm:w-auto
                    ${isLongText
                      ? 'lg:flex-1 lg:min-w-[calc(33.33%-2rem)] lg:max-w-[calc(33.33%-2rem)]'
                      : 'lg:flex-1 lg:min-w-[calc(20%-2rem)] lg:max-w-[calc(20%-2rem)]'
                    }
                  `}
                >
                <span className="flex items-center justify-center gap-2">
                  <span>{button.year}</span>
                  <span className="hidden sm:inline opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0">
                    →
                  </span>
                </span>
              </button>
              );
            })}
          </div>
        </div>
      </div>
    </section>
  );
};

export default YearButtonsSection;
