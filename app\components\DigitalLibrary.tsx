import React from "react";

const DigitalLibrary = () => {

  return (
    <section className="bg-[#D1D9D1]">
      {/* Main wrapper with double shadow effect, full width, and padding */}
      <div className="relative px-4 md:px-24 py-8 md:py-12 shadow-stacked-cards mb-20 rounded-xl border-b border-custom-green">
        {/* Title - Normal content */}
        <div className="text-center mb-8">
          <h2 
            className="text-custom-green mb-8 font-ramilas text-4xl md:text-5xl font-medium leading-none text-center"
          >
            Placement Cell and Training Centre
          </h2>
          <p className="text-gray-700 text-lg leading-relaxed">
            The Placement Cell and Training Centre of our college was established in the year 1998. Since then the cell is providing extensive
            campus training programmes and on-campus recruitment opportunities. JMC has established that consists of well qualified soft skill trainers and Technical trainers. They train the students to improve their employability skills
            by reducing the gap between academic expectations and students skill sets viz., Aptitude, Communication, Personality
            Development, employability skill etc. Training is given to the students within the academic time table in batches. The valuable
            inputs gained help the students in fine-tuning their technical, Managerial and organizational skills.
          </p>
        </div>

        {/* Objective Section - Card with white background */}
        <div className="px-2 md:px-12 xl:px-16">
          <div className="bg-white px-8 py-8 rounded-3xl md:rounded-2xl shadow-lg border border-gray-200">
            <h3 className="text-2xl md:text-3xl font-ramilas text-custom-green mb-6 text-left">
              Objective
            </h3>
            <ul className="space-y-4 px-4 text-xl">
              <li className="flex items-start">
                <span className="text-custom-green font-bold mr-3">•</span>
                <span className="text-gray-700">
                  To upgrade/enhance the student's skills/knowledge based on the organization /
                  current industrial expectations.
                </span>
              </li>
              <li className="flex items-start">
                <span className="text-custom-green font-bold mr-3">•</span>
                <span className="text-gray-700">
                  Aims to place maximum number of students based on their skill set through both
                  On campus and Off-campus interviews.
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DigitalLibrary;
