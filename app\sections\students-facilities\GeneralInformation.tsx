"use client";

import ImageGrid from "@/app/components/ImageGrid";
import { client } from "@/lib/sanity";
import { generalInformationQuery } from "@/sanity/lib/queries";
import Image from "next/image";
import { useEffect, useState } from "react";

const GeneralInformation = () => {
  const [items, setItems] = useState<any[]>([]);

  useEffect(() => {
    client.fetch(generalInformationQuery).then((data) => {
      setItems(data);
    });
  }, []);

  return (
    <section className="bg-white pb-10 px-4">
      <ImageGrid items={items} />
    </section>
  );
};

export default GeneralInformation;
