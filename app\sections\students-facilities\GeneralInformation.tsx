"use client";

import ImageGrid from "@/app/components/ImageGrid";
import { client } from "@/lib/sanity";
import { generalInformationQuery } from "@/sanity/lib/queries";
import { useEffect, useState } from "react";

const GeneralInformation = () => {
  const [items, setItems] = useState<any[]>([]);

  useEffect(() => {
    client.fetch(generalInformationQuery).then((data) => {
      const transformedData = data.map((item: any) => ({
        name: item.name,
        alt: item.alt,
        image: {
          asset: {
            url: item.imageUrl
          }
        }
      }));
      setItems(transformedData);
    });
  }, []);

  return (
    <section className="bg-white pb-10 px-4">
      <h2 className="text-custom-green text-center">Hostel Facilities</h2>
      <ImageGrid items={items} />
    </section>
  );
};

export default GeneralInformation;
