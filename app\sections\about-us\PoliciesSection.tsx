"use client"; // Mark as a Client Component

import { useState, useEffect } from 'react';
import Policies from "@/app/components/Policies";
// Assuming getPolicies.ts is in sanity/lib, adjust if needed
import { getPolicies, PolicyData } from '@/sanity/lib/getPolicies'; 

const PoliciesSection = () => {
  const [policies, setPolicies] = useState<PolicyData[]>([]);
  const [isLoading, setIsLoading] = useState(true); // Manage loading state here

  useEffect(() => {
    const fetchPolicies = async () => {
      setIsLoading(true);
      try {
        // Use the imported getPolicies function
        const fetchedPolicies = await getPolicies(); 
        setPolicies(fetchedPolicies);
      } catch (error) {
        console.error("Failed to fetch policies:", error);
        // Optionally set an error state here
      } finally {
        setIsLoading(false);
      }
    };

    fetchPolicies();
  }, []); // Empty dependency array ensures this runs once on mount

  // Pass the fetched items and loading state to the Policies component
  return (
    <Policies 
      title="Policies" 
      items={policies} // Pass the fetched data
      isLoading={isLoading} // Pass the loading state
      iconType="download" // Keep default or change as needed
    />
  );
};

export default PoliciesSection;
