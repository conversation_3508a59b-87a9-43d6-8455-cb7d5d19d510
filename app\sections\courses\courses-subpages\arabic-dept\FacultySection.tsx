"use client";

import Image from "next/image";

const facultyList = Array.from({ length: 12 }, (_, index) => ({
  name: "DR.A.MOHAMED ISMAIL",
  degrees: "M.A.,M.Phil.,PG.D.C.A., Ph.D.",
  position: "Assistant Professor & Head",
  email: "<EMAIL>",
  image: `/faculty/${index + 1}.jpg`, // Place images inside /public/faculty/
}));

const FacultySection = () => {
  return (
    <section className="bg-white py-10 px-4 max-w-7xl mx-auto text-center space-y-6">
      <h2 className="text-2xl md:text-3xl font-bold text-custom-green">
        Faculty
      </h2>
      <p className="max-w-2xl mx-auto text-[#565656] text-sm md:text-base">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
        tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
        veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip
      </p>

      {/* Department Dropdown */}
      <div className="w-full md:w-64 mx-auto">
        <select className="w-full px-4 py-3 border border-custom-green text-custom-green rounded-lg focus:outline-none">
          <option>MEN-AIDED</option>
          <option>MEN-SELF FINANCE</option>
          <option>WOMEN</option>
        </select>
      </div>

      {/* Faculty Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {facultyList.map((faculty, index) => (
          <div
            key={index}
            className="flex flex-col items-center border rounded-xl shadow-sm hover:shadow-md transition bg-white p-4 space-y-3"
          >
            <div className="w-32 h-32 relative rounded-full overflow-hidden border">
              <Image
                src={faculty.image}
                alt={faculty.name}
                fill
                className="object-cover"
              />
            </div>
            <div className="text-center space-y-1">
              <h3 className="font-bold text-md text-gray-800">
                {faculty.name}
              </h3>
              <p className="text-xs text-gray-500">{faculty.degrees}</p>
              <p className="text-xs text-gray-500">{faculty.position}</p>
              <p className="text-xs text-gray-500">e-mail: {faculty.email}</p>
            </div>
            <button className="mt-2 px-4 py-2 bg-custom-green text-white rounded-lg hover:bg-green-800 transition text-sm">
              View Details
            </button>
          </div>
        ))}
      </div>

      {/* CTA Buttons */}
      <div className="space-y-4 md:space-y-0 md:flex md:justify-center md:gap-4 pt-6">
        <button className="w-full md:w-auto px-6 py-3 border border-custom-green text-custom-green rounded-full hover:bg-custom-green hover:text-white transition">
          PROGRAMME AND COURSE OUTCOMES
        </button>
        <button className="w-full md:w-auto px-6 py-3 border border-custom-green text-custom-green rounded-full hover:bg-custom-green hover:text-white transition">
          PROGRAMME AND COURSE OUTCOMES
        </button>
      </div>
    </section>
  );
};

export default FacultySection;
