"use client";

import ImageGrid from "@/app/components/ImageGrid";
import { client } from "@/lib/sanity";
import { HOSTEL_INFRA_QUERY } from "@/sanity/lib/queries";
import { useEffect, useState } from "react";

const HostelInfrastructure = () => {
  const [blocks, setBlocks] = useState<any[]>([]);

  useEffect(() => {
    client.fetch(HOSTEL_INFRA_QUERY).then((data) => {
      // Transform the data to match ImageGrid component expectations
      const transformedData = data.map((item: any) => ({
        name: item.name,
        alt: item.alt,
        image: {
          asset: {
            url: item.imageUrl
          }
        }
      }));
      setBlocks(transformedData);
    });
  }, []);

  return (
    <section className="bg-white pb-10 px-4">
      <ImageGrid
        title="Hostel Infrastructure"
        description="The Khajamian Hostel features a well-designed campus with multiple
          blocks, including administrative and residential facilities. It
          provides a supportive environment for comfortable living and holistic
          learning."
        items={blocks}
      />
    </section>
  );
};

export default HostelInfrastructure;
