"use client";

import AdmissionCategoriesImageCards from "@/app/components/AdmissionCategoriesImageCards";
import Image from "next/image";
import Link from 'next/link'; // Import Link

const OnlineAdmission = () => {
  return (
    <section className="pt-10 pb-8 px-4 relative" id="online-admission">
      <div className="container max-w-7xl mx-auto space-y-6 mb-4">
        <h4 className="text-custom-green text-center font-ramillas">
          Online Admission 2025 - 2026
        </h4>
        <p className="text-custom-new-green text-center">
          Admissions are now open for the 2025–2026 academic year! At Jamal
          Mohamed College, we offer a wide range of Aided and Unaided programs
          for men, and a variety of Unaided programs for women. Step into a
          vibrant learning environment where academic excellence and personal
          growth go hand in hand. Begin your journey with us today!
        </p>
        <AdmissionCategoriesImageCards />
        <div className="flex justify-center">
          {/* Replace <a> with Link */}
          <Link
            href="https://jmcerp.in/printapplication/"
            target="_blank" // Open in new tab
            rel="noopener noreferrer" // Security measure
            aria-label="Print the Application Form" // Updated aria-label slightly
            className="font-poppins bg-custom-green hover:bg-green-800 text-white font-semibold px-6 md:px-8 py-4 md:py-6 text-lg md:text-xl rounded-2xl shadow-[0px_0px_34px_0px_#00000026] flex items-center gap-4 md:gap-8 lg:gap-12 transition"
          >
            <span>CLICK HERE TO PRINT APPLICATION</span>
            <Image
              src="/courses/download_icon.svg" // Ensure path starts with / if in public folder
              alt="Print Icon" // Changed alt text slightly
              width={24}
              height={24}
            />
          </Link>
        </div>
      </div>
    </section>
  );
};

export default OnlineAdmission;
