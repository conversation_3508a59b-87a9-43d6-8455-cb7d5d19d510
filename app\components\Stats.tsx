"use client";

import { useEffect, useRef } from 'react';
import { motion, useInView, useAnimation, animate } from 'framer-motion';

// Define a type for the Stats props, including isModalOpen
interface StatsProps {
  className?: string;
  text_size?: string;
  isModalOpen: boolean; // Prop from parent
  statsData?: { value: string; label: string }[]; 
}

// Define a type for AnimatedStat props, change isModalOpen to startAnimation
interface AnimatedStatProps {
  value: string;
  text_size?: string;
  startAnimation: boolean; // Renamed prop to trigger animation
}

// Update component to use startAnimation prop
const AnimatedStat: React.FC<AnimatedStatProps> = ({ value, text_size, startAnimation }) => {
  const ref = useRef<HTMLSpanElement>(null);
  // Removed useInView - parent component (Stats) handles this now

  useEffect(() => {
    // Trigger animation only when startAnimation becomes true
    if (startAnimation) {
      let numericValue = 0;
      let suffix = '';
      if (value.endsWith('k')) {
        numericValue = parseFloat(value.replace('k', '')) * 1000;
        suffix = 'k';
      } else {
        numericValue = parseFloat(value);
      }

      const animation = animate(0, numericValue, {
        duration: 2,
        onUpdate(latest) {
          if (ref.current) {
            let displayValue = '';
            if (suffix === 'k' && numericValue >= 1000) {
              displayValue = Math.round(latest / 1000 * 10) / 10 + 'k';
            } else {
              displayValue = Math.round(latest).toLocaleString();
            }
            ref.current.textContent = displayValue;
          }
        },
        onComplete() {
           if (ref.current) {
             ref.current.textContent = value;
           }
        }
      });

      return () => animation.stop();
    }
    // Update dependency array
  }, [startAnimation, value]);

  return (
    <span ref={ref} className={`text-5xl md:text-4xl font-bold text-custom-green ${text_size}`}>
      {/* Show 0 initially, animation will update it when startAnimation is true */}
      {value.endsWith('k') ? '0k' : '0'}
    </span>
  );
};


const Stats: React.FC<StatsProps> = ({ className, text_size, isModalOpen, statsData }) => {
  const stats = statsData ?? [
    { value: "98", label: "Programmes Offered" },
    { value: "483", label: "Teaching Faculties" },
    { value: "10.5k", label: "Students Strength" }
  ];

  // Use useInView here in the parent Stats component
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useInView(ref, { once: true }); // Trigger once when Stats section is visible

  // Determine if animation should start: section must be in view AND modal must be closed
  const shouldStartAnimation = isInView && !isModalOpen;

  const gridCols =
  stats.length === 1
    ? 'grid-cols-1 justify-center'
    : stats.length === 2
    ? 'grid-cols-2 justify-center'
    : 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 justify-center'

const maxWidth =
  stats.length === 1
    ? 'max-w-4xl'
    : stats.length === 2
    ? 'max-w-xl'
    : 'max-w-6xl';

  return (
    <div
      ref={ref}
      className={`px-4 container mx-auto ${maxWidth} grid ${gridCols} gap-8 text-center font-ramilas font-bold ${className}`}
    >
      {stats.map((stat, index) => (
        <div key={index} className="space-y-2">
          {/* Pass the calculated shouldStartAnimation as the startAnimation prop */}
          <AnimatedStat value={stat.value} text_size={text_size} startAnimation={shouldStartAnimation} />
          <div className="text-xl md:text-base text-custom-new-green font-ubuntu font-normal">
            {stat.label}
          </div>
        </div>
      ))}
    </div>
  );
};

export default Stats;
