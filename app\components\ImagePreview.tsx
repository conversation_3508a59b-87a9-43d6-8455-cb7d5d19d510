"use client";

import { X } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

type AccreditationItem = {
  name?: string;
  imageUrl: string;
};

const ImagePreview = ({ items }: { items: AccreditationItem[] }) => {
  const [selectedImg, setSelectedImg] = useState<string | null>(null);

  return (
    <>
      <div className="mt-8 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
        {items.map((item, index) => (
          <div
            key={index}
            className="w-full max-w-[334px] max-h-[376px] mx-auto border border-[#0E325E] cursor-pointer p-2 flex items-center justify-center bg-white"
            onClick={() => setSelectedImg(item.imageUrl)}
          >
            <Image
              src={item.imageUrl}
              alt={item?.name || `Certificate ${index + 1}`}
              width={0}
              height={0}
              sizes="100vw"
              className="w-full h-full object-contain"
            />
          </div>
        ))}
      </div>

      {/* Modal Image Preview */}
      {selectedImg && (
        <div
          className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4"
          onClick={() => setSelectedImg(null)}
        >
          <button
            className="absolute top-2 right-2 z-50 text-white hover:text-gray-300"
            onClick={() => setSelectedImg(null)}
          >
            <X size={28} />
          </button>
          <div
            className="relative w-full max-w-5xl max-h-screen overflow-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-center items-center">
              <Image
                src={selectedImg}
                alt="Full preview"
                width={1000}
                height={1000}
                className="w-auto max-h-[90vh] object-contain rounded shadow-lg"
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ImagePreview;
