"use client";

import Image from "next/image";
import { useEffect, useState } from "react";
import { client } from "@/lib/sanity";
import { JMC_MOSQUE_QUERY } from "@/sanity/lib/queries";

interface MosqueImage {
  name: string;
  imageUrl: string;
}

interface JmcMosqueData {
  _id: string;
  mosqueImages: MosqueImage[];
  menPrayerHallImages: MosqueImage[];
  womenPrayerHallImages: MosqueImage[];
}

const MenPrayerHall = () => {
  const [prayerHallImages, setPrayerHallImages] = useState<MosqueImage[]>([]);

  // Fallback data if no Sanity data
  const fallbackImages = [
    {
      name: "Men's Prayer Hall Image 1",
      imageUrl: "https://jmc.edu/images/facilities/prayer-hall-men-1.jpg",
    },
    {
      name: "Men's Prayer Hall Image 2",
      imageUrl: "https://jmc.edu/images/facilities/prayer-hall-men-2.jpg",
    },
    {
      name: "Men's Prayer Hall Image 3",
      imageUrl: "https://jmc.edu/images/facilities/prayer-hall-men-3.jpg",
    },
    {
      name: "Men's Prayer Hall Image 4",
      imageUrl: "https://jmc.edu/images/facilities/prayer-hall-men-4.jpg",
    },
  ];

  useEffect(() => {
    client.fetch(JMC_MOSQUE_QUERY).then((data: JmcMosqueData[]) => {
      if (data.length > 0 && data[0].menPrayerHallImages?.length > 0) {
        // Filter out images with empty URLs
        const validImages = data[0].menPrayerHallImages.filter(img =>
          img.imageUrl && img.imageUrl.trim() !== ""
        );
        setPrayerHallImages(validImages.length > 0 ? validImages : fallbackImages);
      } else {
        setPrayerHallImages(fallbackImages);
      }
    });
  }, []);
  return (
    <section className="bg-white pb-10 px-4" id="men-prayer-hall">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green text-center">Prayer Hall for Men</h2>
        <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          {prayerHallImages.map((item, idx) => (
            <div
              key={idx}
              className="h-36 sm:h-48 md:h-60 rounded-2xl overflow-hidden shadow-md group relative"
            >
              <Image
                src={item?.imageUrl}
                alt={item?.name}
                width={500}
                height={300}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default MenPrayerHall;
