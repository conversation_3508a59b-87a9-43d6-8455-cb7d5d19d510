import React from "react";

interface ResearchProject {
  id: number;
  investigator: string;
  title: string;
  amount: string;
  fundingAgency: string;
}

interface ResearchDepartmentTableProps {
  subtitle?: string;
  departmentName: string;
  projects: ResearchProject[];
}

const ResearchDepartmentTable: React.FC<ResearchDepartmentTableProps> = ({
  subtitle,
  departmentName,
  projects = [],
}) => {
  return (
    <div >
      {subtitle && (
        <h3 className="bg-[#D1D9D1] text-lg sm:text-xl font-semibold text-[#002E00] pt-6 sm:pt-8 px-4 sm:px-8">
          {subtitle}
        </h3>
      )}

      <div className="bg-[#D1D9D1]">
        {/* Department Name Header */}
        <div className="text-[#002E00] text-center py-3 sm:py-4 font-semibold text-lg sm:text-xl">
          {departmentName}
        </div>

        <div className="bg-[#D1D9D1] rounded-lg overflow-hidden">
          {/* Add overflow container for mobile responsiveness */}
          <div className="overflow-x-auto">
            <div className="min-w-[768px]"> {/* Minimum width to prevent squishing on mobile */}
              {/* Header with margin */}
              <div className="mx-4 my-4">
                <div className="bg-[#002E00] text-white rounded-full">
                  <div className="flex w-full">
                    <div className="py-3 sm:py-4 px-3 sm:px-6 text-left font-medium w-[20%] text-xs sm:text-sm md:text-base whitespace-normal break-words">NAME OF THE INVESTIGATOR</div>
                    <div className="py-3 sm:py-4 px-3 sm:px-6 text-left font-medium w-[40%] text-xs sm:text-sm md:text-base whitespace-normal break-words">TITLE OF THE PROJECT AND DURATION</div>
                    <div className="py-3 sm:py-4 px-3 sm:px-6 text-left font-medium w-[20%] text-xs sm:text-sm md:text-base whitespace-normal break-words">AMOUNT SANCTIONED (IN INR)</div>
                    <div className="py-3 sm:py-4 px-3 sm:px-6 text-left font-medium w-[20%] text-xs sm:text-sm md:text-base whitespace-normal break-words">FUNDING AGENCY</div>
                  </div>
                </div>
              </div>

              {/* Table content */}
              <table className="min-w-full table-fixed">
                <colgroup>
                  <col className="w-[20%]" />
                  <col className="w-[40%]" />
                  <col className="w-[20%]" />
                  <col className="w-[20%]" />
                </colgroup>
                <tbody>
                  {projects.map((project) => (
                    <tr key={project.id} className="border-b border-[#555555]">
                      <td className="py-2 sm:py-3 px-3 sm:px-6 text-left text-[#555555] text-xs sm:text-sm md:text-base whitespace-normal break-words">{project.investigator}</td>
                      <td className="py-2 sm:py-3 px-3 sm:px-6 text-left text-[#555555] text-xs sm:text-sm md:text-base whitespace-normal break-words">{project.title}</td>
                      <td className="py-2 sm:py-3 px-3 sm:px-6 text-left text-[#555555] text-xs sm:text-sm md:text-base whitespace-normal break-words">{project.amount}</td>
                      <td className="py-2 sm:py-3 px-3 sm:px-6 text-left text-[#555555] text-xs sm:text-sm md:text-base whitespace-normal break-words">{project.fundingAgency}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResearchDepartmentTable;
