"use client";

import Navbar from "@/components/Navbar";
import Footer from "../sections/Footer";
import Link from "next/link";
import AboutResearch from "../sections/research/AboutResearch";
import MissionVision from "../sections/research/MissionVision";
import ResearchCommittee from "../sections/research/ResearchCommittee";
import ResearchEthicsCommittee from "../sections/research/ResearchEthicsCommittee";
import ResearchTeam from "../sections/research/ResearchTeam";
import MPhilGuide from "../sections/research/MPhilGuide";
import StudentProjects from "../sections/research/StudentProjects";
import ResearchProjects from "../sections/research/ResearchProjects";
import PatentCertification from "../sections/research/PatentCertification";
import SeedMoneyResearch from "../sections/research/SeedMoneyResearch";
import DstFistResearch from "../sections/research/DstFistResearch";
import CTAComponent from "../components/CTAComponent";

const ResearchPage = () => {

  return (
    <>
      <Navbar fixed={true} border={true} />
      <header
        className="relative pt-40 pb-16 px-4 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex flex-col md:flex-row md:justify-between md:items-center">
          <h1 className="font-ramilas text-center md:text-left mb-4 md:mb-0">
            Research
          </h1>
          <ul className="font-poppins flex flex-row justify-center md:justify-end gap-8 md:gap-8 md:pr-16">
            <li className="list-disc">
              <Link href="/">Home</Link>
            </li>
            <li className="list-disc">Research</li>
          </ul>
        </div>
      </header>

      <main className="py-12 bg-gray-50">
        <div className="container mx-auto max-w-7xl">
          {/* About Research Section */}
          <AboutResearch />

          {/* Mission and Vision Section */}
          <MissionVision />
        </div>

        {/* Committee Sections */}
        <ResearchCommittee />
        <ResearchEthicsCommittee />
        <ResearchTeam />
        <MPhilGuide />
        <StudentProjects />
        <ResearchProjects />
        <PatentCertification />
        <SeedMoneyResearch />
        <DstFistResearch />
        <section className="bg-white py-10 px-4 lg:px-12">
          <div className=" mx-auto">
            <CTAComponent />
          </div>
        </section>

      </main>

      <Footer />
    </>
  );
};

export default ResearchPage;
