"use client";

import ScholarshipTable from "@/app/components/ScholarshipTable";
import { eligibilityData } from "@/app/helper/helper";

const Scholarships = () => {
  const freshScholarshipDocuments = [
    "Income Certificate (Current Year Income Proof Only)",
    "Community Certificate Copy",
    "Aadhar Card Copy",
    "Ration Card Copy",
    "Student Bank Account Book Copy",
    "10th and 12th Mark Sheet Copy",
    "College Fee Receipt Copy",
    "Previous Year Attendance Certificate (Original)",
  ];
  const renewalScholarshipDocuments = [
    "Community Certificate Copy",
    "Aadhar Card Copy",
    "Ration Card Copy",
    "Student Bank Account Book Copy",
    "10th and 12th and Previous Semesters Mark Sheet Copy",
    "College Fee Receipt Copy",
    "Previous Year Attendance Certificate (Original)",
  ];

  return (
    <section className="bg-white pb-10 px-4">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green text-center underline underline-offset-4">
          Scholarships
        </h2>
        <ScholarshipTable />
        <div className="rounded-2xl border border-gray-200 shadow-sm bg-white p-4">
          <div className="md:p-10 lg:px-16 lg:py-10 space-y-6">
            <div>
              <h3 className="text-custom-green text-fluid-base font-poppins font-semibold mb-2">
                The following documents required for all types of Government
                scholarships
              </h3>
              <div className="space-y-4">
                <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
                  Fresh Scholarship Application
                </h3>
                <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
                  {freshScholarshipDocuments?.map((rule: any, idx: any) => (
                    <li key={idx}>{rule}</li>
                  ))}
                </ul>
              </div>
            </div>
            <div className="space-y-4">
              <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
                Renewal Scholarship Application
              </h3>
              <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
                {renewalScholarshipDocuments?.map((rule: any, idx: any) => (
                  <li key={idx}>{rule}</li>
                ))}
              </ul>
            </div>
            <div className="space-y-4 scholarship-list">
              <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
                ELIGIBILITY CONDITIONS FOR SCHOLARSHIPS
              </h3>
              <ol className="list-decimal list-inside space-y-4 marker:font-poppins marker:text-custom-new-green text-custom-new-green font-poppins">
                {eligibilityData.map((item, index) => (
                  <li key={index}>
                    {item.title}
                    <ul className="list-disc list-inside pl-4 mt-2 space-y-1">
                      {item.points.map((point, idx) => (
                        <li
                          key={idx}
                          dangerouslySetInnerHTML={{ __html: point }}
                        />
                      ))}
                    </ul>
                  </li>
                ))}
              </ol>
            </div>
          </div>
          <div className="p-4 mt-6 flex flex-col md:flex-row gap-4 justify-center border rounded-2xl border-custom-green font-poppins">
            <a
              href="https://example.com/form1"
              target="_blank"
              rel="noopener noreferrer"
              className="flex-1 inline-block rounded-2xl border border-custom-green bg-custom-green px-6 py-3 text-white text-center text-sm font-semibold transition hover:bg-opacity-90"
            >
              COLLEGE SPONSORED SCHOLARSHIP APPLICATION FORM
              <br />
              FOR POOR & MERITORIOUS STUDENTS (FRESH FORM)
            </a>
            <a
              href="https://example.com/form2"
              target="_blank"
              rel="noopener noreferrer"
              className="flex-1 inline-block rounded-2xl border border-custom-green bg-custom-green px-6 py-3 text-white text-center text-sm font-semibold transition hover:bg-opacity-90"
            >
              COLLEGE SPONSORED SCHOLARSHIP APPLICATION FORM
              <br />
              FOR POOR & MERITORIOUS STUDENTS (RENEWAL FORM)
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Scholarships;
