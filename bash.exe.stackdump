Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF983630000 ntdll.dll
7FF96B200000 aswhook.dll
7FF982D30000 KERNEL32.DLL
7FF980A20000 KERNELBASE.dll
7FF982460000 USER32.dll
000210040000 msys-2.0.dll
7FF981100000 win32u.dll
7FF9821B0000 GDI32.dll
7FF980F40000 gdi32full.dll
7FF981060000 msvcp_win.dll
7FF9807C0000 ucrtbase.dll
7FF9822F0000 advapi32.dll
7FF9828A0000 msvcrt.dll
7FF981FA0000 sechost.dll
7FF981130000 bcrypt.dll
7FF982B30000 RPCRT4.dll
7FF97FF50000 CRYPTBASE.DLL
7FF980740000 bcryptPrimitives.dll
7FF983520000 IMM32.DLL
