import React from "react";

// Define interfaces for our data structure
export interface CopyrightEntry {
  serialNo: number;
  name: string;
  applicationNumber: string;
  title: string;
  dateOfGrantOrFiling: string;
  yearOfGrant: string;
}

export interface CopyrightTableData {
  title: string;
  entries: CopyrightEntry[];
}

interface CopyrightTableProps {
  copyrightData: CopyrightTableData[];
}

const CopyrightTable: React.FC<CopyrightTableProps> = ({ copyrightData }) => {
  return (
    <div className="bg-[#D1D9D1]">
      {copyrightData.map((data, tableIndex) => (
        <div key={`copyright-table-${tableIndex}`} className="mb-8">
          <h3 className="text-lg sm:text-xl font-bold text-[#002E00] pt-6 sm:pt-8 px-4 sm:px-8">
            {data.title}
          </h3>

          <div className="rounded-lg overflow-hidden">
            {/* Add overflow container for mobile responsiveness */}
            <div className="overflow-x-auto">
              <div className="min-w-[768px] max-w-full"> {/* Minimum width to prevent squishing on mobile */}
                {/* Header with margin */}
                <div className="mx-4 my-4">
                  <div className="bg-[#002E00] text-white rounded-full">
                    <table className="w-full">
                      <thead>
                        <tr>
                          <th className="py-4 px-6 text-center font-medium w-[4.17%] text-xs sm:text-sm md:text-base">S.NO.</th>
                          <th className="py-4 px-6 text-left font-medium w-[30%] text-xs sm:text-sm md:text-base whitespace-normal break-words">NAME</th>
                          <th className="py-4 px-6 text-center font-medium w-[18%] text-xs sm:text-sm md:text-base whitespace-normal break-words">
                            <span className="md:hidden">APP. NO.</span>
                            <span className="hidden md:inline">APPLICATION NUMBER</span>
                          </th>
                          <th className="py-4 px-6 text-left font-medium w-[30%] text-xs sm:text-sm md:text-base whitespace-normal break-words hyphens-auto" style={{ wordBreak: 'break-word' }}>TITLE</th>
                          <th className="py-4 px-6 text-center font-medium w-[15%] text-xs sm:text-sm md:text-base whitespace-normal break-words">
                            <span className="md:hidden">DATE</span>
                            <span className="hidden md:inline">DATE OF GRANT / FILING</span>
                          </th>
                          <th className="py-4 px-6 text-center font-medium w-[10%] text-xs sm:text-sm md:text-base whitespace-normal break-words">
                            YEAR OF GRANT
                          </th>
                        </tr>
                      </thead>
                    </table>
                  </div>
                </div>

                {/* Table content */}
                <table className="min-w-full">
                  <tbody>
                    {data.entries.map((entry) => (
                      <tr
                        key={`copyright-${data.title}-${entry.serialNo}`}
                        className="border-b border-[#555555]"
                      >
                        <td className="py-3 px-12 text-center text-[#555555] w-[3%] text-xs sm:text-sm md:text-base">{entry.serialNo}</td>
                        <td className="py-3 px-6 text-left text-[#555555] w-[25%] lg:w-[28%] text-xs sm:text-sm md:text-base whitespace-normal break-words">{entry.name}</td>
                        <td className="py-3  text-center text-[#555555] w-[15%] text-xs sm:text-sm md:text-base whitespace-normal break-words">{entry.applicationNumber}</td>
                        <td className="py-3 px-4 text-left text-[#555555] w-[25%] text-xs sm:text-sm md:text-base whitespace-normal break-words hyphens-auto" style={{ wordBreak: 'break-word' }}>{entry.title}</td>
                        <td className="py-3 text-center text-[#555555] w-[12%] md:w-[15%] text-xs sm:text-sm md:text-base whitespace-normal break-words">{entry.dateOfGrantOrFiling}</td>
                        <td className="py-3 px-6 pr-8 text-left text-[#555555] w-[14%] xl:w-[8%] text-xs sm:text-sm md:text-base whitespace-normal break-words">{entry.yearOfGrant}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default CopyrightTable;
