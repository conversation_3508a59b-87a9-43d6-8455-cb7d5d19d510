import { defineType, defineField } from "sanity";
import { DocumentTextIcon, LinkIcon } from '@sanity/icons'; // Added LinkIcon

export default defineType({
  name: "policy",
  title: "Policy",
  type: "document",
  icon: DocumentTextIcon, 
  fields: [
    defineField({
      name: "name",
      title: "Policy Name",
      type: "string",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "policyUrl", // Renamed from policyFile
      title: "Policy URL (PDF Link)", // Updated title
      type: "url", // Changed type from 'file' to 'url'
      icon: LinkIcon, // Optional: Add a link icon
      validation: (Rule) => Rule.required().uri({
        scheme: ['http', 'https'] // Ensure it's a valid http/https URL
      })
    }),
  ],
  preview: {
    select: {
      title: 'name',
      subtitle: 'policyUrl', // Show URL in preview
      media: 'icon' 
    }
  }
});