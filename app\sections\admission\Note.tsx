"use client";

const Note = () => {
  return (
    <section className="px-4 py-12">
      <div className="max-w-4xl container mx-auto rounded-2xl border border-gray-200 p-6 md:p-10 shadow-sm bg-white">
        <h2 className="text-custom-green mb-6">Note</h2>
        <ul className="list-disc list-outside pl-6 space-y-4 text-custom-new-green text-base leading-relaxed">
          <li>
            Candidates are instructed to ensure that whether they have entered
            their e-mail id and Contact number correctly before submitting the
            application.
          </li>
          <li>
            Students selected for Provisional Admission will be notified only by
            Registered Email id and Mobile Number.
          </li>
          <li>
            Applicants can also view the selected Provisional Admission List on
            the College Website.
          </li>
          <li>
            Selected shortlisted applicants should pay the college fees through
            online mode as per the instructions received in your email.
          </li>
        </ul>
      </div>
    </section>
  );
};

export default Note;
