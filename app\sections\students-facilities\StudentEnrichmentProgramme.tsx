import { client } from "@/lib/sanity";
import { studentEnrichmentProgrammesQuery } from "@/sanity/lib/queries";
import useEmblaCarousel from "embla-carousel-react";
import Image from "next/image";
import React, { useCallback, useEffect, useState } from "react";

const StudentEnrichmentProgramme = () => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true });
  const [programmes, setProgrammes] = useState<any[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [scrollSnaps, setScrollSnaps] = useState<number[]>([]);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  const scrollTo = useCallback(
    (index: number) => emblaApi && emblaApi.scrollTo(index),
    [emblaApi]
  );

  const onPrevious = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const onNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;

    setScrollSnaps(emblaApi.scrollSnapList());
    emblaApi.on("select", onSelect);
    onSelect();

    return () => {
      emblaApi.off("select", onSelect);
    };
  }, [emblaApi, onSelect]);

  useEffect(() => {
    client.fetch(studentEnrichmentProgrammesQuery).then((data) => {
      console.log("Fetched student enrichment programmes:", data);
      const transformedData = data.map((item: any) => ({
        ...item,
        image: {
          asset: {
            url: item.imageUrl
          }
        }
      }));
      setProgrammes(transformedData);
    });
  }, []);

  return (
    <section className="py-10 px-4 bg-custom-green">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-white">Student Enrichment Programme</h2>
        <p className="text-white">
          Aliquet sed nulla tincidunt pulvinar sed fames sit facilisis dictumst.
          Ornare faucibus quis velit fringilla aliquam ultricies. Malesuada ut
          aliquam at ac est nisi, interdum etiam dignissim. Sed ut vestibulum
          eget purus ornare. Risus elit et fringilla habitant ut facilisi.
        </p>
        <div className="relative">
          <div className="overflow-hidden" ref={emblaRef}>
            <div className="flex">
              {programmes.map((prog) => (
                <div key={prog._id} className="flex-[0_0_100%] min-w-0">
                  <div className="relative flex flex-col md:flex-row px-1 gap-4 md:gap-0">
                    {/* Left side - Image */}
                    <div className="w-full md:w-3/5 relative h-[250px] md:h-[350px] rounded-2xl overflow-hidden mb-2 md:mb-0">
                      <Image
                        src={prog.image.asset.url}
                        alt={prog.name}
                        fill
                        className="object-cover"
                      />
                    </div>

                    {/* Right side - Content */}
                    <div className="w-full md:w-3/4 md:absolute md:left-[50%] md:top-[50px] p-6 border-2 border-custom-green rounded-2xl md:rounded-xl bg-white flex flex-col justify-center z-10 md:h-[250px] md:max-w-[45%]">
                      <h3 className="text-xl md:text-2xl font-bold text-custom-green mb-2 md:mb-3">
                        {prog.name}
                      </h3>
                      <p className="text-[#0F172A] mb-3 md:mb-4 line-clamp-6 overflow-hidden text-ellipsis">
                        {prog.description}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation buttons */}
          <div className="hidden md:flex justify-between absolute top-1/2 -translate-y-1/2 left-0 right-0 px-4">
            <button
              onClick={onPrevious}
              className="bg-white rounded-full w-10 h-10 flex items-center justify-center shadow-md z-20"
            >
              <span className="sr-only">Previous slide</span>
              <svg
                width="15"
                height="15"
                viewBox="0 0 15 15"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M8.84182 3.13514C9.04327 3.32401 9.05348 3.64042 8.86462 3.84188L5.43521 7.49991L8.86462 11.1579C9.05348 11.3594 9.04327 11.6758 8.84182 11.8647C8.64036 12.0535 8.32394 12.0433 8.13508 11.8419L4.38508 7.84188C4.20477 7.64955 4.20477 7.35027 4.38508 7.15794L8.13508 3.15794C8.32394 2.95648 8.64036 2.94628 8.84182 3.13514Z"
                  fill="currentColor"
                  fillRule="evenodd"
                  clipRule="evenodd"
                ></path>
              </svg>
            </button>
            <button
              onClick={onNext}
              className="bg-white rounded-full w-10 h-10 flex items-center justify-center shadow-md z-20"
            >
              <span className="sr-only">Next slide</span>
              <svg
                width="15"
                height="15"
                viewBox="0 0 15 15"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M6.1584 3.13514C5.95694 3.32401 5.94673 3.64042 6.13559 3.84188L9.565 7.49991L6.13559 11.1579C5.94673 11.3594 5.95694 11.6758 6.1584 11.8647C6.35986 12.0535 6.67627 12.0433 6.86514 11.8419L10.6151 7.84188C10.7954 7.64955 10.7954 7.35027 10.6151 7.15794L6.86514 3.15794C6.67627 2.95648 6.35986 2.94628 6.1584 3.13514Z"
                  fill="currentColor"
                  fillRule="evenodd"
                  clipRule="evenodd"
                ></path>
              </svg>
            </button>
          </div>

          {/* Dots indicator */}
          <div className="flex justify-center mt-8 gap-2">
            {scrollSnaps.map((_, index) => (
              <button
                key={index}
                className={`w-3 h-3 border-2 border-white rounded-full ${
                  selectedIndex === index ? "bg-white" : "bg-custom-green"
                }`}
                onClick={() => scrollTo(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>

          {/* Mobile View All button */}
          <div className="flex md:hidden justify-center mt-6">
            <button className="bg-transparent text-custom-green px-24 py-3 rounded-full border-2 border-custom-green text-3xl font-semibold">
              View All
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default StudentEnrichmentProgramme;
