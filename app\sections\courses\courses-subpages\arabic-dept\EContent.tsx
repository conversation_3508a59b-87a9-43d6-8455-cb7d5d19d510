"use client";

import Policies from "@/app/components/Policies";
// Interface for the content items passed from the parent
interface EContentItem {
  _id: string;
  title: string;
  contentUrl: string;
  author?: string; // Include author if it's passed
}

// Interface for this component's props
interface EContentProps {
  title: string; 
  content: EContentItem[];
  isLoading?: boolean;
}

const EContent = ({ title, content = [], isLoading = false }: EContentProps) => {

  // Map the incoming 'content' data to the structure expected by DataDisplaySection
  const displayItems = content.map(item => ({
    _id: item._id,
    name: item.title, // Map title to name
    url: item.contentUrl, // Map contentUrl to url
    author: item.author // Pass author along if needed by DisplayItem interface
  }));
  return <Policies title={title} 
  items={displayItems} 
  isLoading={isLoading}
  iconType="external" />;
};

export default EContent;
