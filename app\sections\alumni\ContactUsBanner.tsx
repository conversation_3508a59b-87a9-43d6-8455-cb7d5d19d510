// app/sections/alumni-showcase/ContactUsBanner.tsx
import React from 'react';

const ContactUsBanner = () => {
  return (
    <section className="relative py-20 bg-green-700 text-white" style={{ 
      backgroundImage: "url('/alumni/ctabgpattern.png')",
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      backgroundRepeat: 'no-repeat'
    }}>
      {/* <div className="absolute inset-0 bg-black opacity-50"></div> Overlay for better text readability */}
      <div className="container mx-auto px-4 text-center relative z-10">
        <h2 className="text-3xl pt-8 font-bold mb-4">Jamal <PERSON> College Provide Scholarship For Talented Student!</h2>
        <p className="text-lg mb-8 max-w-2xl mx-auto">
          CA<PERSON><PERSON> Needs enables you to harness the power of your alumni network. Whatever may be the need academic, relocation, career, projects, mentorship, etc. you can ask the community and get.
        </p>
        <button className="bg-white text-green-700 font-semibold py-3 px-8 rounded-full hover:bg-gray-100 transition duration-300">
          Get Consultation
        </button>
      </div>
    </section>
  );
};

export default ContactUsBanner;