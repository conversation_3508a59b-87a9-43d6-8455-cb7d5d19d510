import type { Rule } from "@sanity/types";
const newsletterSchema = {
  name: "newsletters",
  title: "Newsletters",
  type: "document",
  fields: [
    {
      name: "name",
      title: "Name",
      type: "string",
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: "category",
      title: "Category",
      type: "string",
      validation: (Rule: Rule) => Rule.required(),
    },
    {
      name: "pdfUrl", // Renamed for clarity
      title: "PDF URL", // Updated title
      type: "url", // Changed type from 'file' to 'url'
      validation: (Rule: Rule) => Rule.required().uri({
        scheme: ['http', 'https'] // Optional: Ensure it's a valid HTTP/HTTPS URL
      }),
      // Removed 'options' as it's not applicable to 'url' type
    },
  ],
};

export default newsletterSchema;
