import { defineType, defineField } from "sanity";
import { DocumentTextIcon, LinkIcon } from '@sanity/icons';

export default defineType({
  name: "annualReport",
  title: "Annual Report",
  type: "document",
  icon: DocumentTextIcon,
  fields: [
    defineField({
      name: "year",
      title: "Year Range (e.g., 2024–25)",
      type: "string",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "label",
      title: "Label (e.g., Men & Women)",
      type: "string",
      initialValue: "Men & Women", // Optional: Set a default value
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "reportUrl",
      title: "Report URL (PDF Link)",
      type: "url",
      icon: LinkIcon,
      validation: (Rule) => Rule.required().uri({
        scheme: ['http', 'https']
      })
    }),
  ],
  preview: {
    select: {
      title: 'year',
      subtitle: 'label',
      media: 'icon'
    }
  },
  // Optional: Define ordering for the Sanity Studio list view
  orderings: [
    {
      title: 'Year, Newest First',
      name: 'yearDesc',
      by: [
        {field: 'year', direction: 'desc'}
      ]
    }
  ]
});