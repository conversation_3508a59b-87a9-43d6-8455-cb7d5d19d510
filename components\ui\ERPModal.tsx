"use client";

import { Dialog, DialogContent, DialogOverlay, DialogTitle } from "@/components/ui/dialog";
import Image from "next/image";
import Link from "next/link";
import { X } from "lucide-react";

// Define the possible types for the modal
type ModalType = 'erp' | 'payFee';

interface ERPModalProps {
    isOpen: boolean;
    onClose: () => void;
    type?: ModalType; // Add type prop, default to 'erp'
    onProceed?: () => void; // Add optional callback for proceeding (used for payFee)
}

// Define content based on type
const modalContent = {
    erp: {
        title: "ERP Login",
        heading: "CHOOSE YOUR LOGIN TYPE",
        button1Text: "Staff Login",
        button1Link: "https://jmcerp.in/erp/",
        button2Text: "Student Login",
        button2Link: "https://jmcerp.in/studentlogin/",
    },
    payFee: {
        title: "Pay Fee Online",
        heading: "CHOOSE CATEGORY TO PAY ONLINE",
        button1Text: "SEMESTER FEES PAYMENT INSTRUCTIONS",
        button1Link: "/feesPaymentProcedure", // Replace with actual link if available
        button2Text: "ONLINE PAYMENT",
        // button2Link is removed as it's handled by onProceed now
    },
};

const ERPModal: React.FC<ERPModalProps> = ({ isOpen, onClose, type = 'erp', onProceed }) => {
    const handleClose = () => {
        console.log(`Modal (${type}) close triggered!`); 
        onClose(); 
    };

    const content = modalContent[type]; // Get content based on type

    const handleProceedClick = () => {
        if (onProceed) {
            console.log("Proceed button clicked, calling onProceed...");
            onProceed(); // Call the callback function passed from Navbar
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogOverlay className="fixed inset-0 bg-black/30 z-[999]" />
            <DialogContent
                className="fixed left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%] z-[1000]
                           max-w-3xl w-[90vw] overflow-hidden rounded-xl border-none
                           shadow-lg bg-custom-green p-0"
                // Pass the custom class for the close button
                closeButtonClassName="absolute top-6 right-6 text-custom-green hover:text-gray-800 border-2 border-custom-green rounded-full px-6 py-2 cursor-pointer focus:outline-none focus:ring-0"
            >
                <DialogTitle className="sr-only">{content.title}</DialogTitle>

                {/* Main Content */}
                <div> 
                    {/* Card */}
                    <div className="bg-[#e8ede8] p-6 pt-10 flex flex-col items-center justify-center rounded-lg m-2">
                        {/* Logo */}
                        <div className="mb-4">
                            <Image
                                src="/jmc_logo 2.svg"
                                alt="JMC Logo"
                                width={120}
                                height={120}
                                className="w-auto h-auto"
                                priority
                            />
                        </div>

                        {/* Heading - Use dynamic content */}
                        <h2 className="text-xl font-bold text-custom-green mb-6 text-center">
                            {content.heading}
                        </h2>
                    </div>

                    {/* Buttons - Use dynamic content */}
                    <div className="p-6 flex flex-col sm:flex-row gap-4 justify-center bg-white">
                        {/* Link 1 - Ensure it's a flex item */}
                        <Link 
                            href={content.button1Link} 
                            className="w-full flex" // Added flex
                            {...(type !== 'payFee' && { target: "_blank", rel: "noopener noreferrer" })}
                        >
                            <button className="border-2 border-custom-green text-custom-green hover:bg-custom-green hover:text-white font-medium py-3 px-8 rounded-full min-w-[140px] w-full h-full transition-colors flex items-center justify-center text-center">
                                {content.button1Text}
                            </button>
                        </Link>
                        
                        {/* Button 2 (Conditional: Link for 'erp', Button for 'payFee') */}
                        {type === 'erp' ? (
                            // Link 2 - Ensure it's a flex item
                            <Link 
                                href={modalContent.erp.button2Link} 
                                className="w-full flex" // Added flex
                                target="_blank" 
                                rel="noopener noreferrer"
                            >
                                <button className="border-2 border-custom-green text-custom-green hover:bg-custom-green hover:text-white font-medium py-3 px-8 rounded-full min-w-[140px] w-full h-full transition-colors flex items-center justify-center text-center">
                                    {content.button2Text}
                                </button>
                            </Link>
                        ) : (
                            // Div Wrapper - Ensure it's a flex item and allows button to stretch
                            <div className="w-full flex"> 
                                <button 
                                    onClick={handleProceedClick}
                                    className="border-2 border-custom-green text-custom-green hover:bg-custom-green hover:text-white font-medium py-3 px-8 rounded-full min-w-[140px] w-full h-full transition-colors flex items-center justify-center text-center"
                                >
                                    {content.button2Text}
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default ERPModal;