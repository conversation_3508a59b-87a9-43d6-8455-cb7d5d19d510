"use client";
import { useState, useEffect } from "react";
import { ChevronDown, ChevronUp, Download, Loader2, ExternalLink } from "lucide-react"; // Keep icons

// Define a generic interface for the items it can display
interface DisplayItem {
  _id: string;
  name: string; // Use 'name' consistently internally
  url: string;  // Use 'url' consistently internally
  // Add other potential fields if needed, like 'author', but they won't be used by default
  author?: string;
}

interface PoliciesProps {
  title: string;
  items: DisplayItem[]; // Accept items array as prop
  isLoading?: boolean; // Optional loading prop from parent
  iconType?: 'download' | 'external'; // Optional prop to choose icon
}

// Renamed component slightly for clarity, but you can keep 'Policies'
const Policies = ({ title, items = [], isLoading = false, iconType = 'download' }: PoliciesProps) => {
  const [showAll, setShowAll] = useState(false);
  const [windowWidth, setWindowWidth] = useState(0);

  useEffect(() => {
    // Remove data fetching logic

    // Window resize listener setup remains
    setWindowWidth(window.innerWidth);
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []); // Empty dependency array

  // Determine counts based on screen size (uses 'items' prop)
  const getVisibleCount = () => {
    if (showAll) return items.length;
    if (windowWidth >= 1024) return 6;
    if (windowWidth >= 768) return 4;
    if (windowWidth >= 640) return 2;
    return 1;
  };

  const getTeaserEnd = () => {
    if (windowWidth >= 1024) return 3;
    if (windowWidth >= 768) return 2;
    if (windowWidth >= 640) return 2;
    return 1;
  };

  const visibleCount = getVisibleCount();
  const showTeaser = !showAll && items.length > visibleCount;
  const teaserStart = visibleCount;
  const teaserEnd = getTeaserEnd();

  const IconComponent = iconType === 'external' ? ExternalLink : Download;

  return (
    <section
      className="bg-custom-green text-white px-8 pt-8 pb-16 relative min-h-[200px]"
      // id can be passed or generated if needed
    >
      <div className="container mx-auto max-w-7xl text-center">
        <h2 className="mb-6 text-2xl md:text-3xl font-ramilas">{title}</h2>

        {isLoading ? (
          <div className="flex justify-center items-center h-40">
            <Loader2 className="w-10 h-10 animate-spin text-white" />
          </div>
        ) : items.length === 0 ? (
          <div className="flex justify-center items-center h-40">
            {/* Make empty message slightly more generic */}
            <p className="text-xl font-poppins text-gray-300">No Items Available.</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 justify-center mb-6 text-sm md:text-base transition-all duration-300">
              {/* Map over the items prop */} 
              {items.slice(0, visibleCount).map((item, index) => {
                // Tooltip text remains the same
                const tooltipText = item.author ? `${item.name} - ${item.author}` : item.name;

                return (
                  <a
                    key={item._id || index}
                    href={item.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    // Adjusted padding slightly for potential two lines
                    className="flex items-center justify-between px-4 py-2 md:px-6 md:py-2.5 w-full sm:w-auto min-w-[180px] 
                           bg-white bg-opacity-20 backdrop-blur-sm hover:bg-opacity-30 rounded-full border border-white/50 shadow-lg 
                           transition-all hover:scale-105 font-poppins text-white group"
                    title={tooltipText} // Tooltip shows both if author exists
                  >
                    {/* Use flex-col for vertical stacking */}
                    <div className="flex flex-col text-left overflow-hidden mr-2">
                      <span
                        className="font-medium truncate group-hover:text-yellow-300 transition-colors"
                      >
                        {item.name}
                      </span>
                      {/* Conditionally render author on second line */}
                      {item.author && (
                        <span className="text-xs opacity-80 truncate group-hover:text-yellow-200 transition-colors">
                          <span className="hidden md:inline">Author: </span> {item.author}
                        </span>
                      )}
                    </div>
                    <IconComponent className="w-4 h-4 md:w-5 md:h-5 ml-auto flex-shrink-0 text-white/80 group-hover:text-yellow-300 transition-colors" />
                  </a>
                );
              })}
            </div>

            {showTeaser && (
              <div
                className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-6 
                   opacity-10 pointer-events-none transition-opacity duration-300"
              >
                {items
                  .slice(teaserStart, teaserStart + teaserEnd)
                  .map((item, index) => {
                    const teaserTooltipText = item.author ? `${item.name} - ${item.author}` : item.name;
                    return (
                      <div
                        key={`teaser-${item._id || index}`}
                        className="flex items-center justify-between px-4 py-2 md:px-6 md:py-2.5 w-full sm:w-auto min-w-[180px] 
                             bg-white bg-opacity-10 rounded-full border border-white/30 shadow-md 
                             font-poppins text-white/70"
                        title={teaserTooltipText}
                      >
                        {/* Teaser: Use flex-col for vertical stacking */}
                        <div className="flex flex-col text-left overflow-hidden mr-2">
                          <span className="font-medium truncate">
                            {item.name}
                          </span>
                          {/* Teaser: Conditionally render author */}
                          {item.author && (
                            <span className="text-xs opacity-80 truncate">
                              {item.author}
                            </span>
                          )}
                        </div>
                        <IconComponent className="w-4 h-4 md:w-5 md:h-5 ml-auto flex-shrink-0 text-white/50" />
                      </div>
                    );
                  })}
              </div>
            )}

            {/* View More/Less Button - Based on items prop */} 
            {items.length > getVisibleCount() && !showAll && (
                 <div
                 className={`absolute left-1/2 transform -translate-x-1/2 bottom-4 z-10`}
               >
                 <button
                   onClick={() => setShowAll(true)}
                   className="px-6 py-2 md:px-8 md:py-3 flex items-center gap-2 bg-white bg-opacity-90 
                         text-[#012D01] font-semibold rounded-full shadow-md 
                         hover:bg-gray-200 hover:bg-opacity-100 transition font-poppins text-sm md:text-base"
                 >
                   View More
                   <ChevronDown size={18} className="md:w-5 md:h-5" />
                 </button>
               </div>
            )}
             {showAll && (
                 <div
                 className={`flex justify-center mt-8`}
               >
                 <button
                   onClick={() => setShowAll(false)}
                   className="px-6 py-2 md:px-8 md:py-3 flex items-center gap-2 bg-white bg-opacity-90 
                         text-[#012D01] font-semibold rounded-full shadow-md 
                         hover:bg-gray-200 hover:bg-opacity-100 transition font-poppins text-sm md:text-base"
                 >
                   View Less
                   <ChevronUp size={18} className="md:w-5 md:h-5" />
                 </button>
               </div>
            )}
          </>
        )}
      </div>
    </section>
  );
};

// Export with the new name or keep 'Policies'
export default Policies;

