"use client";

import { Download } from "lucide-react";

interface StrategicPlanProps {
  title: string;
  description?: string;
  downloadLink: string;
  downloadLabel?: string;
  sectionId?: string;
}

const StrategicPlan = ({
  title,
  description,
  downloadLink,
  downloadLabel = "Download",
  sectionId = "strategic-plan",
}: StrategicPlanProps) => {
  return (
    <section
      className="bg-[#CFD7CF] py-10 px-10 shadow-custom-glow relative"
      id={sectionId}
    >
      <div className="container max-w-7xl mx-auto space-y-6 text-center mb-4">
        <h2 className="text-custom-green">{title}</h2>
        {description && <p className="text-custom-new-green">{description}</p>}
      </div>
      <div className="absolute left-1/2 md:left-3/4 -translate-x-1/2 bottom-0 translate-y-1/2 z-10">
        <a
          href={downloadLink}
          target="_blank"
          rel="noopener noreferrer"
          className="font-poppins bg-custom-green hover:bg-green-800 text-white font-semibold px-4 sm:px-6 md:px-8 py-3 sm:py-4 md:py-6 text-base md:text-xl rounded-xl shadow-[0px_0px_34px_0px_#00000026] flex items-center gap-2 sm:gap-4 md:gap-24 transition whitespace-nowrap"
        >
          {downloadLabel}{" "}
          <Download className="w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6" />
        </a>
      </div>
    </section>
  );
};

export default StrategicPlan;
