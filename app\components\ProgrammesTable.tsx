"use client";

import { Download } from "lucide-react";
import Link from 'next/link'; // Import Link

// Define the structure for a single syllabus entry
interface SyllabusEntry {
  _key?: string;
  year: string;  // Change from 'title' to 'year'
  link: string; // Change from 'fileUrl' to 'link'
}

type Programme = {
  name: string;
  sections: number | string;
  syllabus?: SyllabusEntry[];
};

type ProgrammesTableProps = {
  programmes: Programme[];
  showFooter?: boolean;
  showSyllabus?: boolean;
  programmeOutcomes?: { // Add this prop to accept outcomes data
    title?: string;
    pdfUrl: string;
  };
};

export default function ProgrammesTable({
  programmes,
  showFooter = false,
  showSyllabus = false,
  programmeOutcomes,
}: ProgrammesTableProps) {
  return (
    <div className="w-full max-w-7xl mx-auto lg:px-8 lg:pr-12">
      <div
        className="overflow-x-auto rounded-2xl shadow-lg border border-custom-green programmes-table-shadow"
      >
        <table className="w-full table-auto text-left font-ramilas font-bold text-xs sm:text-base md:text-lg lg:text-xl">
          <thead className="bg-custom-green text-white">
            <tr>
              <th className="p-4 sm:p-6">Available Programmes</th>
              <th className="p-4 sm:p-6 text-center">Sections</th>
              {showSyllabus && (
                <th className="p-4 sm:p-6 text-center">Syllabus</th>
              )}
            </tr>
          </thead>
          <tbody className="bg-custom-light-green text-custom-green">
            {programmes.map((program, index) => (
              <tr key={index} className="border-b border-black/20">
                <td className="p-4 sm:p-6 whitespace-normal">
                  {program.name.includes("(") ? (
                    <>
                      {program.name.split("(")[0]}
                      <span className="font-normal">
                        ({program.name.split("(")[1]}
                      </span>
                    </>
                  ) : (
                    program.name
                  )}
                </td>
                <td className="p-4 sm:p-6 text-center">{program.sections}</td>
                {showSyllabus && (
                  <td className="p-4 sm:p-6">
                    {program.syllabus && program.syllabus.length > 0 ? (
                      <div className="flex flex-wrap justify-center items-center gap-2">
                        {program.syllabus.map((syl) => (
                          <a
                            key={syl._key || syl.year} // Use 'year' if it's unique, or _key
                            href={syl.link} // Use 'link'
                            download
                            target="_blank" // Good practice to open PDFs in a new tab
                            rel="noopener noreferrer" // Security for target="_blank"
                            className="flex items-center justify-center gap-1 px-2 py-1 border border-custom-green rounded-md hover:bg-custom-green hover:text-white transition-colors duration-150 ease-in-out"
                          >
                            <Download className="text-custom-green group-hover:text-white" size={16} />
                            <span className="text-custom-green group-hover:text-white text-xs sm:text-sm">
                              {syl.year} {/* Use 'year' */}
                            </span>
                          </a>
                        ))}
                      </div>
                    ) : (
                      <span className="text-gray-400 text-xs italic block text-center">
                        Not Available
                      </span>
                    )}
                  </td>
                )}
              </tr>
            ))}
          </tbody>

          {showFooter && programmeOutcomes && programmeOutcomes.pdfUrl && ( // Check if programmeOutcomes and pdfUrl exist
            <tfoot>
              <tr className="bg-custom-green text-white">
                <td
                  className="p-4 sm:p-6 font-semibold"
                  colSpan={showSyllabus ? 2 : 1}
                >
                  {programmeOutcomes.title || "PROGRAMME AND COURSE OUTCOMES"} {/* Use title from outcomes or default */}
                </td>
                {showSyllabus && (
                  <td className="p-4 sm:p-6 text-center">
                    <Link href={programmeOutcomes.pdfUrl} passHref legacyBehavior>
                      <a
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex justify-center items-center gap-2 hover:underline"
                      >
                        <Download size={18} />
                        <span>Download</span>
                      </a>
                    </Link>
                  </td>
                )}
              </tr>
            </tfoot>
          )}
        </table>
      </div>
    </div>
  );
}
