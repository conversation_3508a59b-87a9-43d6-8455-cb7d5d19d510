"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";

const certificates = [
  "histories_and_achievements/image1.webp",
  "histories_and_achievements/image2.webp",
  "histories_and_achievements/image3.webp",
  "histories_and_achievements/image4.webp",
  "histories_and_achievements/image5.webp",
  "histories_and_achievements/image6.webp",
];

// Duplicate the array to enable infinite scroll effect
const duplicatedCertificates = [...certificates, ...certificates];

const OurAccreditation = () => {
  const router = useRouter();

  const handleViewAll = () => {
    router.push("/accreditation");
  };

  return (
    <section className="text-center bg-white py-12 pt-24" id="accreditation">
      <div className="overflow-hidden">
        <h2 className="px-8">Our Accreditation</h2>
        <p className="px-8 text-custom-new-green mt-4">
          Established with a strong commitment to uplifting the underprivileged
          and socially backward sections of society, our college has grown into
          a beacon of academic excellence. Spread across 87 acres, the
          institution has consistently aimed to provide quality higher education
          and foster holistic development among students.
        </p>

        <div className="relative mt-8 overflow-hidden">
          <div className="animate-scroll flex gap-4 md:gap-6 w-max">
            {duplicatedCertificates.map((src, index) => (
              <div
                key={index}
                className="border-[3px] border-custom-green rounded-2xl shrink-0 flex justify-center w-[140px] sm:w-[160px] md:w-[200px] h-[200px] md:h-[230px]"
              >
                <Image
                  src={src}
                  alt={`Certificate ${index + 1}`}
                  width={200}
                  height={230}
                  className="rounded-2xl border shadow-md"
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      <button
        className="hover:bg-custom-green hover:text-white bg-custom-light-green border-2 border-custom-green text-custom-green px-24 py-2 rounded-full w-fit font-ramilas font-bold mt-8"
        onClick={handleViewAll}
      >
        View All
      </button>
    </section>
  );
};

export default OurAccreditation;
