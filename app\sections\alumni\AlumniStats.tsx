import React from 'react';
import Image from 'next/image';
import Stats from '../../components/Stats';

const AlumniStats: React.FC = () => {
  // The Stats component requires an `isModalOpen` prop.
  // If this AlumniStats section doesn't interact with a modal that should pause animations,
  // we can pass `false`. If it does, this value should be managed by state or props.
  const isModalOpenForThisInstance = false;

  return (
    <section className="relative py-16 md:py-24 bg-gray-50"> {/* Added a light background color for contrast & relative positioning */}
      {/* Background Pattern */}
      <div className="absolute top-0 left-0 right-0 bottom-0 w-full h-full z-0 pointer-events-none "> {/* Adjusted opacity for subtlety */}
        <Image
          src="/homepage/academicsbgimg.png"
          alt="Background Pattern"
          fill
          sizes="100vw"
          // priority // Priority might not be ideal for a subtle background pattern
          className="object-cover object-center"
        />
      </div>

      {/* Stats Content - ensure it's above the background pattern */}
      <div className="container mx-auto px-4 relative z-10"> {/* Container for content, z-10 */}
        {/* You might want a title for this section, e.g., <h2 className="text-3xl font-bold text-center mb-12">Our Alumni Impact</h2> */}
        <Stats 
          isModalOpen={isModalOpenForThisInstance} 
          // text_size="text-4xl md:text-5xl" // Example: If you want to customize text size for this section
          // className="text-custom-blue" // Example: If you want to customize colors for this section
        />
      </div>
    </section>
  );
};

export default AlumniStats;