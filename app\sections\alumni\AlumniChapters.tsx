// app/sections/alumni-showcase/AlumniChapters.tsx
import React from 'react';
import Image from 'next/image';

interface ChapterCardProps {
  imageSrc: string;
  chapterName: string;
}

const ChapterCard: React.FC<ChapterCardProps> = ({ imageSrc, chapterName }) => {
  return (
    <div className="relative rounded-lg overflow-hidden shadow-lg group">
      <Image 
        src={imageSrc} 
        alt={`${chapterName} background`} 
        width={400} 
        height={250} 
        layout="responsive"
        objectFit="cover"
        className="transition-transform duration-300 group-hover:scale-105"
      />
      <div className="absolute inset-0 bg-black bg-opacity-40 flex items-end p-4">
        <h3 className="text-white text-lg font-semibold group-hover:text-yellow-400 transition-colors">
          {chapterName}
        </h3>
      </div>
    </div>
  );
};

const AlumniChapters = () => {
  const chapters = [
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/trichy.jpg", chapterName: "TRICHY CHAPTER" }, // Replace with actual image paths
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/uae.jpg", chapterName: "UAE CHAPTER" },
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/chennai.jpg", chapterName: "CHENNAI CHAPTER" },
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/kerala.jpg", chapterName: "KERALA CHAPTER" },
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/kuwait.jpg", chapterName: "KUWAIT CHAPTER" },
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/jeddah.jpg", chapterName: "JEDDAH CHAPTER" },
    { imageSrc: "/alumni-chapters/sivagangai.jpg", chapterName: "SIVAGANGAI CHAPTER" }, // Example, adjust as per design
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/usa.jpg", chapterName: "USA CHAPTER" },
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/Bahrain.jpg", chapterName: "BAHRAIN CHAPTER" },
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/riyadh.jpg", chapterName: "RIYADH CHAPTER" },
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/malaysia.jpg", chapterName: "MALAYSIA CHAPTER" },
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/thenkasi.jpg", chapterName: "TENKASI CHAPTER" },
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/uk.jpg", chapterName: "UNITED KINGDOM CHAPTER" },
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/kerala.jpg", chapterName: "KONGU NADU CHAPTER" },
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/singapore.jpg", chapterName: "SINGAPORE CHAPTER" },
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/bangalore.jpg", chapterName: "BENGALURU CHAPTER" },
    { imageSrc: "/alumni-chapters/dubai.jpg", chapterName: "DUBAI CHAPTER" }, // Example, adjust as per design
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/qatar.jpg", chapterName: "QATAR CHAPTER" },
    { imageSrc: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Alumni/Home/jmcalumni.jpg", chapterName: "JMC STAFF ALUMNI CHAPTER" },
  ];

  return (
    <section className="py-12 md:pt-12 md:pb-24 px-4 md:px-20 bg-gray-50">
      <div className="container mx-auto">
        <div className="text-center mb-10 md:mb-16">
          <h2 className="text-3xl md:text-4xl font-ramilas font-semibold text-green-800">
            Alumni Chapter
          </h2>
          <p className="text-gray-600 mt-2 max-w-2xl mx-auto font-poppins text-sm md:text-base">
            The Khajamian Alumni Network is a well-designed complex with multiple talents, including entrepreneurial and member-led 
            facilities. It provides a conducive environment for continuous learning and holistic training.
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-6 md:gap-8">
          {chapters.map((chapter, index) => (
            <ChapterCard key={index} imageSrc={chapter.imageSrc} chapterName={chapter.chapterName} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default AlumniChapters;