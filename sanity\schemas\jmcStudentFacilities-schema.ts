const jmcStudentFacilitiesSchema = {
  name: "jmcstudentfacilities",
  title: "JMC Student Facilities",
  type: "document",
  fields: [
    {
      name: "studentCounsellingImages",
      title: "Student Counselling Centre Images",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "name",
              title: "Image Name",
              type: "string",
              description: "Name or description for the student counselling image",
            },
            {
              name: "imageUrl",
              title: "Image URL",
              type: "url",
              description: "URL of the student counselling image",
            },
          ],
        },
      ],
      description: "Array of student counselling centre images with names and URLs",
    },
    {
      name: "cafeteriaImages",
      title: "Cafeteria Images",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "name",
              title: "Image Name",
              type: "string",
              description: "Name or description for the cafeteria image",
            },
            {
              name: "imageUrl",
              title: "Image URL",
              type: "url",
              description: "URL of the cafeteria image",
            },
          ],
        },
      ],
      description: "Array of cafeteria images with names and URLs",
    },
    {
      name: "dayCareCentreImages",
      title: "Day Care Centre Images",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "name",
              title: "Image Name",
              type: "string",
              description: "Name or description for the day care centre image",
            },
            {
              name: "imageUrl",
              title: "Image URL",
              type: "url",
              description: "URL of the day care centre image",
            },
          ],
        },
      ],
      description: "Array of day care centre images with names and URLs",
    },
    {
      name: "transportImages",
      title: "Transport Images",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "name",
              title: "Image Name",
              type: "string",
              description: "Name or description for the transport image",
            },
            {
              name: "imageUrl",
              title: "Image URL",
              type: "url",
              description: "URL of the transport image",
            },
          ],
        },
      ],
      description: "Array of transport images with names and URLs",
    },
    {
      name: "communicationLabImages",
      title: "Communication Lab Images",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "name",
              title: "Image Name",
              type: "string",
              description: "Name or description for the communication lab image",
            },
            {
              name: "imageUrl",
              title: "Image URL",
              type: "url",
              description: "URL of the communication lab image",
            },
          ],
        },
      ],
      description: "Array of communication lab images with names and URLs",
    },
    {
      name: "sportsImages",
      title: "Sports Images",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "name",
              title: "Image Name",
              type: "string",
              description: "Name or description for the sports image",
            },
            {
              name: "imageUrl",
              title: "Image URL",
              type: "url",
              description: "URL of the sports image",
            },
            {
              name: "title",
              title: "Sports Title",
              type: "string",
              description: "Title for the sports activity (e.g., Sports Indoor, Sports Outdoor)",
            },
            {
              name: "description",
              title: "Sports Description",
              type: "text",
              description: "Description of the sports activity",
            },
          ],
        },
      ],
      description: "Array of sports images with names, URLs, titles, and descriptions",
    },
  ],
};

export default jmcStudentFacilitiesSchema;
