import React from "react";
import ProjectsYearlyTable, { 
  ProjectTableSection, 
  UgcMinorProject, 
  TnscstStudentProject 
} from "../../components/tables/ProjectsYearlyTable";

const AdditionalProjects = () => {
  // UGC Minor Research Project data
  const ugcMinorProjects: UgcMinorProject[] = [
    {
      serialNo: 1,
      particulars: "Dr<PERSON> <PERSON><PERSON>, Dept. of Chemistry",
      amount: "4,80,000"
    },
    {
      serialNo: 2,
      particulars: "<PERSON><PERSON> <PERSON><PERSON>, Dept. of Commerce",
      amount: "1,29,000"
    },
    {
      serialNo: 3,
      particulars: "<PERSON><PERSON> <PERSON><PERSON>, Dept. of Economics",
      amount: "1,11,000"
    },
    {
      serialNo: 4,
      particulars: "Mr. <PERSON><PERSON><PERSON><PERSON>, Dept. of Economics",
      amount: "89,000"
    },
    {
      serialNo: 5,
      particulars: "Dr<PERSON> <PERSON><PERSON>, Dept. of English",
      amount: "2,40,000"
    },
    {
      serialNo: 6,
      particulars: "<PERSON><PERSON> <PERSON><PERSON>, Dept. of Zoology",
      amount: "4,42,550"
    },
    {
      serialNo: 7,
      particulars: "<PERSON><PERSON> <PERSON><PERSON>, Dept. of Commerce",
      amount: "1,30,000"
    },
    {
      serialNo: 8,
      particulars: "Ms. G. <PERSON>ka, Dept. of Commerce",
      amount: "1,98,806"
    },
    {
      serialNo: 9,
      particulars: "Ms. Khairunnisa, Dept. of Information Tech.",
      amount: "2,00,000"
    },
    {
      serialNo: 10,
      particulars: "Ms. A.G. Meera, Dept. of Fashion Technology",
      amount: "1,15,000"
    },
    {
      serialNo: 11,
      particulars: "Ms. V. Kavitha, Dept. of Nutrition & Dietetics",
      amount: "1,70,000"
    },
    {
      serialNo: 12,
      particulars: "Mrs. B. Rajalakshmi, Dept. of Nutrition & Dietetics",
      amount: "1,19,500"
    },
    {
      serialNo: 13,
      particulars: "Ms. D. Bhuvaneswari, Dept. of Nutrition & Dietetics",
      amount: "1,16,000"
    }
  ];

  // TNSCST Student Projects 2017-2018
  const tnscstProjects2017: TnscstStudentProject[] = [
    {
      serialNo: 1,
      guideName: "Dr. A. Sangeetha, Dept. of N & D",
      studentNames: ["A. Fasila Begum"],
      amount: "10,000"
    }
  ];

  // TNSCST Student Projects 2018-2019
  const tnscstProjects2018: TnscstStudentProject[] = [
    {
      serialNo: 1,
      guideName: "Dr. A. Aslam, Dept. of Botany",
      studentNames: ["S. Perumal", "M. Pradeep Kumar"],
      amount: "7500"
    },
    {
      serialNo: 2,
      guideName: "T.Nargis Begum, Dept. of Bio. Tech.",
      studentNames: ["R. Ashraf Nisha"],
      amount: "7500"
    },
    {
      serialNo: 3,
      guideName: "Dr. A.Raja, Dept. of Microbiology",
      studentNames: ["A. Parveen"],
      amount: "7500"
    },
    {
      serialNo: 4,
      guideName: "Dr. V. Kavitha, Dept. of N&D",
      studentNames: ["G. Sowmiya"],
      amount: "7500"
    },
    {
      serialNo: 5,
      guideName: "Ms. B. Rajalakshmi, Dept. of N&D",
      studentNames: ["B. Madhumitha"],
      amount: "7500"
    }
  ];

  // Define table sections
  const tableSections: ProjectTableSection[] = [
    {
      id: "ugc-minor-2017-2019",
      title: "C. UGC MINOR RESEARCH PROJECT (2017-2019)",
      type: "ugc",
      year: "2017-2019",
      data: ugcMinorProjects
    },
    {
      id: "tnscst-2017-2018",
      title: "D. TNSCST STUDENT PROJECTS FOR THE YEAR 2017-2018",
      type: "tnscst",
      year: "2017-2018",
      data: tnscstProjects2017
    },
    {
      id: "tnscst-2018-2019",
      title: "E. TNSCST STUDENT PROJECTS FOR THE YEAR 2018-2019",
      type: "tnscst",
      year: "2018-2019",
      data: tnscstProjects2018
    }
  ];

  return (
    <section className="py-8 bg-[#F5F8F5]">
      <div className="container mx-auto max-w-7xl px-4">
        <ProjectsYearlyTable sections={tableSections} />
      </div>
    </section>
  );
};

export default AdditionalProjects;
