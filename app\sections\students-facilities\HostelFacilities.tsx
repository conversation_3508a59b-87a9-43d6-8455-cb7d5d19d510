"use client";

import ImageGrid from "@/app/components/ImageGrid";
import { client } from "@/lib/sanity";
import { HOSTEL_FACILITIES_QUERY } from "@/sanity/lib/queries";
import { useEffect, useState } from "react";

const HostelFacilities = () => {
  const [facilites, setFacilities] = useState<any[]>([]);

  useEffect(() => {
    client.fetch(HOSTEL_FACILITIES_QUERY).then((data) => {
      setFacilities(data);
    });
  }, []);

  return (
    <section className="bg-white pb-10 px-4">
      <ImageGrid
        title="Hostel Facilities"
        description="The hostel provides well-maintained accommodations with essential amenities for comfortable living. It fosters a supportive atmosphere for academic growth and communal engagement."
        items={facilites}
      />
    </section>
  );
};

export default HostelFacilities;
