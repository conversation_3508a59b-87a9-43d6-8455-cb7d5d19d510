import { type SchemaTypeDefinition } from "sanity";
import { updates } from "../schemas/updates-schema";
import academicResponsibilitiesSchema from "../schemas/academicResponsibilities-schema";
import coursesSchema from "../schemas/courses-schema";
import facultySchema from "../schemas/facultyProfile-schema";
import overviewSchema from "../schemas/overview";
import newsletterSchema from "../schemas/newsletter-schema";
import clubSchema from "../schemas/clubs-schema";
import eventSchema from "../schemas/events-schema"; // Import the new schema
import noticeUpdateSchema from "../schemas/notice-update-schema";
import hostelInfraSchema from "../schemas/hostelInfra-schema";
import hostelFacilitiesSchema from "../schemas/hostelFacilities-schema";
import studentEnrichmentProgrammesSchema from "../schemas/studentEnrichmentProgrammesSchema";
import policySchema from "../schemas/policy-schema";
import annualReportSchema from "../schemas/annualReport-schema";
import generalInfoSchema from "../schemas/generalInfo-schema";
import accreditationSchema from "../schemas/accreditation-schema";
import valueAddedCoursesSchema from "../schemas/valueAddedCourses-schema"; // Import new schema
import eContentSchema from "../schemas/eContent-schema";
import departmentActivitiesSchema from "../schemas/departmentActivities-schema";
import handbook from "../schemas/handbookOnAutonomy-schema";
import annualReport from "../schemas/annualReportOfExamination-schema";
import calendar from "../schemas/calendar-schema";
import degreeExamSchedule from "../schemas/degreeExamSchedule-schema";
import womensHostelSchema from "../schemas/womensHostel-schema";

export const schema: { types: SchemaTypeDefinition[] } = {
  types: [
    updates,
    academicResponsibilitiesSchema,
    coursesSchema,
    facultySchema,
    overviewSchema,
    newsletterSchema,
    clubSchema,
    eventSchema,
    noticeUpdateSchema,
    policySchema,
    annualReportSchema,
    hostelInfraSchema,
    hostelFacilitiesSchema,
    studentEnrichmentProgrammesSchema,
    generalInfoSchema,
    valueAddedCoursesSchema,
    eContentSchema,
    departmentActivitiesSchema,
    accreditationSchema,
    handbook,
    annualReport,
    calendar,
    degreeExamSchedule,
    womensHostelSchema,
  ],
};
