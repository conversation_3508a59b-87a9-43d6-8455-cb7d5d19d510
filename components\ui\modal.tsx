"use client";

import { <PERSON><PERSON>, <PERSON>alogContent, DialogOverlay, DialogTitle, DialogClose } from "@/components/ui/dialog";
import Image from "next/image";
import { X } from "lucide-react";
import Link from "next/link";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose }) => {

  // Create a wrapper function for onClose to add logging
  const handleClose = () => {
    console.log("Modal close triggered!"); // Add this log
    onClose(); // Call the original onClose function
  };

  return (
    // Pass the wrapper function to onOpenChange
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogOverlay className="fixed inset-0 bg-black/50 z-[999]" />
      <DialogContent
        className="fixed left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%] z-[1000]
                   max-w-3xl w-[90vw] max-h-[90vh] md:max-h-[85vh] lg:max-h-[80vh] p-0 overflow-y-auto bg-custom-green rounded-xl border-none
                   shadow-[10px_10px_0px_0px_#5F765F,_20px_20px_0px_0px_#002E00]
                   max-[375px]:w-[95vw] max-[375px]:max-h-[95vh] max-[375px]:shadow-[5px_5px_0px_0px_#5F765F,_10px_10px_0px_0px_#002E00]"
      >
        <DialogTitle className="sr-only">Admission Notification</DialogTitle>

        {/* Main Content */}
        <div className="relative">
          {/* Decorative Elements - Add pointer-events-none to the container */}
          <div className="absolute inset-0 z-[1002] pointer-events-none">
            {/* Pattern or grid background */}
            {/* <div className="absolute inset-0 opacity-10 bg-[url('/homepage/contentbgpattern.png')]" /> */}
            {/* Wrap the button with DialogClose */}
            <DialogClose asChild>
              <button
                className="absolute top-4 right-4 text-white border border-white rounded-full px-4 py-2 transition-all hover:bg-white hover:text-custom-green z-[1004] cursor-pointer focus:outline-none focus:ring-0 pointer-events-auto
                           max-[375px]:top-2 max-[375px]:right-2 max-[375px]:px-2 max-[375px]:py-1"
              >
                <X size={24} className="max-[375px]:w-5 max-[375px]:h-5" />
                <span className="sr-only">Close dialog</span>
              </button>
            </DialogClose>
            {/* Decorative Images - Add pointer-events-none */}
            <Image
              src="/homepage/modalimg1.png"
              alt="Decorative Image 1"
              width={150}
              height={150}
              // Corrected typo and added responsive hiding/showing
              className="absolute bottom-[11rem] md:bottom-[11rem] lg:bottom-[11rem] left-4 lg:left-8 w-[150px] h-[150px] md:w-[180px] md:h-[180px] lg:w-[200px] lg:h-[200px]  hidden md:block pointer-events-none"
              priority
            />
            <Image
              src="/homepage/modalimg2.png"
              alt="Decorative Image 2"
              width={150}
              height={150}
              className="absolute bottom-[11rem] md:bottom-[11rem] lg:bottom-[11rem] right-4 lg:right-8 w-[150px] h-[150px] md:h-[180px] md:w-[180px] lg:w-[200px] lg:h-[200px] max-[375px]:hidden hidden md:block pointer-events-none"
              priority
            />
          </div>

          {/* Card */}
          {/* Keep original z-index */}
          <div className="bg-white/20 backdrop-blur-sm rounded-xl m-2 p-6 relative z-[1001] min-h-[400px] md:min-h-[450px] lg:min-h-[380px] flex items-center lg:items-start
                          max-[375px]:m-1 max-[375px]:p-3 max-[375px]:min-h-[280px]">


            <div className="flex flex-col md:flex-row items-center justify-center w-full gap-0">
              {/* Left Side - College Logo */}
              <div className="md:w-1/3 flex justify-end mb-4 md:mb-0">
                <Image
                  src="/jmc_logo 2.svg"
                  alt="JMC Logo"
                  width={180}
                  height={180}
                  className="w-auto h-auto md:w-[150px] md:h-[150px] max-[375px]:w-[100px] max-[375px]:h-[100px]"
                  priority
                />
              </div>

              {/* Right Side - Content */}
              <div className="md:w-2/3 text-center md:text-left text-white min-[375px]:max-[375px]:pb-0 md:pb-0 md:pl-8 max-[375px]:pl-0">
                <h2 className="text-xl sm:text-xl md:text-2xl font-bold text-white mb-2 max-[375px]:text-lg max-[375px]:mb-1">
                  Admission Notification 2025-26
                </h2>
                {/* Updated list items with white underline and cursor pointer */}
                <p className="text-lg mb-2 md:mb-4 flex items-center justify-center md:justify-start max-[375px]:text-sm max-[375px]:mb-1">
                  <span className="w-2 h-2 bg-white rounded-full mr-2 max-[375px]:w-1.5 max-[375px]:h-1.5"></span>
                  <Link
                    href="#"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline underline-offset-4 decoration-white hover:text-gray-200 transition-colors cursor-pointer"
                  >
                    B.com International Finance
                  </Link>
                </p>
                <p className="text-lg mb-2 md:mb-4 flex items-center justify-center md:justify-start max-[375px]:text-sm max-[375px]:mb-1">
                  <span className="w-2 h-2 bg-white rounded-full mr-2 max-[375px]:w-1.5 max-[375px]:h-1.5"></span>
                  <Link
                    href="https://jmcerp.in/appliaided/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline underline-offset-4 decoration-white hover:text-gray-200 transition-colors cursor-pointer"
                  >
                    Aided
                  </Link>
                </p>
                <p className="text-lg mb-2 md:mb-4 flex items-center justify-center md:justify-start max-[375px]:text-sm max-[375px]:mb-1">
                  <span className="w-2 h-2 bg-white rounded-full mr-2 max-[375px]:w-1.5 max-[375px]:h-1.5"></span>
                  <Link
                    href="https://jmcerp.in/applisfm/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline underline-offset-4 decoration-white hover:text-gray-200 transition-colors cursor-pointer"
                  >
                    Self Finance Men
                  </Link>
                </p>
                <p className="text-lg mb-2 md:mb-4 flex items-center justify-center md:justify-start max-[375px]:text-sm max-[375px]:mb-1">
                  <span className="w-2 h-2 bg-white rounded-full mr-2 max-[375px]:w-1.5 max-[375px]:h-1.5"></span>
                  <Link
                    href="https://jmcerp.in/applisfw/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline underline-offset-4 decoration-white hover:text-gray-200 transition-colors cursor-pointer"
                  >
                    Self Finance Women
                  </Link>
                </p>
              </div>
            </div>
          </div>

          {/* Buttons - Below Card */}
          <div className="bg-white w-full p-6 relative z-[1003] mt-8 max-[375px]:p-3 max-[375px]:mt-4">
            <div className="flex flex-col md:flex-row gap-4 justify-center max-[375px]:gap-2">
              {/* Wrapped button with Link, added target and rel */}
              <Link
                href="/admission"
              >
                <button className="border-2 border-custom-green text-custom-green hover:bg-custom-green hover:text-white font-medium py-3 px-8 rounded-full min-w-[240px] w-full transition-colors
                                   max-[375px]:py-2 max-[375px]:px-4 max-[375px]:text-sm max-[375px]:min-w-0">
                  Admission 2025-26
                </button>
              </Link>
              {/* Wrapped button with Link, added target and rel */}
              <Link
                href="https://www.youtube.com/watch?v=vpUr5vmcM9g" // Replace with actual URL
                target="_blank"
                rel="noopener noreferrer"
              >
                <button className="border-2 border-custom-green text-custom-green hover:bg-custom-green hover:text-white font-medium py-3 px-8 rounded-full min-w-[240px] w-full transition-colors
                                   max-[375px]:py-2 max-[375px]:px-4 max-[375px]:text-sm max-[375px]:min-w-0">
                  Procedure To Fill Admission Form
                </button>
              </Link>
            </div>

            {/* Full Width Button - New Row */}
            <div className="mt-4 max-[375px]:mt-2 lg:px-12">
              <Link
                href="https://jmcerp.in/printapplication/"
                target="_blank"
                rel="noopener noreferrer"
                className="block"
              >
                <button className="border-2 border-custom-green text-custom-green hover:bg-custom-green hover:text-white font-medium py-3 px-8 rounded-full w-full transition-colors
                                   max-[375px]:py-2 max-[375px]:px-4 max-[375px]:text-sm">
                  Click to Print Online Register Application
                </button>
              </Link>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default Modal;
