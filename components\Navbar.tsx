"use client";

import {
  Menu,
  X,
  ArrowUpRight,
  Search,
  Phone,
  LockKeyhole,
  Wallet,
  Plus, // Add Plus icon for FAB
  ChevronUp, // Optional: for indicating open FAB menu
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect, ChangeEvent } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useRouter, usePathname } from "next/navigation";
import { getDepartments } from "@/sanity/lib/getDepartments"; // Adjust path if necessary
import ERPModal from "./ui/ERPModal";
import TermsModal from "./ui/TermsModal";

// First, let's add a type definition for menu items
interface MenuItem {
  title: string;
  href: string;
  subSections?: MenuItem[];
  disabled?: boolean;
}

export const menuItems: MenuItem[] = [
  { title: "Home", href: "/" },
  {
    title: "About Us",
    href: "/aboutUs",
    subSections: [
      { title: "About JMC", href: "/aboutUs#about_jmc" },
      { title: "Honoris Causa", href: "/aboutUs#honoris_causa" },
      { title: "Principal Profile", href: "/aboutUs#principal-profile" },
      {
        title: "Academic Responsibilities",
        href: "/aboutUs#academic-responsibilites",
      },
      {
        title: "Accreditation",
        href: "/aboutUs#accreditation",
      },
      { title: "Policies", href: "/aboutUs#policies" },
      { title: "Strategic Plan", href: "/aboutUs#strategic-plan" },
      { title: "Annual Report", href: "/aboutUs#annual-report" },
    ],
  },
  {
    title: "Courses",
    href: "/courses", // Main link for the courses page
    // subSections are now fetched dynamically
  },
  { title: "Placement", href: "/placement" },
  {
    title: "Admission",
    href: "/admission",
    subSections: [
      {
        title: "General Instructions",
        href: "/admission#general-instructions",
      },
      {
        title: "Programmes Offered 2025-2026",
        href: "/admission#programs-offered2025-2026",
      },
      { title: "ADMISSSION 2025-2026", href: "/admission#admission2025-2026" },
      {
        title: "Online Admission 2025-2026",
        href: "/admission#online-admission",
      },
    ],
  },
  {
    title: "Student's corner",
    href: "/students",
    subSections: [
      {
        title: "Student's Facilities",
        href: "/students-corner/students-facilities",
      },
      {
        title: "Student's Activities",
        href: "/students-corner/students-activities",
      },
      { title: "News Letters", href: "/students-corner/newsletters" },
      {
        title: "Student's Amenities",
        href: "/students-corner/students-amenities",
      },
    ],
  },
  { title: "Library", href: "/students-corner/library" },
  { title: "Examination", href: "/examination" },
  { title: "Research", href: "/research" },
  { title: "RTI", href: "/rti", disabled: true },
  { title: "Certification", href: "/accreditation" },
  { title: "Contact Us", href: "/contactUs" },
  {
    title: "Others",
    href: "/iqac",
    subSections: [
      { title: "IQAC", href: "/iqac" },
      { title: "NIRF", href: "/nirf" },
      { title: "DBT", href: "/dbt" },
      { title: "ARIIA", href: "/ariia" },
    ],
  },
];

const menuVariants = {
  closed: {
    opacity: 0,
    transition: {
      duration: 0.2,
    },
  },
  open: {
    opacity: 1,
    transition: {
      duration: 0.3,
    },
  },
};

const menuItemVariants = {
  closed: { opacity: 0, x: 20 },
  open: (i: number) => ({
    opacity: 1,
    x: 0,
    transition: {
      delay: i * 0.1,
    },
  }),
};

const subsectionVariants = {
  closed: { opacity: 0 },
  open: (i: number) => ({
    opacity: 1,
    transition: {
      delay: i * 0.05,
    },
  }),
};

// Variants for FAB menu animation
const fabMenuContainerVariants = {
  open: {
    transition: { staggerChildren: 0.07, delayChildren: 0.1 },
  },
  closed: {
    transition: {
      staggerChildren: 0.05,
      staggerDirection: -1,
      when: "afterChildren",
    },
  },
};

const fabMenuItemVariants = {
  open: {
    y: 0,
    opacity: 1,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 20,
    },
  },
  closed: {
    y: 20,
    opacity: 0,
    scale: 0.9,
    transition: {
      duration: 0.15,
    },
  },
};

export default function Navbar({
  className,
  fixed = true,
  border = true,
}: any) {
  const pathname = usePathname();
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isERPModalOpen, setIsERPModalOpen] = useState(false);
  const [isTermsModalOpen, setIsTermsModalOpen] = useState(false);
  const [isPayFeeModalOpen, setIsPayFeeModalOpen] = useState(false); // Add missing state
  const [isErpModalOpenForPayFee, setIsErpModalOpenForPayFee] = useState(false); // For Pay Fee ERP flow
  const [isFabMenuOpen, setIsFabMenuOpen] = useState(false); // State for FAB dropdown
  const [courseSearchQuery, setCourseSearchQuery] = useState("");
  const [filteredCourses, setFilteredCourses] = useState<MenuItem[]>([]);
  const [dynamicCourses, setDynamicCourses] = useState<MenuItem[]>([]); // State for fetched courses

  // Find the active section based on the current pathname
  const findActiveSection = () => {
    // First try to match exact paths
    let active = menuItems.find((item) => item.href === pathname);

    // If no exact match, try to match parent paths for nested routes
    if (!active) {
      active = menuItems.find((item) => {
        if (pathname.startsWith(item.href) && item.href !== "/") {
          return true;
        }
        // Check hardcoded subsections (for non-course items)
        if (item.subSections) {
          return item.subSections.some((sub) => pathname === sub.href);
        }
        // Special check for dynamic course pages
        if (item.title === "Courses" && pathname.startsWith("/courses/")) {
          return true;
        }
        return false;
      });
    }
    return active || menuItems[0];
  };

  const [selectedSection, setSelectedSection] =
    useState<MenuItem>(findActiveSection());
  const [showSubmenu, setShowSubmenu] = useState(false);

  // Effect to fetch courses from Sanity on component mount
  useEffect(() => {
    async function fetchCourses() {
      try {
        const departments = await getDepartments();
        console.log("Fetched Departments from Sanity:", departments); // Log the raw data

        const formattedCourses = departments
          .filter((dept) => dept.name && dept.name !== "Unnamed Department") // Filter out unnamed departments
          .map((dept) => ({
            // Prepend "Department Of " to the title
            title: `Department Of ${dept.name}`,
            href: `/courses/${dept.slug}`,
          }));

        setDynamicCourses(formattedCourses);

        const currentActiveSection = findActiveSection();
        if (currentActiveSection.title === "Courses") {
          setFilteredCourses(formattedCourses);
        }
      } catch (error) {
        console.error("Failed to fetch courses from Sanity:", error);
      }
    }
    fetchCourses();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Run only once on mount

  // Update selected section when pathname changes
  useEffect(() => {
    const activeSection = findActiveSection();
    setSelectedSection(activeSection);

    // Initialize filtered courses ONLY if dynamic courses are loaded and section is Courses
    if (activeSection.title === "Courses" && dynamicCourses.length > 0) {
      setFilteredCourses(dynamicCourses);
    } else if (activeSection.subSections) {
      // Handle hardcoded subsections for other items
      setFilteredCourses(activeSection.subSections);
    } else {
      setFilteredCourses([]);
    }
    setCourseSearchQuery("");
  }, [pathname, dynamicCourses]); // Add dynamicCourses dependency

  // Effect to filter courses based on search query
  useEffect(() => {
    if (selectedSection?.title === "Courses") {
      if (courseSearchQuery.trim() === "") {
        setFilteredCourses(dynamicCourses); // Use dynamicCourses
      } else {
        const lowerCaseQuery = courseSearchQuery.toLowerCase();
        const filtered = dynamicCourses.filter(
          (
            course // Filter dynamicCourses
          ) => course.title.toLowerCase().includes(lowerCaseQuery)
        );
        setFilteredCourses(filtered);
      }
    } else {
      // For non-course sections, filter their hardcoded subSections if they exist
      const currentSubSections =
        menuItems.find((item) => item.title === selectedSection?.title)
          ?.subSections || [];
      if (courseSearchQuery.trim() === "") {
        setFilteredCourses(currentSubSections);
      } else {
        const lowerCaseQuery = courseSearchQuery.toLowerCase();
        const filtered = currentSubSections.filter((sub) =>
          sub.title.toLowerCase().includes(lowerCaseQuery)
        );
        setFilteredCourses(filtered);
      }
    }
  }, [courseSearchQuery, selectedSection, dynamicCourses]); // Add dynamicCourses dependency

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Function to handle course search input change
  const handleCourseSearchChange = (event: ChangeEvent<HTMLInputElement>) => {
    setCourseSearchQuery(event.target.value);
  };

  // Function to handle menu item click
  const handleMenuItemClick = (item: MenuItem) => {
    setSelectedSection(item);
    setCourseSearchQuery("");

    // Determine subsections (dynamic for Courses, static otherwise)
    const subsections =
      item.title === "Courses" ? dynamicCourses : item.subSections || [];

    if (item.title === "Courses") {
      setFilteredCourses(dynamicCourses); // Initialize with all dynamic courses
    } else if (item.subSections) {
      setFilteredCourses(item.subSections); // Initialize with static subsections
    } else {
      setFilteredCourses([]);
    }

    if (isMobile && subsections.length > 0) {
      setShowSubmenu(true);
    } else if (isMobile && subsections.length === 0) {
      setIsOpen(false);
    } else if (!isMobile && subsections.length === 0) {
      setIsOpen(false);
      router.push(item.href);
    }
  };

  // Function to go back to main menu
  const handleBackToMenu = () => {
    setShowSubmenu(false);
    setCourseSearchQuery(""); // Reset search on back
  };
  // Function to handle the transition from PayFee modal to Terms modal
  const handleProceedToTerms = () => {
    setIsPayFeeModalOpen(false); // Close the PayFee modal
    setIsTermsModalOpen(true); // Open the Terms modal
  };

  // Function to handle clicking a search result link
  const handleCourseLinkClick = () => {
    setIsOpen(false); // Close the main menu overlay
    setCourseSearchQuery(""); // Reset search query
  };

  return (
    <nav
      id="main-navbar"
      className={`${fixed ? `fixed top-4` : "relative"} w-full z-50 p-4`}
    >
      <div className="container max-w-7xl mx-auto">
        <div
          className={`${fixed ? `bg-white/80 backdrop-blur-sm px-4 lg:px-8 py-2` : ``}  ${border ? `rounded-full border-2 border-custom-green` : ``} flex justify-between items-center ${className}`}
        >
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2 xl:space-x-8">
            {" "}
            {/* Base space-x-2, xl:space-x-8 */}
            <Image
              src="/JMC logo.svg"
              alt="University Logo"
              width={40}
              height={40}
              className="w-12 h-12 lg:w-16 lg:h-16 xl:w-20 xl:h-20" /* Base w/h-12, xl:w/h-20 */
            />
            {/* Replaced Image with Text - Updated Styling (Responsive) */}
            <div className="flex flex-col text-center">
              {/* College Name: Changed max-width breakpoint */}
              <span className="text-sm max-[393px]:text-[12px] lg:text-3xl xl:text-4xl font-bold text-custom-green leading-tight tracking-normal xl:tracking-wider">
                JAMAL MOHAMED COLLEGE
              </span>
              {/* Subtitle: Changed max-width breakpoint */}
              <span className="text-[11px] max-[393px]:text-[9px] xl:text-md font-normal xl:font-bold text-custom-green leading-tight">
                (Autonomous) Tiruchirappalli-620020
              </span>
              {/* Accreditation/Affiliation - Responsive Line Break */}
              {/* Single line for XL screens - No change needed here */}
              <span className="hidden xl:inline text-[12px] font-medium text-custom-green/80 leading-tight mt-0.5">
                Accredited With A++ grade (4th cycle) with CGPA 3.69 out of 4.0
                | Affiliated to Bharathidasan University
              </span>
              {/* Two lines for smaller screens - Changed max-width breakpoint */}
              <span className="block xl:hidden text-[9px] max-[393px]:text-[7px] font-normal text-custom-green/80 leading-tight mt-0.5">
                Accredited With A++ grade (4th cycle) with CGPA 3.69 out of 4.0
              </span>
              <span className="block xl:hidden text-[9px] max-[393px]:text-[7px] font-normal text-custom-green/80 leading-tight">
                Affiliated to Bharathidasan University
              </span>
            </div>
          </Link>

          {/* Desktop Navigation - Responsive Spacing/Padding/Text Size */}
          <div className="flex items-center space-x-2 xl:space-x-4 font-ramilas font-bold">
            {" "}
            {/* Base space-x-2, xl:space-x-4 */}
            {/* Phone Icon */}
            {/* <button className="items-center space-x-2 border-2 border-custom-green text-custom-green px-3 py-3 rounded-full hover:bg-green-50 transition-colors hidden lg:flex">
              <Phone size={24} className="text-custom-green fill-custom-green" />
            </button> */}
            {/* ERP Button - Hidden on mobile (md:flex) */}
            <button
              onClick={() => setIsERPModalOpen(true)}
              className="items-center space-x-1 xl:space-x-4 border-2 border-custom-green text-custom-green px-4 xl:px-6 py-2 rounded-full hover:bg-green-50 transition-colors hidden md:flex"
            >
              <LockKeyhole size={18} className="text-custom-green" />
              <span className="text-sm xl:text-base">ERP</span>
            </button>
            {/* Pay Fee Button - Update onClick */}
            <button
              onClick={() => setIsPayFeeModalOpen(true)} // Updated to use isPayFeeModalOpen
              className={`items-center space-x-1 xl:space-x-4 border-2 border-custom-green bg-custom-green text-white px-4 xl:px-6 py-2 rounded-full hidden md:flex`} // Ensure it's hidden on mobile
            >
              <Wallet size={18} />
              <span className="text-sm xl:text-base">Pay fee Online</span>
            </button>
            {/* Existing Menu Button */}
            <button
              onClick={() => setIsOpen(true)}
              className="flex items-center space-x-1 xl:space-x-4 border-2 border-custom-green text-custom-green px-4 xl:px-6 py-2 rounded-full hover:bg-green-50 transition-colors overflow-hidden bg-white md:bg-transparent" /* Base px-4/space-x-1, xl:px-6/space-x-4 */
            >
              <Menu size={18} />
              <span className="hidden md:inline text-sm xl:text-base">
                Menu
              </span>{" "}
              {/* Added text-sm xl:text-base */}
            </button>
          </div>
        </div>
      </div>

      {/* Menu Overlay */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial="closed"
            animate="open"
            exit="closed"
            variants={menuVariants}
            className="fixed inset-0 flex"
          >
            {/* Background Image */}
            <div
              className="absolute inset-0 bg-[url('https://images.unsplash.com/photo-1541339907198-e08756dedf3f')] bg-cover bg-center"
              style={{ filter: "brightness(0.4)" }}
            />

            {/* Content Container */}
            <div className="relative w-full h-full flex flex-col md:flex-row">
              {/* Desktop Subsections - Left Side */}
              {!isMobile && selectedSection && (
                <motion.div
                  className="hidden md:flex w-2/3 h-full p-12 items-center justify-end font-poppins backdrop-blur-md"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  {/* Conditional Rendering based on selected section */}
                  {selectedSection.title === "Courses" ? (
                    // Custom UI for Courses (Uses filteredCourses state)
                    <div className="w-full max-w-md text-white flex flex-col h-[calc(100%-4rem)]">
                      {/* Search Bar */}
                      <div className="flex items-center bg-white/20 rounded-full p-1.5 mb-8 backdrop-blur-sm w-full flex-shrink-0">
                        {" "}
                        {/* Added flex-shrink-0 */}
                        <input
                          type="text"
                          placeholder="Search Course"
                          value={courseSearchQuery}
                          onChange={handleCourseSearchChange}
                          className="bg-transparent flex-grow px-2 lg:px-6 py-3 text-white placeholder-white/70 outline-none text-lg"
                        />
                        <button className="bg-custom-green text-white rounded-full px-8 py-3 font-semibold hover:bg-custom-green/90 transition-colors">
                          Search
                        </button>
                      </div>

                      {/* Scrollable Course List */}
                      <div className="space-y-3 mb-4 overflow-y-auto flex-grow pr-2">
                        {filteredCourses.map(
                          (
                            subSection,
                            i // Renders dynamic or static based on state
                          ) => (
                            <Link
                              key={subSection.title}
                              href={subSection.href}
                              className="flex items-center justify-end gap-4 py-3 border-b-2 border-white/50 hover:border-white/80 transition-colors group"
                              onClick={handleCourseLinkClick}
                            >
                              <span className="text-lg font-medium">
                                {subSection.title}
                              </span>
                              <ArrowUpRight
                                size={20}
                                className="opacity-70 group-hover:opacity-100 transition-opacity border border-white/70 rounded-lg p-1 w-6 h-6"
                              />
                            </Link>
                          )
                        )}
                        {filteredCourses.length === 0 && courseSearchQuery && (
                          <p className="text-white/70 text-center py-4">
                            No courses found matching "{courseSearchQuery}".
                          </p>
                        )}
                      </div>

                      {/* View All Button */}
                      <div className="text-center mt-auto flex-shrink-0">
                        {" "}
                        {/* Added mt-auto and flex-shrink-0 */}
                        <Link href="/courses" onClick={handleCourseLinkClick}>
                          <button className="border-2 border-white/80 text-white rounded-full px-8 py-2 hover:bg-white/10 transition-colors font-semibold">
                            View All
                          </button>
                        </Link>
                      </div>
                    </div>
                  ) : (
                    // Default Submenu UI for other sections (Uses selectedSection.subSections)
                    <div className="space-y-2 text-right">
                      {selectedSection.subSections?.map((subSection, i) => (
                        <motion.div
                          key={subSection.title}
                          custom={i}
                          variants={subsectionVariants}
                          className="border-b-2 border-white/80"
                        >
                          <Link
                            href={subSection.href}
                            className="block py-3 text-white text-lg hover:text-green-300 transition-colors font-semibold"
                            onClick={() => setIsOpen(false)}
                          >
                            {subSection.title}
                          </Link>
                        </motion.div>
                      ))}
                      {/* ... no further details message ... */}
                    </div>
                  )}
                </motion.div>
              )}

              {/* Mobile Submenu View / Desktop Main Menu */}
              <AnimatePresence mode="wait">
                {isMobile && showSubmenu ? (
                  <motion.div
                    className="w-full h-full bg-custom-green/20 backdrop-blur-md p-6 flex flex-col"
                    initial={{ x: "100%" }}
                    animate={{ x: 0 }}
                    exit={{ x: "100%" }}
                    transition={{ type: "tween" }}
                  >
                    {/* Back Button */}
                    <div className="flex justify-between items-center mb-6 border-b border-white pb-4 flex-shrink-0">
                      <button
                        onClick={handleBackToMenu}
                        className="text-white hover:text-green-300 transition-colors flex items-center gap-2"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="24"
                          height="24"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="lucide lucide-chevron-left"
                        >
                          <path d="m15 18-6-6 6-6" />
                        </svg>
                        Back
                      </button>
                      <span className="text-white text-xl font-semibold">
                        {selectedSection?.title}
                      </span>
                      {/* Close button for the entire menu */}
                      <button
                        onClick={() => setIsOpen(false)}
                        className="text-white hover:text-green-300 transition-colors"
                      >
                        <X size={24} />
                      </button>
                    </div>

                    {selectedSection?.title === "Courses" ? (
                      <>
                        {/* Search Bar (Stays at top) */}
                        <div className="flex items-center bg-white/20 rounded-full p-1 mb-6 backdrop-blur-sm flex-shrink-0">
                          {" "}
                          {/* Added flex-shrink-0 */}
                          <input
                            type="text"
                            placeholder="Search Course"
                            value={courseSearchQuery}
                            onChange={handleCourseSearchChange}
                            className="bg-transparent flex-grow px-3 py-2 text-white placeholder-white/70 outline-none text-sm"
                          />
                          <button className="bg-custom-green text-white rounded-full px-3 py-2 text-sm font-semibold hover:bg-custom-green/90 transition-colors">
                            Search
                          </button>
                        </div>

                        {/* Scrollable Course List */}
                        <div className="space-y-0 overflow-y-auto flex-grow mb-4">
                          {filteredCourses.map((subSection, i) => (
                            <div
                              key={subSection.title}
                              className="text-center py-4 border-b border-white/30" // Keep text-center here
                            >
                              <Link
                                href={subSection.href}
                                className="flex items-center text-white text-lg" // Removed justify-between
                                onClick={handleCourseLinkClick}
                              >
                                <span className="flex-grow text-left mr-2">
                                  {subSection.title}
                                </span>{" "}
                                {/* Added flex-grow, text-center, and margin-right */}
                                <ArrowUpRight
                                  size={24}
                                  className="border border-white rounded-lg p-1 flex-shrink-0" // Added flex-shrink-0
                                />
                              </Link>
                            </div>
                          ))}
                          {filteredCourses.length === 0 &&
                            courseSearchQuery && (
                              <p className="text-white/70 text-center py-4">
                                No courses found matching "{courseSearchQuery}".
                              </p>
                            )}
                        </div>

                        {/* View All Button (Stays at bottom) */}
                        <div className="mt-auto flex-shrink-0">
                          {" "}
                          {/* Added mt-auto and flex-shrink-0 */}
                          <Link href="/courses" onClick={handleCourseLinkClick}>
                            <button className="border-2 border-white text-white rounded-full px-8 py-3 hover:bg-white/10 transition-colors font-bold w-full text-lg ">
                              View All
                            </button>
                          </Link>
                        </div>
                      </>
                    ) : (
                      // Default Mobile Submenu for other sections (Uses selectedSection.subSections)
                      <div className="space-y-0 overflow-y-auto flex-grow">
                        {" "}
                        {/* Added flex-grow and overflow */}
                        {selectedSection?.subSections?.map((subSection, i) => (
                          <motion.div
                            key={subSection.title}
                            custom={i}
                            variants={menuItemVariants} // Reuse main menu item variants
                            className="text-center py-4 border-b border-white/30"
                          >
                            <Link
                              href={subSection.href}
                              className="flex items-center justify-between text-white text-lg"
                              onClick={() => setIsOpen(false)} // Close menu on link click
                            >
                              <span>{subSection.title}</span>
                              <ArrowUpRight
                                size={24}
                                className="border border-white rounded-lg p-1"
                              />
                            </Link>
                          </motion.div>
                        ))}
                        {!selectedSection?.subSections?.length && (
                          <p className="text-white/70 text-center py-4">
                            No further details available.
                          </p>
                        )}
                      </div>
                    )}
                  </motion.div>
                ) : (
                  // Main Menu (Desktop Right Side / Mobile Initial View)
                  <motion.div
                    className="w-full h-full bg-white p-10 flex flex-col md:w-1/3" // Removed md:ml-auto
                    initial={{ opacity: isMobile ? 0 : 1 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    <div className="flex justify-between items-center mb-4">
                      <h2 className="text-2xl font-semibold text-custom-green">
                        Menu
                      </h2>
                      <button
                        onClick={() => setIsOpen(false)}
                        className="border border-custom-green py-2 px-4 rounded-full hover:bg-gray-100 transition-colors"
                      >
                        <X size={24} />
                      </button>
                    </div>

                    <div className="space-y-0 flex-1 overflow-y-auto scrollbar-hide">
                      {menuItems.map((item, i) => (
                        <motion.div
                          key={item.title}
                          custom={i}
                          variants={menuItemVariants}
                          initial="closed"
                          animate="open"
                          exit="closed"
                        >
                          <button
                            className={`group flex items-center justify-between px-6 py-2 text-xl w-full text-left ${
                              item.disabled
                                ? "opacity-50 cursor-not-allowed"
                                : ""
                            } ${
                              !isMobile && selectedSection?.title === item.title
                                ? "text-white bg-custom-green rounded-full px-4 mb-4" // Highlight selected on desktop
                                : isMobile
                                  ? "text-custom-green border border-custom-green/30 rounded-full pl-12 mb-4" // Border for mobile items
                                  : "text-custom-green hover:text-custom-green/80 mb-4" //normal desktop items
                            }  transition-all`}
                            onClick={() =>
                              !item.disabled && handleMenuItemClick(item)
                            } // Only trigger if not disabled
                          >
                            {/* Conditionally render Link only on mobile for items without subsections (static or dynamic) */}
                            {isMobile &&
                            ((item.title !== "Courses" &&
                              !item.subSections?.length) ||
                              (item.title === "Courses" &&
                                dynamicCourses.length === 0)) &&
                            !item.disabled ? (
                              <Link
                                href={item.href}
                                className="font-medium w-full h-full block"
                                onClick={() => setIsOpen(false)} // Close menu on mobile navigation
                              >
                                {item.title}
                              </Link>
                            ) : (
                              <>
                                <span className="font-medium">
                                  {item.title}
                                  {item.disabled && (
                                    <span className="ml-2 text-sm">
                                      (Coming Soon)
                                    </span>
                                  )}
                                </span>
                                {/* Show arrow for active item on desktop or for items WITH subsections (static or dynamic) on mobile */}
                                {((!isMobile &&
                                  selectedSection?.title === item.title) ||
                                  (isMobile &&
                                    (item.subSections?.length ||
                                      (item.title === "Courses" &&
                                        dynamicCourses.length > 0)))) &&
                                  !item.disabled && (
                                    <span className="text-white text-2xl">
                                      →
                                    </span>
                                  )}
                              </>
                            )}
                          </button>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <ERPModal
        isOpen={isERPModalOpen}
        onClose={() => setIsERPModalOpen(false)}
      />
      <ERPModal
        isOpen={isPayFeeModalOpen}
        onClose={() => setIsPayFeeModalOpen(false)}
        type="payFee" // Set type to 'payFee'
        onProceed={handleProceedToTerms} // Pass the transition function
      />
      {/* Replace this section in your Navbar component */}
      <TermsModal
        isOpen={isTermsModalOpen}
        onClose={() => setIsTermsModalOpen(false)}
        // onAgree prop is removed since we're using a direct link
      />
      {/* Mobile Floating Action Button (FAB) */}
      {isMobile && !isOpen && (
        <div className="fixed bottom-6 right-6 z-[60] flex flex-col items-end">
          <AnimatePresence>
            {isFabMenuOpen && (
              <motion.div
                variants={fabMenuContainerVariants}
                className="mb-3 flex flex-col items-end space-y-2.5"
                initial="closed"
                exit="closed"
                animate="open"
              >
                <motion.button
                  variants={fabMenuItemVariants}
                  onClick={() => {
                    setIsERPModalOpen(true);
                    setIsFabMenuOpen(false);
                  }}
                  className="w-40 flex items-center justify-center gap-2 bg-white text-custom-green border border-custom-green px-4 py-2.5 rounded-full shadow-lg hover:bg-green-50 transition-colors duration-150 text-sm font-ramilas font-bold"
                >
                  <LockKeyhole size={16} />
                  <span>ERP</span>
                </motion.button>
                <motion.button
                  variants={fabMenuItemVariants}
                  onClick={() => {
                    setIsPayFeeModalOpen(true); // Updated to use isPayFeeModalOpen
                    setIsFabMenuOpen(false);
                  }}
                  className="w-40 flex items-center justify-center gap-2 bg-custom-green text-white border border-custom-green px-4 py-2.5 rounded-full shadow-lg hover:bg-custom-green/90 transition-colors duration-150 text-sm font-ramilas font-bold"
                >
                  <Wallet size={16} />
                  <span>Pay Fee</span>
                </motion.button>
              </motion.div>
            )}
          </AnimatePresence>
          <button
            onClick={() => setIsFabMenuOpen(!isFabMenuOpen)}
            className="bg-custom-green text-white w-14 h-14 rounded-full shadow-xl flex items-center justify-center hover:bg-custom-green/90 focus:outline-none focus:ring-2 focus:ring-custom-green focus:ring-opacity-50 transition-colors duration-150 transform hover:scale-105"
            aria-label="Open quick actions menu"
          >
            <AnimatePresence mode="wait" initial={false}>
              <motion.div
                key={isFabMenuOpen ? "x" : "plus"}
                initial={{ rotate: -45, opacity: 0, scale: 0.5 }}
                animate={{ rotate: 0, opacity: 1, scale: 1 }}
                exit={{
                  rotate: 45,
                  opacity: 0,
                  scale: 0.5,
                  transition: { duration: 0.15 },
                }}
                transition={{
                  duration: 0.2,
                  type: "spring",
                  stiffness: 260,
                  damping: 20,
                }}
              >
                {isFabMenuOpen ? <X size={24} /> : <Plus size={24} />}
              </motion.div>
            </AnimatePresence>
          </button>
        </div>
      )}
    </nav>
  );
}
