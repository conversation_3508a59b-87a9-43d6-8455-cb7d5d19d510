"use client";

const OnlineCertificateVerification = () => {
  return (
    <section
      className="relative px-4 py-16 text-white bg-cover bg-center"
      style={{ backgroundImage: "url('/jamal_college.jpeg')" }}
    >
      {/* Overlay */}
      <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

      <div className="relative max-w-7xl container mx-auto z-10">
        <div className="flex flex-col items-center text-center md:text-left md:items-center md:flex-row justify-between">
          <div>
            <h2 className="mb-4 font-ramilas text-4xl sm:text-3xl md:text-4xl font-bold">
              Online Certificate Verification
            </h2>
            <p className="max-w-4xl mb-8 md:mb-0 font-poppins">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
              eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut
              enim ad minim veniam, quis nostrud exercitation ullamco laboris
              nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in
              reprehenderit in voluptate velit esse cillum dol
            </p>
          </div>
          <div className="flex justify-center items-center gap-4">
            <a
              href="https://jmc.directverify.in/"
              target="_blank"
              rel="noopener noreferrer"
              className="font-bold font-poppins bg-white text-custom-green px-4 sm:px-6 md:px-8 py-3 sm:py-4 md:py-5 text-base md:text-xl rounded-xl shadow-[0px_0px_34px_0px_#00000026] flex items-center gap-2 sm:gap-4 md:gap-24 transition whitespace-nowrap"
            >
              Verify Your Certificate
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default OnlineCertificateVerification;
