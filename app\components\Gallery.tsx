"use client";

import React from "react";

const Gallery = () => {
  const yearButtons = [
    { year: "2017 - 2018", path: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Placement/Gallery/PLACEMENT-PHOTO-GALLERY-2017-2018.pdf" },
    { year: "2018 - 2019", path: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Placement/Gallery/PLACEMENT-PHOTO-GALLERY-2018-2019.pdf" },
    { year: "2019 - 2020", path: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Placement/Gallery/PLACEMENT-PHOTO-GALLERY-2019-2020.pdf" },
    { year: "2020 - 2021", path: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Placement/Gallery/PLACEMENT-PHOTO-GALLERY-2020-2021.pdf" },
    { year: "2021 - 2022", path: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/Placement/Gallery/PLACEMENT-PHOTO-GALLERY-2021-2022.pdf" },
  ];

  const handleButtonClick = (path: string) => {
    window.open(path, "_blank");
  };

  return (
    <section
      className="relative py-16 text-white bg-cover bg-center"
      style={{ backgroundImage: "url('/jamal_college.jpeg')" }}
    >
      {/* Overlay */}
      <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

      {/* Content */}
      <div className="relative z-10 px-8 text-center">
        {/* Title */}
        <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
          Gallery
        </h2>

        {/* Description */}
        <p className="text-white text-base md:text-lg leading-relaxed mb-12 px-8">
          Explore our journey of academic excellence and student achievements through the years. Browse year-wise highlights and
          discover how our Value Added Courses and placement initiatives have empowered students from 2017 to 2024.
        </p>

        {/* Year Buttons */}
        <div className="flex flex-wrap justify-center gap-4">
          {yearButtons.map((year, index) => (
            <button
              key={index}
              className="px-8 py-4 bg-white text-custom-green font-medium rounded-lg hover:bg-gray-100 transition-colors duration-200 text-sm md:text-lg"
              onClick={() => handleButtonClick(year.path)}
            >
              {year.year}
            </button>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Gallery;
