const calendar = {
  name: "calendar",
  title: "Examination - Calendar",
  type: "document",
  fields: [
    {
      name: "period",
      title: "Period (e.g. 2023 - 2024)",
      type: "string",
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: "pdfUrl",
      title: "Calendar (PDF URL)",
      type: "url",
      validation: (Rule: any) =>
        Rule.required().uri({
          allowRelative: false,
          scheme: ["http", "https"],
        }),
    },
  ],
};

export default calendar;
