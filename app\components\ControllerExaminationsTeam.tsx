import React from "react";
import Image from "next/image";

interface TeamMemberProps {
  image: string;
  name: string;
  title: string;
  description: string;
}

const TeamMemberCard: React.FC<TeamMemberProps> = ({
  image,
  name,
  title,
  description,
}) => {
  return (
    <div className="flex flex-col md:flex-row overflow-hidden mb-3 sm:mb-4 px-2 sm:px-4">
      <div className="w-2/3 sm:w-1/3 md:w-1/4 lg:w-1/5 mx-auto md:mx-0 mb-4 md:mb-0">
        <div className="aspect-square relative">
          <Image
            src={image}
            alt={name}
            width={300}
            height={300}
            className="object-cover object-center w-full h-full rounded-lg"
          />
        </div>
      </div>
      <div className="w-full md:w-3/4 lg:w-4/5 p-4 sm:p-6">
        <h3 className="text-xl sm:text-2xl font-bold text-[#002E00] mb-2">{name}</h3>
        <p className="text-sm sm:text-base text-[#555555] mb-2">
          <span className="font-semibold">{title}. </span>
          {description}
        </p>
      </div>
    </div>
  );
};

interface ControllerExaminationsTeamProps {
  title?: string;
}

const ControllerExaminationsTeam: React.FC<ControllerExaminationsTeamProps> = ({
  title = "Controller of Examinations"
}) => {
  const teamMembers: TeamMemberProps[] = [
    {
      image: "/research/team/placeholder.png",
      name: "Dr. A. Mohamed Sindhasha",
      title: "Department of Commerce",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum est.",
    },
    {
      image: "/research/team/placeholder.png",
      name: "Dr. M. I. Fazal Mohamed",
      title: "Department of Chemistry",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum est.",
    },
    {
      image: "/research/team/placeholder.png",
      name: "Dr. A. Mohamed Ibraheem",
      title: "Department of English",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum est.",
    },
    {
      image: "/research/team/placeholder.png",
      name: "Dr. J. Abdul Razak",
      title: "Department of Computer Science",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum est.",
    },
    {
      image: "/research/team/placeholder.png",
      name: "Dr. D.L George Amalarethinam",
      title: "Department of Computer Science",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum est.",
    },
    {
      image: "/research/team/placeholder.png",
      name: "Dr. M. Syed Ali Padusha",
      title: "Department of Chemistry",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum est.",
    },
  ];

  return (
    <section className="py-8 sm:py-12 md:py-16 bg-[#EBEBEB]">
      <h2 className="text-2xl sm:text-3xl font-bold text-center text-[#002E00] mb-4 sm:mb-8 px-4">
        {title}
      </h2>
      <div className="container max-w-7xl mx-auto px-2 sm:px-4">
        <div className="space-y-3 sm:space-y-4">
          {teamMembers.map((member, index) => (
            <TeamMemberCard
              key={index}
              image={member.image}
              name={member.name}
              title={member.title}
              description={member.description}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ControllerExaminationsTeam;
