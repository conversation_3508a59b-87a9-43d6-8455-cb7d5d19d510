import React from "react";
import Image from "next/image";
import Link from 'next/link';

const AlumniAssociation = () => {
    const alumniImages = [
        "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Alumni/01.jpg",
        "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Alumni/02.jpg",
        "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Alumni/03.jpg",
        "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Alumni/04.jpg",
    ];

    return (
        <section className="py-12 bg-white relative">
            <div >
                {/* Top Row */}
                <div className="mb-16 bg-white rounded-lg p-8 shadow-[0_0_24px_rgba(0,0,0,0.1)] pb-16">
                    <h2 className="text-3xl md:text-4xl font-bold text-center text-custom-green mb-6">
                        <span className="md:hidden">ALUMNI<br />ASSOCIATION</span>
                        <span className="hidden md:inline">ALUMNI ASSOCIATION</span>
                    </h2>

                    <p className="text-gray-700 text-center max-w-6xl mx-auto mb-4">
                        Our Alumni Association is a cornerstone of Jamal Mohamed College, fostering lifelong connections between our alumni and current students. The network offers valuable opportunities for mentorship, career growth, and engagement through events, reunions, and collaborations. We are proud of our alumni's continued success and their support in shaping the future of our institution.
                    </p>
                </div>

                {/* Centered Explore Button with Logo - Overlay */}
                <div className="relative z-20 flex justify-center -mt-28 -mb-8">
                    <div className="bg-white rounded-lg p-6 shadow-[0_0_25px_rgba(0,0,0,0.2)]">
                        <div className="flex items-center gap-3">
                            <div className="flex items-center">
                                <Image
                                    src="/homepage/aluminiassocimg1.png"
                                    alt="Alumni Association Logo"
                                    width={60}
                                    height={60}
                                    className="object-contain"
                                />
                            </div>
                            {/* Updated href to /alumni, removed target and rel */}
                            <Link href="/alumni"> 
                                <button className="bg-custom-green text-white px-6 py-2 rounded-full font-medium">
                                    Explore Alumni
                                </button>
                            </Link>
                        </div>
                    </div>
                </div>

                {/* Alumni Images Grid - Bottom Row */}
                <div className="container mx-auto max-w-7xl md:px-6 pt-14">
                    <div className="px-4 flex gap-x-4 md:grid md:grid-cols-4 md:gap-4 overflow-x-auto pb-4 scrollbar-hide"> {/* Added gap-x-4 for mobile */} 
                        {alumniImages.map((image, index) => (
                            <div
                                key={index}
                                className="relative h-28 md:h-48 lg:h-56 min-w-[150px] w-[150px] md:w-auto flex-shrink-0 overflow-hidden rounded-2xl"
                            >
                                <Image
                                    src={image}
                                    alt={`Alumni ${index + 1}`}
                                    fill
                                    className="object-obtain"
                                    priority={index < 2}
                                    onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.src = `https://images.unsplash.com/photo-${1550000000000 + index}?w=500&auto=format&fit=crop&q=60`;
                                    }}
                                />
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </section>
    );
};

export default AlumniAssociation;