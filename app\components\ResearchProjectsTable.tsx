import React from "react";

interface ProjectSchemeData {
  id: number;
  name: string;
  departments: string;
  amount: string;
  fundingAgency: string;
}

interface ResearchProjectsTableProps {
  title: string;
  subtitle: string;
  schemes: ProjectSchemeData[];
}

const ResearchProjectsTable: React.FC<ResearchProjectsTableProps> = ({
  title,
  subtitle,
  schemes = [],
}) => {
  return (
    <div className="mt-8">
      <h2 className="text-3xl font-bold text-center text-[#002E00] mb-6">
        {title}
      </h2>

      <h3 className="bg-[#D1D9D1] text-lg sm:text-xl font-semibold text-[#002E00] pt-6 sm:pt-8 px-4 sm:px-8">
        {subtitle}
      </h3>

      <div className="bg-[#D1D9D1]">
        <div className="rounded-lg overflow-hidden">
          {/* Add overflow container for mobile responsiveness */}
          <div className="overflow-x-auto">
            <div className="min-w-[768px]"> {/* Minimum width to prevent squishing on mobile */}
              {/* Header with margin */}
              <div className="mx-4 my-4">
                <div className="bg-[#002E00] text-white rounded-full">
                  <div className="grid grid-cols-4 w-full">
                    <div className="py-4 px-6 text-left font-medium text-xs sm:text-sm md:text-base whitespace-normal break-words">NAME OF THE SCHEME</div>
                    <div className="py-4 px-6 text-left font-medium text-xs sm:text-sm md:text-base whitespace-normal break-words">DEPARTMENTS</div>
                    <div className="py-4 px-6 text-left font-medium text-xs sm:text-sm md:text-base whitespace-normal break-words">AMOUNT SANCTIONED (IN INR)</div>
                    <div className="py-4 px-6 text-left font-medium text-xs sm:text-sm md:text-base whitespace-normal break-words">FUNDING AGENCY</div>
                  </div>
                </div>
              </div>

              {/* Table content */}
              <table className="min-w-full">
                <tbody>
                  {schemes.map((scheme) => (
                    <tr key={scheme.id} className="border-b border-[#555555]">
                      <td className="py-3 px-6 text-left text-[#555555] w-1/4 text-xs sm:text-sm md:text-base whitespace-normal break-words">{scheme.name}</td>
                      <td className="py-3 px-6 text-left text-[#555555] w-1/4 text-xs sm:text-sm md:text-base whitespace-normal break-words">{scheme.departments}</td>
                      <td className="py-3 px-6 text-left text-[#555555] w-1/4 text-xs sm:text-sm md:text-base whitespace-normal break-words">{scheme.amount}</td>
                      <td className="py-3 px-6 text-left text-[#555555] w-1/4 text-xs sm:text-sm md:text-base whitespace-normal break-words">{scheme.fundingAgency}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResearchProjectsTable;
