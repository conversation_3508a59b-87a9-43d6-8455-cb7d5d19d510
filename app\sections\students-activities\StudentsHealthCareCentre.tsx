"use client";

import { Profile, ProfileCard } from "@/app/components/ProfileCard";

const StudentsHealthCareCentre = () => {
  const profiles: Profile[] = [
    {
      name: "Dr.<PERSON><PERSON>",
      role: "Coordinator",
      image: "https://jmc.edu/include/studenthealth/img/ZAHIR.png",
      qualification: "M.<PERSON>., M.Phil., M.Ed., M.Phil.(Edn)., Ph.D., PGDGC.,",
    },
    {
      name: "Dr. <PERSON><PERSON>",
      role: "Member",
      image: "https://jmc.edu/include/studenthealth/img/sebastinraj.png",
      qualification: "M.Sc., Ph.D.,",
    },
  ];
  return (
    <section className="bg-white py-10 space-y-6">
      <div className="container max-w-7xl mx-auto space-y-6 px-4">
        <h2 className="text-custom-green">Students Health Care Centre</h2>
        <div className="rounded-2xl border border-gray-200 p-6 md:p-10 shadow-sm bg-white">
          <ul className="list-disc list-outside pl-6 space-y-4 text-custom-new-green text-fluid-base font-poppins leading-relaxed">
            <li>
              Attaining a state of complete physical, mental and social
              well-being of students
            </li>
            <li>
              Promoting and encourageing health related activities and to have
              stress free life style of the students
            </li>
            <li>Imparting the knowledge on health and wellness</li>
            <li>Inculcating first aid practices for our students</li>
            <li>Organising the Medical camps in the institutions</li>
            <li>
              Celebrating National and International health care related days
            </li>
          </ul>
        </div>
        <div className="flex flex-col sm:flex-row gap-4 font-ramilas font-medium">
          <a
            href="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/StudentsHealthCareCentre/Health-Care-activities-2019-20.pdf"
            target="_blank"
            rel="noopener noreferrer"
            className="flex-1 text-center bg-custom-green text-white px-10 py-3 rounded-lg text-lg hover:bg-green-800 transition-all text-[clamp(1.5rem,4vw,28px)]"
          >
            2019-20
          </a>
          <a
            href="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/StudentsHealthCareCentre/Health-Care-Activities-2021-22.pdf"
            target="_blank"
            rel="noopener noreferrer"
            className="flex-1 text-center bg-custom-green text-white px-10 py-3 rounded-lg text-lg hover:bg-green-800 transition-all text-[clamp(1.5rem,4vw,28px)]"
          >
            2021-22
          </a>
        </div>
      </div>
      <section className="bg-[#F7F8F7] py-10">
        <div className="container max-w-7xl mx-auto space-y-6 px-4">
          <h2 className="text-custom-green text-center">
            Members of Students Health Care Centre
          </h2>
          <div className="grid gap-4">
            {profiles.length > 0 ? (
              profiles.map((profile, index) =>
                profile && typeof profile === "object" ? (
                  <ProfileCard
                    key={index}
                    name={(profile as any).name}
                    role={(profile as any).role}
                    image={(profile as any).image}
                    description={(profile as any).description}
                    qualification={(profile as any).qualification}
                    swapHeaderOrder={true}
                  />
                ) : null
              )
            ) : (
              <p className="text-gray-500">No profiles available.</p>
            )}
          </div>
        </div>
      </section>
    </section>
  );
};

export default StudentsHealthCareCentre;
