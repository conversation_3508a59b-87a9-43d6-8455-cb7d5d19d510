// CommonTable.tsx
"use client";
import React from "react";

interface Member {
  name: string;
  year: string;
}

interface TableData {
  title: string;
  description?: string;
  headers: [string, string];
  members: Member[];
}

interface TableProps {
  data: TableData;
}

export default function TableCommon({ data }: TableProps) {
  const { title, description, headers, members } = data;
  return (
    <div className="container lg:max-w-6xl xl:max-w-7xl mx-auto text-center space-y-6 font-ramilas font-bold">
      <h3 className="text-custom-green">{title}</h3>
      {description && <p className="text-gray-600 mb-6">{description}</p>}
      <div className="overflow-x-auto flex justify-center pb-8">
        <table className="w-full max-w-sm sm:max-w-xl md:max-w-4xl lg:max-w-5xl mx-auto border-collapse rounded-2xl overflow-hidden font-medium relative border border-custom-green bg-custom-light-green md:shadow-milestone">
          <thead>
            <tr className="bg-custom-green text-white rounded-t-2xl">
              <th className="px-3 py-4 md:px-8 md:py-6 text-left border-l-0 border-r-0">
                {headers[0]}
              </th>
              <th className="px-3 py-4 md:px-8 md:py-6 text-right border-l-0 border-r-0">
                {headers[1]}
              </th>
            </tr>
          </thead>
          <tbody>
            {members.map((member, index) => (
              <tr
                key={index}
                className="border-b"
                style={{ borderBottom: "1px solid #00000033" }}
              >
                <td className="px-3 py-4 md:px-8 md:py-6 text-left border-l-0 border-r-0">
                  {member.name}
                </td>
                <td className="px-3 py-4 md:px-8 md:py-6 text-right border-l-0 border-r-0">
                  {member.year}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
