import React from "react";
import <PERSON> from "next/link";

interface SmallCard {
  title: string;
  items: string[];
}

interface DBTSmallCardsProps {
  cards: SmallCard[];
}

const DBTSmallCards: React.FC<DBTSmallCardsProps> = ({ cards }) => {
  // Function to check if a card is a GFR compliance card
  const isGFRCard = (title: string) => {
    return title.toLowerCase().includes("compliance with gfr");
  };

  // Function to check if it's the first GFR card (with multiple volumes)
  const isFirstGFRCard = (card: SmallCard) => {
    return isGFRCard(card.title) && card.items.some(item => item.includes("Volume"));
  };

  // Function to check if it's the second GFR card (with single compliance rule)
  const isSecondGFRCard = (card: SmallCard) => {
    return isGFRCard(card.title) && !card.items.some(item => item.includes("Volume"));
  };

  // Function to render items based on card type
  const renderItems = (card: SmallCard) => {
    if (isFirstGFRCard(card)) {
      // For first GFR card, render items in a grid with 2 items per row
      return (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
          {card.items.map((item, itemIndex) => (
            <Link
              key={itemIndex}
              href="#"
              className="flex items-center space-x-2 p-2 "
            >
              <span className="text-custom-green font-bold flex-shrink-0">•</span>
              <span className="text-[#555555] text-sm md:text-base font-semibold  leading-relaxed">
                {item}
              </span>
            </Link>
          ))}
        </div>
      );
    } else if (isSecondGFRCard(card)) {
      // For second GFR card, render items in single column (1 link per row)
      return (
        <ul className="space-y-3">
          {card.items.map((item, itemIndex) => (
            <li key={itemIndex}>
              <Link
                href="#"
                className="flex items-center space-x-2 p-2"
              >
                <span className="text-custom-green font-bold flex-shrink-0">•</span>
                <span className="text-[#555555] text-sm md:text-base font-semibold  leading-relaxed">
                  {item}
                </span>
              </Link>
            </li>
          ))}
        </ul>
      );
    } else {
      // For regular cards, render items normally
      return (
        <ul className="space-y-3">
          {card.items.map((item, itemIndex) => (
            <li key={itemIndex}>
              <Link
                href="#"
                className="flex items-center space-x-2 p-2"
              >
                <span className="text-custom-green font-bold flex-shrink-0">•</span>
                <span className="text-[#555555] text-sm md:text-base font-semibold  leading-relaxed">
                  {item}
                </span>
              </Link>
            </li>
          ))}
        </ul>
      );
    }
  };

  return (
    <section className="py-8">
      <div className="container max-w-7xl mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {cards.map((card, index) => (
            <div
              key={index}
              className="bg-[#D1D9D1] p-6 rounded-xl border-2 border-custom-green shadow-md hover:shadow-lg transition-shadow"
            >
              <h3 className="text-lg md:text-2xl font-bold text-custom-green mb-4 font-ramilas">
                {card.title}
              </h3>
              {renderItems(card)}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default DBTSmallCards;
