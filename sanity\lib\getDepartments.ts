import { client } from "@/lib/sanity"; // Assuming urlFor is not needed here anymore

export interface Department {
  _id: string;
  name: string;
  slug: string;
  description: string;
  imageUrl?: string; // Make imageUrl optional as it might not exist
}

export async function getDepartments(): Promise<Department[]> {
  const query = `*[_type == "courses"] | order(_createdAt asc) {
    _id,
    "slug": slug.current,
    overview-> {
      departmentTitle,
      description,
      image // Fetch the image URL directly
    }
  }`;

  const results = await client.fetch(query);
  console.log("fetched departments results : ", results);

  return results.map((dept: any) => ({
    _id: dept._id,
    name: dept.overview?.departmentTitle || 'Unnamed Department',
    slug: dept.slug || '',
    description: dept.overview?.description || 'No description available.',
    // Use the image URL directly, provide fallback if it doesn't exist
    imageUrl: dept.overview?.image || undefined // Use undefined or a placeholder path like '/placeholder.png'
  }));
}