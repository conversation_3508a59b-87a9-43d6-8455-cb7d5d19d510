"use client";

import ProgrammesTable from "@/app/components/ProgrammesTable";

// Define the structure for a single syllabus entry
interface SyllabusEntry {
  _key?: string;
  year: string; // Ensure this is 'year'
  link: string; // Ensure this is 'link'
}

interface Programme {
  name: string;
  sections: string;
  syllabus: SyllabusEntry[];
}

interface ProgrammesProps {
  programmes: Programme[];
  outcomes?: { // Add this prop
    title?: string;
    pdfUrl: string;
  };
}

export default function Programmes({ programmes, outcomes }: ProgrammesProps) { // Destructure outcomes here
 console.log("programmes", programmes);
 console.log("outcomes", outcomes); // Add this to check the outcomes prop
  return (
    <section className="py-4 px-4 bg-white" id="programmes">
      <div className="container max-w-7xl mx-auto space-y-6 text-center">
        <h3 className="text-custom-green">Programmes</h3>
        <p className="text-custom-new-green">
        Explore our wide range of academic programs including Bachelor's, Master's, Diploma, and Certificate courses. Download the latest syllabus for each course to get detailed curriculum insights.
        </p>
        <ProgrammesTable
          programmes={programmes}
          showFooter={true}
          showSyllabus={true}
          programmeOutcomes={outcomes}
          // You'll likely want to pass 'outcomes' to ProgrammesTable as well
          // For example: programmeOutcomes={outcomes} 
        />
      </div>
    </section>
  );
}
