import React, { useState } from "react";
import PlacementFormModal from "./PlacementFormModal";

const PlacementPolicy = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAgreed, setIsAgreed] = useState(false);

  const policyItems = [
    {
      title: "Preamble",
      content: "The placement policy lays the guidelines for training the students and improving their employability skills thereby bridging the gap between corporate expectations and academic skills of the students."
    },
    {
      title: "Student Registration Requirements",
      content: "It is mandatory for the student to register themselves with the Placement Cell and Training Centre for availing the training and placement drives conducted at the college."
    },
    {
      title: "One Student One Job Opportunity",
      content: "To ensure maximum placement of least one campus placement offer, the Placement Cell and Training Centre lays emphasis on the One Student One Job opportunity principle. The Coordinator of the Placement Cell will ensure that the students who have already been placed through online will decide on the job opportunities for the students based on their aptitudes and capabilities."
    },
    {
      title: "Placement of Company Interview and Confirmation of Participation",
      content: "For campus interviews arranged in the college, the Placement Cell and Training Centre will be intimating registration forms to students. The students are expected to submit the duly filled registration forms to the Placement and Training Centre."
    },
    {
      title: "Mandatory Attendance for Training Sessions",
      content: "The Placement Cell and Training Centre will be conducting training sessions for the students to improve their employability skills. It is mandatory for such students to attend the training sessions."
    },
    {
      title: "Pre-Placement and Recruitment Process Guidance",
      content: "To help the students to get placed in a reputed company / organization, if the student is not satisfied with the company, he/she may approach the Placement Cell for guidance. It is mandatory for the students to follow the instructions given by the Placement Cell."
    },
    {
      title: "Contact Person",
      content: "For any doubts regarding Placement and Training the department may contact the Placement Head / Members in charge of the Department may be contacted."
    },
    {
      title: "No fee for attending the interviews",
      content: "No fee will be charged from the students will be arranged by the Placement Cell and Training Centre for the interviews for the students and the placement members."
    }
  ];

  return (
    <section className="bg-[#D1D9D1]">
      {/* Main wrapper with full width and padding */}
      <div className="relative px-8 md:px-16 py-8 md:py-4 bg-white">
        {/* Title */}
        <div className="text-center mb-8">
          <h2 className="text-3xl md:text-4xl font-ramilas text-custom-green mb-6">
            Placement Policy
          </h2>
        </div>

        {/* Policy Card */}
        <div className="bg-white px-8 lg:px-24 py-16 rounded-2xl border-2 border-custom-green">
          <div className="space-y-6">
            {/* Preamble as heading */}
            <div className="space-y-3">
              <h3 className="text-xl md:text-2xl font-bold text-custom-green">
                {policyItems[0].title}
              </h3>
              <div className="flex items-start space-x-3 px-4">
                <span className="text-custom-green font-bold text-lg mt-1">•</span>
                <p className="text-gray-700 leading-relaxed text-base md:text-lg">
                  {policyItems[0].content}
                </p>
              </div>
            </div>

            {/* Padding between preamble and other content */}
            <div className="pt-4">
              {/* Other items as numbered subcontent */}
              {policyItems.slice(1).map((item, index) => (
                <div key={index + 1} className="space-y-3 mb-6">
                  <h3 className="text-xl md:text-2xl font-bold text-custom-green">
                    {index + 1}. {item.title}
                  </h3>
                  <div className="px-4 flex items-start space-x-3">
                    <span className="text-custom-green font-bold text-lg mt-1">•</span>
                    <p className="text-gray-700 leading-relaxed text-base md:text-lg">
                      {item.content}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Checkbox and Button Section - Same Row */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="flex items-center justify-between flex-wrap gap-2 xl:gap-4">
              <div className="flex items-center space-x-2 lg:space-x-4 xl:space-x-3">
                <div className="relative inline-block">
                  <input
                    type="checkbox"
                    id="agreeTerms"
                    checked={isAgreed}
                    onChange={(e) => setIsAgreed(e.target.checked)}
                    className="h-6 w-6 lg:h-8 lg:w-8 xl:h-8 xl:w-8 appearance-none border-2 border-custom-green rounded-lg md:rounded bg-white checked:bg-white checked:border-custom-green focus:outline-none focus:ring-0"
                    style={{
                      backgroundColor: 'white !important',
                      borderColor: '#002E00 !important'
                    } as React.CSSProperties}
                  />
                  {isAgreed && (
                    <svg
                      className="absolute inset-0 h-6 w-6 lg:h-8 lg:w-8 pointer-events-none"
                      style={{
                        left: '0',
                        top: '0'
                      }}
                      fill="#002E00"
                      viewBox="0 0 24 24"
                    >
                      <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                    </svg>
                  )}
                </div>
                <label htmlFor="agreeTerms" className="text-sm lg:text-lg xl:text-xl font-bold text-custom-green leading-relaxed">
                  I Agree with the Terms and Conditions
                </label>
              </div>

              <button
                onClick={() => isAgreed && setIsModalOpen(true)}
                disabled={!isAgreed}
                className={`px-2 lg:px-8 xl:px-24 py-3 rounded-full font-medium text-sm md:text-base transition-all duration-300 ${
                  isAgreed
                    ? "border border-custom-green bg-custom-green text-white hover:bg-green-900 cursor-pointer"
                    : "border border-gray-400 text-gray-500 cursor-not-allowed"
                }`}
              >
                Student Career Enrollment Form
              </button>
              
              <PlacementFormModal 
                isOpen={isModalOpen} 
                onClose={() => setIsModalOpen(false)} 
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PlacementPolicy;
