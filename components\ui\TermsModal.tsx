"use client";

import { Dialog, DialogContent, DialogOverlay, DialogTitle } from "@/components/ui/dialog";
import Link from "next/link";
import { ThumbsUp } from 'lucide-react'; // Using ThumbsUp as an example icon

interface TermsModalProps {
    isOpen: boolean;
    onClose: () => void;
    // onAgree is no longer needed since we're using a direct link
}

const TermsModal: React.FC<TermsModalProps> = ({ isOpen, onClose }) => {
    const handleClose = () => {
        console.log("TermsModal close triggered!"); 
        onClose(); 
    };

    return (
        <Dialog open={isOpen} onOpenChange={handleClose}>
            <DialogOverlay className="fixed inset-0 bg-black/30 z-[1001]" />
            <DialogContent
                className="fixed left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%] z-[1002]
                           max-w-2xl w-[90vw] overflow-hidden rounded-xl border-none
                           shadow-lg bg-white p-0"
                closeButtonClassName="absolute top-4 right-4 text-gray-500 hover:text-gray-800 rounded-full p-1 cursor-pointer focus:outline-none focus:ring-0 bg-gray-100 hover:bg-gray-200"
            >
                <DialogTitle className="text-lg font-semibold text-center p-4 border-b text-gray-700">
                    Terms And Conditions
                </DialogTitle>

                <div className="p-6 space-y-4">
                    <p className="text-sm text-teal-600">
                        Before using this service please read these terms and conditions carefully. You may then proceed to the gateway by clicking on the link 'I agree to the Terms and Conditions' below.
                    </p>
                    <Link 
                        href="#" // Replace with actual Terms and Conditions link
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-green-700 font-semibold hover:underline"
                    >
                        <ThumbsUp size={18} /> 
                        TERMS AND CONDITIONS
                    </Link>
                </div>

                {/* Single Button Footer - Changed to Link */}
                <div className="p-4 flex justify-center bg-gray-50 border-t">
                    <Link 
                        href="https://jmcerp.in/onlinepayment/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-full sm:w-auto"
                        onClick={() => onClose()} // Close the modal when clicked
                    >
                        <button 
                            className="bg-custom-green text-white hover:bg-green-700 font-medium py-2 px-10 rounded-md w-full transition-colors"
                        >
                            ONLINE PAYMENT
                        </button>
                    </Link>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default TermsModal;