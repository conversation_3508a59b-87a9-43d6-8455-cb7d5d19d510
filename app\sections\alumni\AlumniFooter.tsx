// app/sections/alumni-showcase/AlumniFooter.tsx
import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Facebook, Instagram, Linkedin, Youtube } from 'lucide-react';

const AlumniFooter = () => {
  return (
    <footer className="bg-custom-green text-white pt-16 pb-8">
      <div className="container mx-auto px-4">
        {/* Top section: Two main divs (left: logo/content, right: links) */}
        <div className="grid grid-cols-1 md:grid-cols-12 gap-12 xl:gap-24 mb-12"> {/* Increased gap from 8 to 12 */}
          {/* Left Side: Logo and Content */}
          {/* Handles responsive layout: column on small screens, row on medium+ screens.
              - On small screens (flex-col): items are centered.
              - On medium+ screens (md:flex-row): items are stretched to the same height, text aligned left.
              - Gap provides spacing between logo and content blocks.
           */}
          <div className="md:col-span-4 flex flex-col items-center text-center md:flex-row md:items-stretch md:text-left gap-4 sm:gap-6">
            {/* Image Wrapper:
                - `relative` for Next/Image with layout="fill".
                - `w-32 h-32` (128px x 128px) for small screens, making it larger than original 100px.
                - `md:w-40` (160px width) for medium+ screens.
                - `md:h-full` makes it take the full stretched height provided by `md:items-stretch` on the parent.
                - `flex-shrink-0` prevents the image wrapper from shrinking if space is tight.
            */}
            <div className="relative w-32 h-32 md:w-44 md:h-full flex-shrink-0">
              <Image
                src="/JMC logo.svg"
                alt="JMC College Logo"
                layout="fill"
                objectFit="contain" // Ensures the image scales within bounds maintaining aspect ratio
              />
            </div>

            {/* Content Wrapper:
                - `flex flex-col` to stack title, paragraph, and social icons.
                - `md:flex-1` allows this block to grow and fill available horizontal space in md:flex-row context.
            */}
            <div className="flex flex-col md:flex-1">
              <div> {/* Groups main text content and social icons */}
                <h3 className="text-xl font-semibold mb-2">JMC College</h3>
                <p className="text-sm mb-4 max-w-xs"> {/* Added mb-4 for spacing */}
                  We are passionate education dedicated to providing high-quality resources learners all backgrounds.
                </p>
                {/* Social Icons: Now positioned directly below the paragraph */}
                <div className="flex space-x-4">
                  <a href="#" aria-label="Facebook" className="text-gray-300 hover:text-white"><Facebook size={20} /></a>
                  <a href="#" aria-label="Instagram" className="text-gray-300 hover:text-white"><Instagram size={20} /></a>
                  <a href="#" aria-label="LinkedIn" className="text-gray-300 hover:text-white"><Linkedin size={20} /></a>
                  <a href="#" aria-label="YouTube" className="text-gray-300 hover:text-white"><Youtube size={20} /></a>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side: Links Columns */}
          {/* Changed lg:grid-cols-3 to lg:grid-cols-2 to better utilize space with two columns */}
          <div className="md:col-span-8 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-12 xl:gap-16"> {/* Increased gap from 12 to 16, changed lg:grid-cols-3 to lg:grid-cols-2 */}
            <div>
              <h5 className="font-semibold mb-4 text-lg">Get In Touch</h5>
              <ul className="space-y-2 text-sm">
                <li><p>Phone: (0431) 2331135, 9894731035</p></li>
                <li><p>Email: <a href="mailto:<EMAIL>" className="hover:underline"><EMAIL></a></p></li>
                <li><p>Website: <a href="https://www.jmc.edu" target="_blank" rel="noopener noreferrer" className="hover:underline">www.jmc.edu</a></p></li>
              </ul>
            </div>
            <div>
              <h5 className="font-semibold mb-4 text-lg">Contact</h5>
              <ul className="space-y-2 text-sm leading-relaxed">
                <li><p>To The General Secretary,</p></li>
                <li><p>Jamal Mohamed College Alumni Association,</p></li>
                <li><p>P.Box.No. 808, #7, Race Course Road,</p></li>
                <li><p>Khajanagar, Tiruchirappalli-620 020.</p></li>
              </ul>
            </div>
            {/* <div>
              <h5 className="font-semibold mb-4 text-lg">Quick Links</h5>
              <ul className="space-y-2 text-sm">
                <li><Link href="/alumni-showcase/about" legacyBehavior><a className="hover:underline">About Us</a></Link></li>
                <li><Link href="/alumni-showcase/chapters" legacyBehavior><a className="hover:underline">Chapters</a></Link></li>
                <li><Link href="/alumni-showcase/gallery" legacyBehavior><a className="hover:underline">Gallery</a></Link></li>
                <li><Link href="/alumni-showcase/prominent-alumni" legacyBehavior><a className="hover:underline">Prominent Alumni</a></Link></li>
                <li><Link href="/alumni-showcase/awareness" legacyBehavior><a className="hover:underline">Awareness</a></Link></li>
                <li><Link href="/alumni-showcase/service" legacyBehavior><a className="hover:underline">Service</a></Link></li>
              </ul>
            </div> */}
          </div>
        </div>
      </div>
      {/* Separator Line */}
      <hr className="border-gray-700 my-8" />

      {/* Bottom: Copyright */}
      <div className="text-center text-sm text-gray-400">
        <p>Copyright © {new Date().getFullYear()}. All Rights Reserved by Jamal Mohamed College</p>
      </div>

    </footer>
  );
};

export default AlumniFooter;