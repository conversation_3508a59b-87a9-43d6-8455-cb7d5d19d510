import Link from 'next/link';

export default function CTAComponent() {
  return (
    <div
      className="w-full py-12 px-6 sm:px-10 md:px-20 text-center rounded-2xl"
      style={{
        backgroundImage: `
          linear-gradient(90deg, #002E00 0%, #004D00 100%),
          url('/consultation_bg_image.png')
        `,
        backgroundBlendMode: "overlay",
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
        backgroundPosition: "left center, -12.5rem center",
        boxShadow: "0px 0px 24px 0px #00000026",
      }}
    >
      <h2 className="text-white text-2xl sm:text-3xl md:text-4xl font-semibold mb-4">
        Make Your Career With
        JMC Content Now
      </h2>
      <p className="text-white text-base sm:text-lg mb-6">
        more resilient world by exploring
      </p>
      <Link href="/contactUs">
        <button className="bg-white text-custom-green font-poppins font-medium px-6 py-2 rounded-full text-base hover:bg-gray-100 transition">
          Contact Us
        </button>
      </Link>
    </div>
  );
}
