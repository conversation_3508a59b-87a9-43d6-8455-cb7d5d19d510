import React from "react";

const OrdinaryMembers = () => {
  return (
    <section className="px-4 py-8" id="ordinary-members">
      <div className="container lg:max-w-6xl xl:max-w-7xl mx-auto text-center space-y-10">
        <h2 className="text-custom-green">Ordinary Members</h2>
        <p className="text-gray-600 mb-6">
          Our state-of-the-art infrastructure provides a modern, dynamic
          environment for academic and personal growth. Discover well-equipped
          facilities designed to support excellence in learning and research.
        </p>
        <div className="overflow-x-auto flex justify-center pb-8">
          <table className=" w-full max-w-sm sm:max-w-xl md:max-w-2xl lg:max-w-3xl mx-auto border-collapse rounded-2xl overflow-hidden font-ramilas font-medium relative border border-custom-green bg-custom-light-green md:shadow-milestone">
            <thead>
              <tr className="bg-custom-green text-white rounded-t-2xl">
                <th className="px-6 md:px-8 py-6 text-left border-l-0 border-r-0">
                  S.no
                </th>
                <th className="px-6 md:px-8 py-6 text-right border-l-0 border-r-0">
                  Member Names
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                className="border-b"
                style={{ borderBottom: "1px solid #00000033" }}
              >
                <td className="px-6 md:px-8 py-6 text-left border-l-0 border-r-0">
                  1
                </td>
                <td className="px-6 md:px-8 py-6 text-right border-l-0 border-r-0">
                  Hajee K. N. S. S. Rahmathullah
                </td>
              </tr>
              <tr
                className="border-b"
                style={{ borderBottom: "1px solid #00000033" }}
              >
                <td className="px-6 md:px-8 py-6 text-left border-l-0 border-r-0">
                  2
                </td>
                <td className="px-6 md:px-8 py-6 text-right border-l-0 border-r-0">
                  Hajee A. K. Rasheed Ahamed
                </td>
              </tr>
              <tr
                className="border-b"
                style={{ borderBottom: "1px solid #00000033" }}
              >
                <td className="px-6 md:px-8 py-6 text-left border-l-0 border-r-0">
                  3
                </td>
                <td className="px-6 md:px-8 py-6 text-right border-l-0 border-r-0">
                  Dr. K.N. Abdul Kader Nihal
                </td>
              </tr>
              <tr
                className="border-b"
                style={{ borderBottom: "1px solid #00000033" }}
              >
                <td className="px-6 md:px-8 py-6 text-left border-l-0 border-r-0">
                  4
                </td>
                <td className="px-6 md:px-8 py-6 text-right">
                  Mr. M.J.M. Mujeebur Rahman
                </td>
              </tr>
              <tr>
                <td className="px-6 md:px-8 py-6 text-left">5</td>
                <td className="px-6 md:px-8 py-6 text-right">
                  Mr. M. Akbar Ali
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
  );
};

export default OrdinaryMembers;
