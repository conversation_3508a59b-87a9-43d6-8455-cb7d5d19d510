"use client";

import Navbar from "@/components/Navbar";
import Footer from "../sections/Footer";
import Link from "next/link";
import Sidebar from "../components/Sidebar";
import { menuItems } from "@/components/Navbar";
import { usePathname } from "next/navigation";
import CTAComponent from "../components/CTAComponent";
import Image from "next/image";
import YearButtonsSection from "../components/YearButtonsSection";


const Page = () => {
  const pathname = usePathname();

  const currentSection = menuItems.find(
    (item) => pathname.startsWith("/iqac") && item?.subSections
  );

  // ARIIA Annual Report Data
  const ariiaAnnualReportData = {
    title: "ARIIA Annual Report",
    description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
    yearButtons: [
      { year: "2021-22", link: "#" },
      { year: "2020-21", link: "#" },
      { year: "2019-20", link: "#" }
    ]
  };

  // ARIIA Certificate Images
  const ariiaImages = [
    {
      id: 1,
      title: "ARIIA Ranking Certificate 2020",
      image: "/ARIIA/ariia1.png"
    },
    {
      id: 2,
      title: "ARIIA Ranking Certificate 2021",
      image: "/ARIIA/ariia1.png"
    },
    {
      id: 3,
      title: "ARIIA Ranking Certificate 2022",
      image: "/ARIIA/ariia1.png"
    }
  ];

  return (
    <>
      <Navbar fixed={true} border={true} />
      <header
        className="relative pt-40 pb-16 px-4 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex flex-col md:flex-row md:justify-between md:items-center">
          <h1 className="font-ramilas text-center md:text-left mb-4 md:mb-0">
            ARIIA
          </h1>
          <ul className="font-poppins flex flex-row justify-center md:justify-end gap-8 md:gap-8 md:pr-16">
            <li className="list-disc">
              <Link href="/">Home</Link>
            </li>
            <li className="list-disc">ARIIA</li>
          </ul>
        </div>
      </header>

      <main>
        {currentSection && (
          <Sidebar subsections={currentSection.subSections || []} />
        )}

        {/* ARIIA Introduction Section - Similar to DBT page */}
        <section className="">
          <div className="bg-[#D1D9D1] relative px-4 md:px-24 py-8 md:py-12 mb-20 rounded-bl-xl border-b border-custom-green" style={{
            boxShadow: '22px 22px 0px 0px #5F765F, 52px 50px 0px 0px #002E00',
            borderBottomLeftRadius: '1.5rem'
          }}>
            <div className="text-center mb-8">
              <h2 className="text-xl md:text-4xl font-bold text-custom-green mb-8">
                NIRF - Innovation, formerly known as Atal Ranking of Institutions on Innovation Achievements (ARIIA)
              </h2>
              <p className="text-gray-700 text-lg leading-relaxed">
                To ensure that innovation is primary fulcrum of all of its, in the year 2018, Ministry of Education (MoE), Govt. of India has introduced 'Atal Ranking of Institutions on Innovation Achievements (ARIIA)' to systematically rank education institutions and universities in the country primarily on innovation related indicators. In 2023, Atal Ranking of Institutions on Innovation Achievements (ARIIA) is renamed as NIRF - Innovation & consists of major higher education institutions/universities in the world.
              </p>
            </div>
          </div>
        </section>

        {/* Year Buttons Section */}
        <YearButtonsSection
          title={ariiaAnnualReportData.title}
          description={ariiaAnnualReportData.description}
          yearButtons={ariiaAnnualReportData.yearButtons}
          backgroundColor="bg-white"
        />

        {/* ARIIA Certificates Section */}
        <div className="mt-16 pt-8 px-4 lg:px-24">
          <h3 className="text-3xl font-bold text-[#002E00] text-center mb-10">
            ARIIA Annual Report
          </h3>

          <div className="flex flex-wrap justify-center gap-8">
            {ariiaImages.map((certificate) => (
              <div key={certificate.id} className="flex flex-col items-center w-full md:w-[30%]">
                <div className="mb-4 w-full h-80 md:h-96 relative">
                  <Image
                    src={certificate.image}
                    alt={certificate.title}
                    fill
                    style={{ objectFit: "contain" }}
                    className="hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <p className="text-center text-[#002E00] font-medium">{certificate.title}</p>
              </div>
            ))}
          </div>
        </div>

        <section className="bg-white py-10 px-4">
          <div className="container max-w-7xl mx-auto">
            <CTAComponent />
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
};

export default Page;
