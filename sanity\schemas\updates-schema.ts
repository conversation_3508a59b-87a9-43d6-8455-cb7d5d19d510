import { defineType } from "sanity";

export const updates = defineType({
  name: "noticeAndUpdates",
  title: "notice-and-updates",
  type: "document",
  fields: [
    {
      name: "category",
      title: "Category",
      type: "string",
    },
    {
      name: "image",
      title: "Image",
      type: "image",
      options: {
        hotspot: true,
      },
      fields: [
        {
          name: "alt",
          title: "Alt Text",
          type: "string",
          description: "Alternative text for screen readers and SEO",
          validation: (Rule) =>
            Rule.required().warning(
              "Alt text is recommended for accessibility."
            ),
        },
        {
          name: "url",
          title: "URL",
          type: "url",
        },
      ],
    },
    {
      name: "description",
      title: "Description",
      type: "string",
    },
  ],
});
