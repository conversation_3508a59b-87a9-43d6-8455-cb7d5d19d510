import React from "react";
import Image from "next/image";
import Link from "next/link";

const GlobalAlumniSearchEngine = () => {
  const generalInstructions = [
    {
      title: "Students attending campus interviews should adhere to the following instructions:",
      points: [
        "Report at the venue of Pre-Placement Talk and Interview as per the instructions.",
        "Students should carry minimum 2 copies of their Resume, Photocopies of all Original certificates and 3 Passport size photographs.",
        "A student should come with decent attire for the Pre Placement Talk / Recruitment Process."
      ]
    }
  ];

  const additionalInstructions = [
    "It is the prime responsibility of the students to check all the updates related to Campus Recruitment through its Eligibility / Dates / Venue / shortlisted names etc. available through their e-mail or JMC Portal and page or directly from the placement department.",
    "For all the Placement drives, students should bring college ID card, One Photo in Proof, two Copies of resume affixed with photograph and photocopy of 10th, 12th UG/PG Original & photocopy of marks sheets.",
    "Students participating in placement activities must keep their Identity Card with them at the time of the Placement Talk / Test /Group Discussion / Interview etc.",
    "During recruitment, most of the companies insist on PAN card & Passport. So, the students are advised to apply for the same at the earliest."
  ];

  const campusRequirements = [
    "Campus Recruitment is one of the desired services of the student's community. Recruiting agencies of various kind have been visiting our college, not only internationally for the purpose of recruiting qualified and suitable candidates from Information technology, Medical Transcription, Cellular services, Electrical appliances, Chemical industry and others. Every year 5 to 10 percent of the total eligible students are recruited, a routine program of our college."
  ];

  const placementActivities = [
    "Campus Recruitment Drives",
    "Guest Lectures and Enlightenment Sessions by Industry Experts on Placement Opportunities",
    "Employability skills Training programme",
    "Communication Skills Development Programme",
    "Personality Development Programme",
    "Campus Training Programme",
    "Mock Interview Sessions",
    "Development of Leadership Qualities",
    "Spoken English Class through Language Laboratory",
    "Competitive Examinations Coaching - NET / SET"
  ];

  return (
    <section className="bg-[#D1D9D1]">
      {/* Main wrapper with full width and padding */}
      <div className="relative px-8 md:px-16 py-8 md:py-4 bg-white mt-4">
        {/* Title */}
        <div className="text-center mb-8">
          <h2 className="text-3xl md:text-4xl font-ramilas text-custom-green mb-6">
            Global Alumni Search Engine
          </h2>
        </div>

        {/* Big Poster Image */}
        <div className="mb-8 text-center">
          <div className="max-w-4xl mx-auto">
            <Image
              src="/placement/gase/jobportalposter.png"
              alt="Global Alumni Search Engine Poster"
              width={800}
              height={600}
              priority
              quality={100}
              sizes="(max-width: 768px) 100vw, 800px"
              className="rounded-lg shadow-md w-full h-auto"
            />
          </div>
        </div>

        {/* Paragraph */}
        <div className="mb-8">
          <p className="text-gray-700 leading-relaxed text-base md:text-lg text-center max-w-7xl mx-auto">
            JMC Global Alumni Job Portal an Initiative was taken by our illustrious Alumni and established in January 2021 which helps
            our Jamal Mohamed College Students to connect Global Employers, to find and apply for their suitable Placement Positions /
            Jobs. This Portal facilitates JMC job seekers to register their profile through online. Also, the portal enables employers to
            register and post their job vacancies on this portal. This brings a mutual benefit for the employers to hire employees with
            talented Human Resources and also for our Jamalians to board onto a right place according to their skill set.
          </p>
        </div>

        {/* Registration Link Card */}
        <div className="mb-12">
          <div className="bg-white p-6 md:p-8 rounded-2xl border-2 border-custom-green max-w-7xl mx-auto">
            <h3 className="text-xl md:text-3xl font-bold text-custom-green text-center mb-6 lg:mb-8">
              Registration Link
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Link 
                href="https://jobs.jmcalumni.net/users/register/jobseeker" 
                target="_blank"
                rel="noopener noreferrer"
                className="bg-custom-green text-white px-6 py-3 rounded-lg font-bold md:font-medium text-xl md:text-2xl hover:bg-green-700 transition-all duration-300 text-center"
              >
                For Job Seekers
              </Link>
              <Link 
                href="https://jobs.jmcalumni.net/users/register/employer" 
                target="_blank"
                rel="noopener noreferrer"
                className="bg-custom-green text-white px-6 py-3 rounded-lg font-bold md:font-medium text-xl md:text-2xl hover:bg-green-700 transition-all duration-300 text-center"
              >
                For Employers
              </Link>
            </div>
          </div>
        </div>

        {/* Big Card - Similar to PlacementPolicy */}
        <div className="bg-white p-6 md:p-12 rounded-2xl border-2 border-custom-green">
          <div className="space-y-8 lg:px-8">
            {/* General Instructions */}
            <div className="space-y-4">
              <h3 className="text-xl md:text-2xl font-bold text-custom-green">
                General Instruction
              </h3>
              {generalInstructions.map((section, sectionIndex) => (
                <div key={sectionIndex} className="space-y-3">
                  <div className="flex items-start space-x-3 ml-2 md:ml-6">
                    <span className="text-custom-green font-bold text-lg mt-1">•</span>
                    <p className="text-gray-700 leading-relaxed text-base md:text-lg font-medium">
                      {section.title}
                    </p>
                  </div>
                  <div className=" ml-2 md:ml-6 space-y-2">
                    {section.points.map((point, pointIndex) => (
                      <div key={pointIndex} className="flex items-start space-x-3 ml-6">
                        <span className="text-custom-green font-bold text-lg mt-1">
                          {String.fromCharCode(97 + pointIndex)}.
                        </span>
                        <p className="text-gray-700 leading-relaxed text-base md:text-lg">
                          {point}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              ))}

              {/* Additional Main Instructions */}
              {additionalInstructions.map((instruction, index) => (
                <div key={index} className="flex items-start space-x-3 ml-2 md:ml-6">
                  <span className="text-custom-green font-bold text-lg mt-1">•</span>
                  <p className="text-gray-700 leading-relaxed text-base md:text-lg">
                    {instruction}
                  </p>
                </div>
              ))}
            </div>

            {/* Campus Requirements */}
            <div className="space-y-4">
              <h3 className="text-xl md:text-2xl font-bold text-custom-green">
                Campus Requirements
              </h3>
              {campusRequirements.map((requirement, index) => (
                <div key={index} className="flex items-start space-x-3 ml-2 md:ml-6">
                  <span className="text-custom-green font-bold text-lg mt-1">•</span>
                  <p className="text-gray-700 leading-relaxed text-base md:text-lg">
                    {requirement}
                  </p>
                </div>
              ))}
            </div>

            {/* Activities Undertaken by Placement Cell */}
            <div className="space-y-4">
              <h3 className="text-xl md:text-2xl font-bold text-custom-green">
                Activities Undertaken by Placement Cell
              </h3>
              <div className=" ml-2 md:ml-6 space-y-2">
                {placementActivities.map((activity, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <span className="text-custom-green font-bold text-lg mt-1">•</span>
                    <p className="text-gray-700 leading-relaxed text-base md:text-lg">
                      {activity}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default GlobalAlumniSearchEngine;
