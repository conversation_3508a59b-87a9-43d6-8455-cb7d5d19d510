"use client";
import AdmissionCategoriesImageCards from "@/app/components/AdmissionCategoriesImageCards";
import Link from 'next/link'; // Import Link here

const AdmissionSection = () => {
  return (
    <section className="py-12 bg-[#E8EDE8]">
      <div className="container mx-auto max-w-7xl px-8">
        {/* Row 1: Title and Button */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <h2 className="text-4xl md:text-5xl font-bold text-custom-green mb-4 md:mb-0">
            Admission
          </h2>
          <Link href="/admission" className="hidden md:block">
            <button className="bg-transparent text-custom-green px-12 py-2 rounded-full border border-custom-green">
              Know More
            </button>
          </Link>
        </div>

        {/* Row 2: Paragraph */}
        <p className="text-gray-700 max-w-7xl mb-8">
          Admissions are now open for the 2025–2026 academic year! At Jamal
          Mohamed College, we offer a wide range of Aided and Unaided programs
          for men, and a variety of Unaided programs for women. Step into a
          vibrant learning environment where academic excellence and personal
          growth go hand in hand. Begin your journey with us today!
        </p>
        <AdmissionCategoriesImageCards />
      </div>
    </section>
  );
};

export default AdmissionSection;
