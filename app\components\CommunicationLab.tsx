import React from "react";
import Image from "next/image";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

const CommunicationLab = () => {
  const courseTopics = [
    {
      title: "Why should this course",
      points: [
        "Over two-third of the worlds scientists read in English",
        "Three quarters of the worlds mail in written in English",
        "80% of the worlds electronically stored information is in English"
      ]
    },
    {
      title: "Develop Your Career :",
      points: [
        "Most professional jobs require a good level in English- think how impressive excellent level in English will look on your CV",
        "Business and commerce are increasingly driven by international trade and if you are going to grow in your job you need to be able to follow this trend",
        "Technical skills are essential in modern industries and you need to be able to both read and write technical English if you are going to maintain your technical competence"
      ]
    },
    {
      title: "About The class :",
      points: [
        "It's Not A Theory Class",
        "You Are Going To Be Learn Through Computer",
        "You Are Going To Learn Practically",
        "You Will Lose Fear/Shyness/Hesitation",
        "You Will Get An Environment To Speak English",
        "More than 300 students completed this course",
        "Most of the students got placed in companies"
      ]
    },
    {
      title: "About Course :",
      hasSubSections: true,
      subSections: [
        {
          title: "Level -A- Basic",
          subPoints: ["Learning with fun"]
        },
        {
          title: "Level -B- Intermediate",
          subPoints: [
            "Phonetics",
            "Communicative English",
            "Situational conversation",
            "Global communicative English",
            "Error in spoken English",
            "Grammar"
          ]
        },
        {
          title: "Level -C- Advanced English",
          subPoints: [
            "BBC Current Events",
            "Dialogue (U.S Accents)",
            "Idioms",
            "Phrases"
          ]
        },
        {
          title: "Projects",
          subPoints: [
            "Handling Telephone Calls",
            "Career Planning",
            "Making C.V",
            "Email Skill",
            "Group Discussion",
            "Debating",
            "Public Speaking",
            "Presentation Skills",
            "Reporting",
            "Soft Skills"
          ]
        },
        {
          title: "IELTS (INTERNATIONAL ENGLISH LANGUAGE TESTING SYSTEM)",
          subPoints: []
        },
        {
          title: "TOEFL (TEST OF ENGLISH AS A FOREIGN LANGUAGE)",
          subPoints: [
            "Pronunciation",
            "Vocabulary",
            "Listening practice",
            "Listening and speaking",
            "Exercise"
          ]
        }
      ],
      hasTables: true
    }
  ];

  const timingData = {
    boys: [
      { sNo: "1.", name: "First batch", timings: "9.30 am to 10.30 am" },
      { sNo: "2.", name: "Second batch", timings: "10.45 am to 11.45 am" },
      { sNo: "3.", name: "Third batch", timings: "12.00 pm to 1.00 pm" }
    ],
    womens: [
      { sNo: "1.", name: "First batch", timings: "2.30 pm to 3.30 pm" },
      { sNo: "2.", name: "Second batch", timings: "3.30 pm to 4.30 pm" },
      { sNo: "3.", name: "Third batch", timings: "4.30 pm to 5.30 pm" }
    ]
  };

  return (
    <section className="bg-[#D1D9D1]">
      {/* Main wrapper with full width and padding */}
      <div className="relative px-8 md:px-16 py-8 md:py-4 bg-white md:mb-20">

        {/* Heading */}
        <div className="mb-8">
          <h2 className="text-3xl md:text-4xl font-ramilas text-custom-green text-center">
            Communication Lab
          </h2>
        </div>

        {/* Two Images in Single Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8 ">
          <div className="flex justify-center">
            <Image
              src="/placement/communicationlab/SB1.png"
              alt="Communication Lab Image 1"
              width={600}
              height={450}
              className="rounded-xl border border-custom-green shadow-md object-cover"
            />
          </div>
          <div className="flex justify-center">
            <Image
              src="/placement/communicationlab/SB1.png"
              alt="Communication Lab Image 2"
              width={600}
              height={450}
              className="rounded-xl border border-custom-green  shadow-md object-cover"
            />
          </div>
        </div>

        {/* Paragraph */}
        <div className="mb-8 px-8">
          <p className="text-gray-700 text-base md:text-lg leading-relaxed text-center">
          Communication Training Centre offers the communication classes that offer the flexibility to study. Our highly innovative and unique mix of engaging Communication course is accomplished by assessments and interactive sessions that allow the students to progress consistently. Communication training center provides a Good approach that follows best practices for learning and practicing English Communication. Communication English learning features in a very natural way similar to native speakers learn their first language. We analyze the issues faced by the Students, their capacity to cope up and devise a plan to improvise their skills in most effortless way of learning. Unlike other subjects, Communication learning attributes to frequent continuous practice and mind mapping techniques. We adopt a distinct method beyond the classroom, we implement an integrated training strategy, our sessions are structured to be interactive and participative in a fun and dynamic environment. We engage students in practical sessions, and we motivate student to take in part discussions and presentations skills.
We just don’t train, we inspire. We are aware that English learning is not like other subjects. We allow students to practice additionally, interactive and regular feedback sessions from trainer, for the advantage to acquire skills faster.
We know that Language learning is as important as learning any other subject, in this competitive world having extraordinary technical skills underplay when once couldn’t express and present himself properly. Our training objective is not just to make our students speak English as learners but as professionals. Often our students are dumbfounded unable to talk, speaking at the appropriate time with appropriate vocabulary is the key to success, we eliminate fear of speaking, and we break the ice and make them feel free to open their mouth.
          </p>
        </div>

        {/* Card with Four Topics */}
        <div className="bg-white py-12 md:px-12 lg:py-24 md:px-12 rounded-2xl border-2 border-custom-green">
          <div className="space-y-8 px-4 md:px-8">
            {courseTopics.map((topic, topicIndex) => (
              <div key={topicIndex} className="space-y-4">
                <h3 className="text-xl md:text-2xl font-bold text-custom-green">
                  {topic.title}
                </h3>

                {!topic.hasSubSections ? (
                  // Regular points for first three topics
                  <div className="space-y-2 ml-6">
                    {topic.points?.map((point, pointIndex) => (
                      <div key={pointIndex} className="flex items-start space-x-3">
                        <span className="text-custom-green font-bold text-lg mt-1">•</span>
                        <p className="text-gray-700 leading-relaxed text-base md:text-lg">
                          {point}
                        </p>
                      </div>
                    ))}
                  </div>
                ) : (
                  // Sub-sections for fourth topic
                  <div className="space-y-6">
                    {topic.subSections?.map((section, sectionIndex) => (
                      <div key={sectionIndex} className="space-y-3">
                        <div className="flex items-start space-x-3 ml-6">
                          <span className="text-custom-green font-bold text-lg mt-1">•</span>
                          <p className="text-gray-700 leading-relaxed text-base md:text-lg font-medium">
                            {section.title}
                          </p>
                        </div>
                        {section.subPoints.length > 0 && (
                          <div className="ml-12 space-y-2">
                            {section.subPoints.map((subPoint, subIndex) => (
                              <div key={subIndex} className="flex items-start space-x-3">
                                <span className="text-custom-green font-bold text-lg mt-1">•</span>
                                <p className="text-gray-700 leading-relaxed text-base md:text-lg">
                                  {subPoint}
                                </p>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}

                    {/* Tables for timing */}
                    {topic.hasTables && (
                      <div className="space-y-6 mt-8">
                        {/* Timing for Boys */}
                        <div>
                          <h4 className="text-lg font-bold text-gray-800 ml-6">Timing for Boys</h4>
                          <div className="md:px-12 md:py-8">
                            <div className="rounded-lg border-2 border-custom-green overflow-hidden md:shadow-milestone">
                              <Table>
                                <TableHeader>
                                  <TableRow className="bg-custom-green hover:bg-custom-green border-0">
                                    <TableHead className="md:px-6 md:py-3 text-left text-sm font-medium uppercase tracking-wider text-white">S.No</TableHead>
                                    <TableHead className="md:px-6 md:py-3 text-left text-sm font-medium uppercase tracking-wider text-white">Name</TableHead>
                                    <TableHead className="md:px-6 md:py-3 text-left text-sm font-medium uppercase tracking-wider text-white">Timings</TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {timingData.boys.map((row, index) => (
                                    <TableRow key={index} className={`bg-gray-100 hover:bg-gray-100 ${index < timingData.boys.length - 1 ? 'border-b border-gray-300' : 'border-0'}`}>
                                      <TableCell className="md:px-6 md:py-4 text-center text-sm text-gray-900">{row.sNo}</TableCell>
                                      <TableCell className="md:px-6 md:py-4 text-sm text-gray-900">{row.name}</TableCell>
                                      <TableCell className="md:px-6 md:py-4 text-sm text-gray-900">{row.timings}</TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </div>
                          </div>
                        </div>

                        {/* Timing for Womens */}
                        <div>
                          <h4 className="text-lg font-bold text-gray-800 ml-6">Timing for Womens</h4>
                          <div className="md:px-12 md:py-8">
                            <div className="rounded-lg border-2 border-custom-green overflow-hidden md:shadow-milestone">
                              <Table>
                                <TableHeader>
                                  <TableRow className="bg-custom-green hover:bg-custom-green border-0">
                                    <TableHead className="md:px-6 md:py-3 text-left text-sm font-medium uppercase tracking-wider text-white">S.No</TableHead>
                                    <TableHead className="md:px-6 md:py-3 text-left text-sm font-medium uppercase tracking-wider text-white">Name</TableHead>
                                    <TableHead className="md:px-6 md:py-3 text-left text-sm font-medium uppercase tracking-wider text-white">Timings</TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {timingData.womens.map((row, index) => (
                                    <TableRow key={index} className={`bg-gray-100 hover:bg-gray-100 ${index < timingData.womens.length - 1 ? 'border-b border-gray-300' : 'border-0'}`}>
                                      <TableCell className="md:px-6 md:py-4 text-center text-sm text-gray-900">{row.sNo}</TableCell>
                                      <TableCell className="md:px-6 md:py-4 text-sm text-gray-900">{row.name}</TableCell>
                                      <TableCell className="md:px-6 md:py-4 text-sm text-gray-900">{row.timings}</TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

      </div>
    </section>
  );
};

export default CommunicationLab;
