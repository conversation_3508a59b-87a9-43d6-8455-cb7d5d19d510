import Navbar from "@/components/Navbar";
import React from "react";
import { client } from "@/sanity/lib/client";
import CoursePageClient from "./CoursePageClient"; // Import the new client component

export async function generateStaticParams() {
  const courses = await client.fetch<{ slug: { current: string } }[]>(
    `*[_type == "courses" && defined(slug.current)]{
      "slug": slug.current
    }`
  );

  if (!courses) {
    return [];
  }

  return courses.map((course) => ({
    slug: course.slug,
  }));
}

const page = async ({ params }: { params: { slug: string } }) => {
  const courseDetails = await client.fetch(
    `*[_type == "courses" && slug.current == $slug][0]{
      departmentName,
      recruitersImage, // Add new field to fetch recruiter images
      programmes[] {
        _key, // Optional: useful for React list keys
        name,
        sections,
        syllabus[] { // Correctly fetch as an array of objects
          _key,   // Optional: useful for React list keys
          year,   // Matches your schema field name
          link    // Matches your schema field name
        }
      },
      programmesAndOutcomes { // Fetch the new object
        title,
        pdfUrl
      },
      faculty[]->{
        _id,
        name,
        qualifications,
        designation,
        email,
        "profileUrl": profileUrl,
        image,
        category
      },
      overview->{
        image,
        departmentTitle,
        description,
        mission,
        vision
      },
      valueAddedCourses[]->{
        _id,
        title,
        fileUrl
      },
      eContent[]->{
        _id,
        title,
        contentUrl,
        author
      },
      departmentActivities[]->{ // Add this section
        _id,
        title,
        date,
        description,
        image // Fetch the image URL directly
      }
    }`,
    { slug: params.slug }
  );
  console.log("courses details are", courseDetails);

  // Define subsections for the Sidebar
  const subsections = [
    { title: "Overview", href: "#overview" },
    { title: "Programmes", href: "#programmes" },
    { title: "Value Added Courses", href: "#value-added-courses" },
    { title: "E-Content", href: "#e-content" },
    { title: "Faculty", href: "#faculty" },
    { title: "Department Activities", href: "#department-activities" },
    { title: "Infrastructure", href: "#infrastructure" },
    { title: "Our Recruiters", href: "#our-recruiters" },
  ];

  return (
    <>
      <Navbar fixed={true} border={true} />
      <header
        className="relative px-4 pt-36 pb-16 text-white bg-cover bg-center"
        style={{ backgroundImage: "url('/bg_image.webp')" }}
      >
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>
        <div className="relative container max-w-7xl mx-auto z-10 flex justify-between items-center">
          <h1 className="font-ramilas">
            {courseDetails?.departmentName || "Course"}
          </h1>
        </div>
      </header>

      {/* Render the client component, passing data and subsections */}
      <CoursePageClient courseDetails={courseDetails} subsections={subsections} />
    </>
  );
};

export default page;
