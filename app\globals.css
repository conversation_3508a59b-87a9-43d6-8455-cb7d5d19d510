@import url("https://fonts.googleapis.com/css2?family=Ubuntu:wght@400;500;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

.inter {
  font-family: var(--font-inter), sans-serif;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground text-fluid-base;
  }
  h1 {
    @apply text-fluid-h1 font-medium leading-tight font-ramilas;
  }
  h2 {
    @apply text-fluid-h2 font-medium font-ramilas leading-snug;
  }
  h3 {
    @apply text-fluid-h3 font-medium font-ramilas leading-snug;
  }
  h4 {
    @apply text-fluid-h3 font-medium leading-normal;
  }
  h5 {
    @apply text-fluid-h5 font-semibold leading-relaxed;
  }

  p {
    @apply text-fluid-base font-normal leading-relaxed font-poppins;
  }
  small {
    @apply text-fluid-sm font-light leading-snug;
  }
}

@layer utilities {
  .text-fluid-lg {
    @apply text-fluid-lg;
  }
}
.modal-scrollbar::-webkit-scrollbar {
  width: 12px;
}
.modal-scrollbar::-webkit-scrollbar-track {
  background: #fff;
  border-top-right-radius: 1rem;
  border-bottom-right-radius: 1rem;
  padding: 0.5rem 0;
}
.modal-scrollbar::-webkit-scrollbar-thumb {
  background-color: #002e00;
  border-radius: 1rem;
  /* border-bottom-right-radius: 1rem; */
  border: 1px solid #fff;
  background-clip: content-box;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* Custom scrollbar styling */
.scrollbar-custom {
  scrollbar-width: inherit;
  scrollbar-color: #002e00 transparent;
  /* Padding-right doesn't work directly on the container */
}

/* For Webkit browsers (Chrome, Safari, etc.) */
.scrollbar-custom::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-custom::-webkit-scrollbar-track {
  background: transparent;
  margin: 6px 0;
  border-radius: 10px;
}

.scrollbar-custom::-webkit-scrollbar-thumb {
  background-color: #002e00;
  border-radius: 10px;
}

@font-face {
  font-family: "TT Ramilas";
  src: url("/fonts/tt-ramilas/TT_Ramillas_Trial_Regular.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: "TT Ramilas";
  src: url("/fonts/tt-ramilas/TT_Ramillas_Trial_Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: "TT Ramilas";
  src: url("/fonts/tt-ramilas/TT_Ramillas_Trial_Italic.ttf") format("truetype");
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: "TT Ramilas";
  src: url("/fonts/tt-ramilas/TT_Ramillas_Trial_Medium.ttf") format("truetype");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: "TT Ramilas";
  src: url("/fonts/tt-ramilas/TT_Ramillas_Trial_Bold_Italic.ttf")
    format("truetype");
  font-weight: bold;
  font-style: italic;
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  animation: scroll 40s linear infinite;
}
@keyframes scroll-fast {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%); /* Assumes content is duplicated once */
  }
}

.animate-scroll-fast {
  animation: scroll-fast 20s linear infinite;
}

.writing-vertical-lr {
  writing-mode: vertical-lr;
  text-orientation: mixed;
}

/* New class for responsive vertical text */
.md-vertical-text {
  writing-mode: horizontal-tb;
}

@media (min-width: 768px) {
  .md-vertical-text {
    writing-mode: vertical-lr;
    text-orientation: mixed;
    transform: rotate(180deg);
  }
}
@media (min-width: 1024px) {
  .programmes-table-shadow {
    box-shadow:
      10px 10px 0px 0px #5f765f,
      20px 20px 0px 0px #002e00;
  }
}

.custom-marker li::marker {
  color: #565656;
}

html {
  scroll-behavior: smooth; /* Optional: for smooth scrolling */
  scroll-padding-top: 120px; /* Adjust this value based on your actual fixed header height */
}

@layer components {
  .scholarship-list a {
    @apply text-[#0E325E] underline underline-offset-2 font-semibold;
  }
}
