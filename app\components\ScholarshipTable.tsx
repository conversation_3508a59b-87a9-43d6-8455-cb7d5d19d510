"use client";

import { X } from "lucide-react";
import { ChevronDown, ChevronUp } from "lucide-react";
import { useState } from "react";

type ScholarshipPoint = {
  label: string;
  url?: string;
};

type ScholarshipGroup = {
  category: string;
  name?: string;
  points: ScholarshipPoint[];
};

const scholarshipData: ScholarshipGroup[] = [
  {
    category: "Scholarships Of Government Aided Courses",
    name: "Name of the Scholarship Scheme Students Applied Through College",
    points: [
      { label: "BC, MBC, DNC Community Scholarship", url: "#" },
      {
        label: "SC, ST, SCC (Converted Christian) Community Scholarship",
        url: "#",
      },
      {
        label:
          "Higher Education Special scholarship scheme for Hostel students (SC,ST,SCC Community only)",
        url: "#",
      },
      { label: "Post Matric Minority Scholarship", url: "#" },
      { label: "Merit Cum Means Minority Scholarship", url: "#" },
      { label: "Central Sector Scholarship Scheme", url: "#" },
      { label: "Scholarships for Student with Disabilities", url: "#" },
      { label: "PG Rank Holder Scholarship", url: "#" },
      {
        label:
          "Financial Assistance for Education of the Wards of Beedi/Cine/IOMC/LSDM Workers",
        url: "#",
      },
      {
        label:
          "Prime Minister's Scholarship Scheme for Central Armed Police Forces And Assam Rifles",
        url: "#",
      },
      { label: "Prime Minister's Scholarship Scheme for RPF/RPSF", url: "#" },
    ],
  },
  {
    category: "Scholarships Of Self Finance Courses (Men & Women)",
    name: "Name of the Scholarship Scheme Students Applied Through College",
    points: [
      { label: "SC, ST, SCC Community Scholarship", url: "#" },
      {
        label:
          "Higher Education Special scholarship scheme for Hostel students (SC,ST,SCC Community only)",
        url: "#",
      },
      { label: "Post Matric Minority Scholarship", url: "#" },
      { label: "Merit Cum Means Minority Scholarship", url: "#" },
      { label: "Central Sector Scholarship Scheme", url: "#" },
      { label: "Scholarships for student with Disabilities", url: "#" },
      { label: "PG Rank Holder Scholarship", url: "#" },
      { label: "PG Single Girl Child Scholarship", url: "#" },
      {
        label:
          "Financial Assistance for Education of the Wards of Beedi/Cine/IOMC/LSDM Workers",
        url: "#",
      },
      {
        label:
          "Prime Minister’s Scholarship Scheme for Central Armed Police Forces And Assam Rifles",
        url: "#",
      },
      { label: "Prime Minister’s Scholarship Scheme for RPF/RPSF", url: "#" },
    ],
  },
  {
    category:
      "Name Of The Scholarship Scheme Students Applied Directly To The Relevant Department",
    points: [
      { label: "Chief Minister’s Farmer Scholarship" },
      { label: "Police Department Scholarship" },
      { label: "Railway Department Scholarship" },
      { label: "Army Department Scholarship" },
      { label: "UGC Research Scholarship for M.Phil., and Ph.D., Courses" },
      {
        label:
          "State Government Research Scholarship for all Communities (Ph.D., Courses Only)",
      },
      {
        label:
          "State Government Research Scholarship for SC, ST and SC Converted Christian Community (Ph.D., Courses Only)",
      },
    ],
  },
];

const ScholarshipTable = () => {
  const [expandedIndex, setExpandedIndex] = useState<number>(0);
  const [modalContent, setModalContent] = useState<string | null>(null);

  const toggleGroup = (index: number) => {
    setExpandedIndex((prev) => (prev === index ? -1 : index));
  };

  const openModal = (label: string) => {
    setModalContent(label);
  };

  const closeModal = () => {
    setModalContent(null);
  };

  return (
    <div className="px-4 sm:px-6 lg:px-8 relative">
      {scholarshipData.map((item, index) => (
        <div
          key={item.category}
          className="mb-6 border border-custom-green rounded-xl shadow-lg overflow-hidden"
        >
          {/* Header */}
          <div
            className={`flex justify-between items-center px-4 sm:px-6 py-3 sm:py-4 font-semibold text-base sm:text-lg cursor-pointer font-ramilas ${
              expandedIndex === index
                ? "bg-custom-green text-white"
                : "bg-custom-light-green text-custom-green"
            }`}
            onClick={() => toggleGroup(index)}
          >
            <span>{item.category}</span>
            {expandedIndex === index ? (
              <ChevronUp size={20} />
            ) : (
              <ChevronDown size={20} />
            )}
          </div>

          {/* Content */}
          <div
            className={`transition-all duration-500 ease-in-out overflow-hidden bg-white ${
              expandedIndex === index
                ? "max-h-[5000px] py-4 sm:py-6 px-4 sm:px-6"
                : "max-h-0 px-4 sm:px-6 py-0"
            }`}
          >
            {item.name && (
              <h3 className="font-semibold text-custom-green mb-2 underline underline-offset-4">
                {item.name}
              </h3>
            )}
            {item.points.length > 0 && (
              <div className="text-left w-full">
                <ul className="list-disc list-outside space-y-2 text-custom-new-green font-poppins text-sm sm:text-base pl-5 sm:pl-8">
                  {item.points
                    .filter((point) => point.label.trim() !== "")
                    .map((point, idx) => (
                      <li key={idx}>
                        {point.url ? (
                          <button
                            onClick={() => openModal(point.label)}
                            className="underline underline-offset-2 hover:text-custom-green focus:outline-none text-left"
                          >
                            {point.label}
                          </button>
                        ) : (
                          <span>{point.label}</span>
                        )}
                      </li>
                    ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      ))}

      {/* Modal Overlay */}
      {modalContent && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full p-6 relative">
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 text-gray-500 hover:text-red-500"
            >
              <X size={24} />
            </button>
            <h2 className="text-xl font-semibold text-custom-green mb-4">
              Scholarship Info
            </h2>
            <p className="text-gray-700">{modalContent}</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ScholarshipTable;
