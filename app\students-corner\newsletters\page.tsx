"use client";

import CTAComponent from "@/app/components/CTAComponent";
import Footer from "@/app/sections/Footer";
import Newsletter from "@/app/sections/Newsletter";
import Navbar from "@/components/Navbar";
import Link from "next/link";

const page = () => {
  return (
    <>
      <Navbar fixed={false} border={false} />
      <header
        className="relative mt-4 px-4 py-8 md:py-16 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex justify-between items-center">
          <h1 className="font-ramilas">Student's Corner</h1>
          <ul className="font-poppins flex flex-col md:flex-row md:gap-8 md:pr-16">
            <li className="list-disc">
              <Link href="/">Home</Link>
            </li>
            <li className="list-disc">Newsletters</li>
          </ul>
        </div>
      </header>

      <main>
        <Newsletter />
        <section className="bg-white py-10 px-4">
          <div className="container max-w-7xl mx-auto">
            <CTAComponent />
          </div>
        </section>
      </main>

      <Footer />
    </>
  );
};

export default page;
