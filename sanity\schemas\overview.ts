// schemas/overview.ts

const overviewSchema = {
  name: "overview",
  title: "Course Overview",
  type: "document",
  fields: [
    {
      name: "image",
      title: "Overview Image",
      type: "url", // Changed from 'image' to 'url'
      // Removed the 'options' field as it's not applicable to 'url' type
    },
    {
      name: "departmentTitle",
      title: "Department Title",
      type: "string",
    },
    {
      name: "description",
      title: "Department Description",
      type: "text",
    },
    {
      name: "mission",
      title: "Mission",
      type: "array",
      of: [{ type: "string" }],
    },
    {
      name: "vision",
      title: "Vision",
      type: "array",
      of: [{ type: "string" }],
    },
  ],
};

export default overviewSchema;
