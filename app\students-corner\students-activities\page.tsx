"use client";

import CTAComponent from "@/app/components/CTAComponent";
import Policies from "@/app/components/Policies";
import StrategicPlan from "@/app/sections/about-us/StrategicPlan";
import Footer from "@/app/sections/Footer";
import CapacityDevelopment from "@/app/sections/students-activities/CapacityDevelopment";
import EDC from "@/app/sections/students-activities/EDC";
import Part5ExtensionActivities from "@/app/sections/students-activities/Part5ExtensionActivities";
import StudentsHealthCareCentre from "@/app/sections/students-activities/StudentsHealthCareCentre";
import Navbar from "@/components/Navbar";
import Link from "next/link";

const page = () => {
  const policies = [
    {
      _id: "1",
      name: "Anti Ragging Committee",
      url: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/StudentCorner/Anti-Ragging-Committee-2024-2025.pdf",
    },
    {
      _id: "2",
      name: "Prevention of Caste Based Discrimination Committee",
      url: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/StudentCorner/PREVENTIONCASTECOMMITTEE.pdf",
    },
    {
      _id: "3",
      name: "Industrial Visit - Details",
      url: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/StudentCorner/INDUSTRIALVISIT-DETAILS.pdf",
    },
    {
      _id: "4",
      name: "Period of Course Completion - Admitted After 2018-2019",
      url: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/StudentCorner/coursecompletionafter1819.pdf",
    },
    {
      _id: "5",
      name: "Period of Course Completion - Admitted Before 2018-2019",
      url: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/StudentCorner/coursecompletionbefore1819.pdf",
    },
    {
      _id: "6",
      name: "SWAYAM Courses",
      url: "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/StudentCorner/SWAYAM-BOOKLET.pdf",
    },
  ];
  return (
    <>
      <Navbar fixed={false} border={false} />
      <header
        className="relative mt-4 px-4 py-8 md:py-16 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex justify-between items-center">
          <h1 className="font-ramilas">Student's Corner</h1>
          <ul className="font-poppins flex flex-col md:flex-row md:gap-8 md:pr-16">
            <li className="list-disc">
              <Link href="/">Home</Link>
            </li>
            <li className="list-disc">Student's Activities</li>
          </ul>
        </div>
      </header>
      <main>
        <Part5ExtensionActivities />
        <Policies title="Student's Corner" items={policies} />
        <StudentsHealthCareCentre />
        <EDC />
        {/* Academic Calendar */}
        <StrategicPlan
          title="Academic Calendar"
          description="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dol"
          sectionId="academic-calendar"
          downloadLink="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/PDFs/StudentsCorner/StudentsActivities/AcademicCalender/AcademicCalendar_2024-2025.pdf"
        />
        <CapacityDevelopment />
        <section className="bg-white pb-10 px-4">
          <div className="container max-w-7xl mx-auto">
            <CTAComponent />
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
};

export default page;
