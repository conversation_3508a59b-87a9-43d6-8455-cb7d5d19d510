import { client } from "@/lib/sanity"; // Adjust path if your client is elsewhere

export interface AnnualReportData {
  _id: string;
  year: string;
  label: string;
  url: string; // Represents the reportUrl from Sanity
}

export async function getAnnualReports(): Promise<AnnualReportData[]> {
  // Query to fetch annual report documents, ordering by year descending
  const query = `*[_type == "annualReport"] | order(year desc) {
    _id,
    year,
    label,
    "url": reportUrl // Select the reportUrl field
  }`;

  const results = await client.fetch(query);
  console.log("Fetched annual reports results: ", results);

  // Map results to the AnnualReportData interface
  return results.map((report: any) => ({
    _id: report._id,
    year: report.year || 'Unknown Year',
    label: report.label || 'No Label',
    url: report.url || '#', // Provide a fallback URL
  }));
}