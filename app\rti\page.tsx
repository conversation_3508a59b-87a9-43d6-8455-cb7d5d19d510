"use client";

import { usePathname } from "next/navigation";
import Navbar, { menuItems } from "@/components/Navbar";
import Link from "next/link";
import Sidebar from "../components/Sidebar";
import Footer from "../sections/Footer";
import { FileText } from "lucide-react";

const Page = () => {
  const pathname = usePathname(); // Get current pathname
  // Find the current section based on the pathname
  const currentSection = menuItems.find((item) => item.href === pathname);
  return (
    <>
      <Navbar fixed={true} border={true} />
      <header
        className="relative px-4 md:px-14 xl:px-28 pt-40 pb-16 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex flex-col items-center gap-4 md:flex-row md:justify-between">
          <h1 className="font-ramilas">RTI</h1>
          <ul className="font-poppins flex flex-row justify-center items-center gap-8 md:flex-row md:items-center md:gap-8 md:pr-16">
            <li className="list-disc">
              {" "}
              {/* Restored list-disc */}
              <Link href="/">Home</Link>
            </li>
            <li className="list-disc">
              {" "}
              {/* Restored list-disc */}
              RTI
            </li>
          </ul>
        </div>
      </header>

      <main>
        {currentSection && (
          <Sidebar subsections={currentSection.subSections || []} />
        )}
        <section
          className="bg-[#CFD7CF] py-10 px-10 shadow-custom-glow relative mb-16"
          id="right-to-information-act"
        >
          <div className="container max-w-7xl mx-auto space-y-6 text-center mb-4">
            <h2 className="text-custom-green mb-6">Right to Information Act</h2>
            <div className="text-custom-new-green space-y-4">
              <p>
                <span className="font-semibold">Authority under RTI Act:</span>
                <br />
                Principal
                <br />
                Jamal Mohamed College (Autonomous), Tiruchirappalli – 620 020{" "}
                <a
                  href="https://jmc.edu/aqar/RTI-Statutory-Declaration.pdf"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-red-600 hover:underline ml-1"
                >
                  <FileText size={16} className="text-red-600 mr-1" /> (PDF)
                </a>
              </p>
              <p>
                Under the Right to Information Act, the authority designated to
                handle RTI requests is the Principal of Jamal Mohamed College
                (Autonomous), Tiruchirappalli – 620 020. Individuals who wish to
                obtain information under the Right to Information Act, 2005,
                wherever applicable, can submit their requests to the
                above-mentioned authority using the prescribed form below.
              </p>
            </div>
          </div>
          <div className="absolute left-1/2 md:left-3/4 -translate-x-1/2 bottom-0 translate-y-1/2 z-10">
            <a
              href="https://jmc.edu/aqar/RTI-Form.pdf"
              target="_blank"
              rel="noopener noreferrer"
              className="font-poppins bg-custom-green hover:bg-green-800 text-white font-semibold px-4 sm:px-6 md:px-8 py-3 sm:py-4 md:py-6 text-base md:text-xl rounded-xl shadow-[0px_0px_34px_0px_#00000026] flex items-center gap-2 sm:gap-4 md:gap-24 transition whitespace-nowrap"
            >
              Click Here To Check
            </a>
          </div>
        </section>
      </main>

      <Footer />
    </>
  );
};

export default Page;
