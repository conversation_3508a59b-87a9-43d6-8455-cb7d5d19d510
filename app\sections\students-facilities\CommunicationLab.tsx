"use client";

import { aboutCommunicationClass } from "@/app/helper/helper";
import Image from "next/image";
import { useEffect, useState } from "react";
import { client } from "@/lib/sanity";
import { JMC_STUDENT_FACILITIES_QUERY } from "@/sanity/lib/queries";

interface FacilityImage {
  name: string;
  imageUrl: string;
}

interface JmcStudentFacilitiesData {
  _id: string;
  studentCounsellingImages: FacilityImage[];
  cafeteriaImages: FacilityImage[];
  dayCareCentreImages: FacilityImage[];
  transportImages: FacilityImage[];
  communicationLabImages: FacilityImage[];
}

const CommunicationLab = () => {
  const [communicationLabImages, setCommunicationLabImages] = useState<FacilityImage[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    client.fetch(JMC_STUDENT_FACILITIES_QUERY).then((data: JmcStudentFacilitiesData[]) => {
      if (data.length > 0 && data[0].communicationLabImages?.length > 0) {
        // Filter out images with empty URLs
        const validImages = data[0].communicationLabImages.filter(img =>
          img.imageUrl && img.imageUrl.trim() !== ""
        );
        setCommunicationLabImages(validImages);
      }
      setIsLoading(false);
    }).catch(() => {
      setIsLoading(false);
    });
  }, []);
  const boysbatchTimings = [
    {
      batch: "First batch",
      timing: "2.30 pm to 3.30 pm",
    },
    {
      batch: "Second batch",
      timing: "3.30 pm to 4.30 pm",
    },
    {
      batch: "Third batch",
      timing: "4.30 pm to 5.30 pm",
    },
  ];

  const girlsBatchTimings = [
    {
      batch: "First batch",
      timing: "9.30 am to 10.30 am",
    },
    {
      batch: "Second batch",
      timing: "10.45 am to 11.45 am",
    },
    {
      batch: "Third batch",
      timing: "12.00 pm to 1.00 pm",
    },
  ];

  return (
    <section className="bg-white pb-10 px-4" id="cafeteria">
      <div className="container max-w-7xl mx-auto space-y-6">
        <h2 className="text-custom-green text-center">Communication Lab</h2>
        {isLoading ? (
          <div className="flex flex-col md:flex-row gap-6">
            <div className="h-[200px] md:h-[350px] border-[3px] border-gray-200 flex-1 rounded-xl overflow-hidden shadow-md bg-gray-200 animate-pulse">
              <div className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-shimmer"></div>
            </div>
            <div className="h-[200px] md:h-[350px] border-[3px] border-gray-200 flex-1 rounded-xl overflow-hidden shadow-md bg-gray-200 animate-pulse">
              <div className="w-full h-full bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-shimmer"></div>
            </div>
          </div>
        ) : communicationLabImages.length > 0 ? (
          <div className="flex flex-col md:flex-row gap-6">
            {communicationLabImages.slice(0, 2).map((img, idx) => (
              <div key={idx} className="relative h-[200px] md:h-[350px] border-[3px] border-custom-green flex-1 rounded-xl overflow-hidden shadow-md">
                <Image
                  src={img.imageUrl}
                  alt={img.name}
                  width={500}
                  height={400}
                  className="w-full h-full object-cover"
                />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-8">
            <p>No communication lab images available at the moment.</p>
          </div>
        )}
        <div className="rounded-2xl border border-gray-200 p-4 md:p-10 lg:px-16 lg:py-10 shadow-sm bg-white space-y-6">
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              About Our Civil Services Examination Study Centre
            </h3>
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              <li>
                Communication Training Centre offers the communication classes
                that offer the flexibility to study. Our highly innovative and
                unique mix of engaging Communication course is accomplished by
                assessments and interactive sessions that allow the students to
                progress consistently. Communication training center provides a
                Good approach that follows best practices for learning and
                practicing English Communication.
              </li>
              <li>
                Communication English learning features in a very natural way
                similar to native speakers learn their first language. We
                analyze the issues faced by the Students, their capacity to cope
                up and devise a plan to improvise their skills in most
                effortless way of learning. Unlike other subjects, Communication
                learning attributes to frequent continuous practice and mind
                mapping techniques.
              </li>
              <li>
                We adopt a distinct method beyond the classroom, we implement an
                integrated training strategy, our sessions are structured to be
                interactive and participative in a fun and dynamic environment.
                We engage students in practical sessions, and we motivate
                student to take in part discussions and presentations skills.
              </li>
              <li>
                <strong>We just don’t train, we inspire.</strong> We are aware
                that English learning is not like other subjects.
              </li>
              <li>
                We allow students to practice additionally, interactive and
                regular feedback sessions from trainer, for the advantage to
                acquire skills faster.
              </li>
              <li>
                We know that Language learning is as important as learning any
                other subject, in this competitive world having extraordinary
                technical skills underplay when once couldn’t express and
                present himself properly.{" "}
                <strong>
                  Our training objective is not just to make our students speak
                  English as learners but as professionals.
                </strong>
              </li>
              <li>
                Often our students are dumbfounded unable to talk, speaking at
                the appropriate time with appropriate vocabulary is the key to
                success, we eliminate fear of speaking, and we break the ice and
                make them feel free to open their mouth.
              </li>
              <li>
                Speaking a language should be spontaneous and automatic. We
                encourage thoughtless speech that helps to exceed your
                hesitation due to inexperience. We guide you to come out of
                being scared of doing mistakes while speaking.
              </li>
              <li>
                As learning a language through conscious effort doesn’t yield
                the desired result we mainly rely on infusing language into the
                brain using modern brain infusion methods. Our methods are live
                ideas which include brainstorming sessions, careless gossiping,
                small talk, storytelling, thoughtless speech, jokes sessions,
                socializing, and critical thinking group Discussion etc.
              </li>
              <li>
                Listening, by all means, is a way of acquiring language ability.
                We devote a part of the lessons by making you hear the listening
                content, educate, and evaluate. Our courses contain tests that
                help you to realize your level of competence.
              </li>
            </ul>
          </div>
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              Why Study this Course?
            </h3>
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              <li>Over two-third of the worlds scientists read in English</li>
              <li>Three quarters of the worlds mail in written in English</li>
              <li>
                80% of the world's electronically stored information is in
                English
              </li>
            </ul>
          </div>
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              Develop Your Career
            </h3>
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              <li>
                Most professional jobs require a good level in English- think
                how impressive "excellent level in English "will look on your CV
              </li>
              <li>
                Business and commerce are increasingly driven by international
                trade and if you are going to grow in your job you need to be
                able to follow this trend
              </li>
              <li>
                Technical skills are essential in modern industries and you need
                to be able to both read and write technical English if you are
                going to maintain your technical competence
              </li>
            </ul>
          </div>

          {/* About class */}
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              About the Class
            </h3>
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              {aboutCommunicationClass?.map((about: any, idx: any) => (
                <li key={idx}>{about}</li>
              ))}
            </ul>
          </div>

          {/* About Course */}
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              About Course:
            </h3>
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              <li>
                <span className="font-semibold">Level -A- Basic</span>
                <ul className="list-disc pl-5 mt-2 space-y-1">
                  <li>Learning with fun</li>
                </ul>
              </li>
              <li>
                <span className="font-semibold">Level -B- Intermediate</span>
                <ul className="list-disc pl-5 mt-2 space-y-1">
                  <li>Phonetics</li>
                  <li>Communicative English</li>
                  <li>Situational conversation</li>
                  <li>Global communicative English</li>
                  <li>Error in spoken English</li>
                  <li>Grammar</li>
                </ul>
              </li>
              <li>
                <span className="font-semibold">
                  Level -C- Advanced English
                </span>
                <ul className="list-disc pl-5 mt-2 space-y-1">
                  <li>BBC Current Events</li>
                  <li>Dialogue (U.S Accents)</li>
                  <li>Idioms</li>
                  <li>Phrases</li>
                </ul>
              </li>
              <li>
                <span className="font-semibold">Projects</span>
                <ul className="list-disc pl-5 mt-2 space-y-1">
                  <li>Handling Telephone Calls</li>
                  <li>Career Planning</li>
                  <li>Making C.V</li>
                  <li>Email Skill</li>
                  <li>Group Discussion</li>
                  <li>Debating</li>
                  <li>Public Speaking</li>
                  <li>Presentation Skills</li>
                  <li>Reporting</li>
                  <li>Soft Skills</li>
                </ul>
              </li>
              <li>
                <span className="font-semibold">
                  IELTS (INTERNATIONAL ENGLISH LANGUAGE TESTING SYSTEM)
                </span>
              </li>
              <li>
                <span className="font-semibold">
                  TOEFL (TEST OF ENGLISH AS A FOREIGN LANGUAGE)
                </span>
                <ul className="list-disc pl-5 mt-2 space-y-1">
                  <li>Pronunciation</li>
                  <li>Vocabulary</li>
                  <li>Listening practice</li>
                  <li>Listening and speaking</li>
                  <li>Exercise</li>
                </ul>
              </li>
            </ul>
          </div>
          <div className="space-y-6">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              Timing for Girls
            </h3>
            <div className="overflow-x-auto flex justify-center pb-8">
              <table className="w-full max-w-4xl mx-auto border-collapse rounded-2xl overflow-hidden font-ramilas font-medium relative border border-custom-green bg-custom-light-green md:shadow-milestone">
                <thead>
                  <tr className="bg-custom-green text-white rounded-t-2xl">
                    <th className="px-6 md:px-8 py-4 text-left">Batches</th>
                    <th className="px-6 md:px-8 py-4 text-center">Timing</th>
                  </tr>
                </thead>
                <tbody className="text-custom-green">
                  {girlsBatchTimings?.map((item: any, index: any) => (
                    <tr
                      key={index}
                      className={
                        index !== girlsBatchTimings.length - 1
                          ? "border-b border-gray-300"
                          : ""
                      }
                    >
                      <td className="px-6 md:px-8 py-4">{item.batch}</td>
                      <td className="px-6 md:px-8 py-4 text-center">
                        {item.timing}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Timing for boys */}
          <div className="space-y-6">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              Timing for Boys
            </h3>
            <div className="overflow-x-auto flex justify-center pb-8">
              <table className="w-full max-w-4xl mx-auto border-collapse rounded-2xl overflow-hidden font-ramilas font-medium relative border border-custom-green bg-custom-light-green md:shadow-milestone">
                <thead>
                  <tr className="bg-custom-green text-white rounded-t-2xl">
                    <th className="px-6 md:px-8 py-4 text-left">Batches</th>
                    <th className="px-6 md:px-8 py-4 text-center">Timing</th>
                  </tr>
                </thead>
                <tbody className="text-custom-green">
                  {boysbatchTimings?.map((item: any, index: any) => (
                    <tr
                      key={index}
                      className={
                        index !== boysbatchTimings.length - 1
                          ? "border-b border-gray-300"
                          : ""
                      }
                    >
                      <td className="px-6 md:px-8 py-4">{item.batch}</td>
                      <td className="px-6 md:px-8 py-4 text-center">
                        {item.timing}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CommunicationLab;
