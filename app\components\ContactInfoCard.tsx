import Image from 'next/image';
import { Phone, Mail, MapPin } from 'lucide-react';

const ContactInfoCard = () => {
  // Define your custom green hex code here (replace #166534 if needed)
  const customGreenColor = "#166534"; 

  const contactDetails1 = [
    {
      // Use fill for green background, stroke for white lines
      icon: <Phone size={20} fill={customGreenColor} stroke="white" strokeWidth={1} />, 
      text: "+91 93609 63012",
      href: "tel:+91 93609 63012",
    },
    {
      icon: <Mail size={20} fill={customGreenColor} stroke="white" strokeWidth={1} />, 
      text: "<EMAIL>",
      href: "mailto:<EMAIL>",
    },
    {
      icon: <MapPin size={20} fill={customGreenColor} stroke="white" strokeWidth={1} />, 
      text: (
        <>
          7, Race Course Road, <br />
          Kaja <PERSON>, <br />
          Tiruchirappalli, <br />
          Tamil Nadu 620020
        </>
      ),
      href: "#", // Add a link to Google Maps if desired
    },
  ];
  // const contactDetails2 = [
  //   {
  //     // Use fill for green background, stroke for white lines
  //     icon: <Phone size={20} fill={customGreenColor} stroke="white" strokeWidth={1} />, 
  //     text: "+1012 3456 789",
  //     href: "tel:+10123456789",
  //   },
  //   {
  //     icon: <Mail size={20} fill={customGreenColor} stroke="white" strokeWidth={1} />, 
  //     text: "<EMAIL>",
  //     href: "mailto:<EMAIL>",
  //   },
  //   {
  //     icon: <MapPin size={20} fill={customGreenColor} stroke="white" strokeWidth={1} />, 
  //     text: "132 Dartmouth Street Boston, Massachusetts 02156 United States",
  //     href: "#", // Add a link to Google Maps if desired
  //   },
  // ];
  return (
    <div className="relative bg-white rounded-2xl border-2 border-custom-green overflow-hidden shadow-lg">
      {/* Optional Background Pattern - Increased opacity */}
      <div 
        className="absolute inset-0 opacity-100 pointer-events-none" // Changed from opacity-10 to opacity-20
        style={{ 
          backgroundImage: "url('/homepage/academicsbgimg.png')", 
          backgroundSize: 'cover' 
        }}
      ></div>

      {/* Changed grid columns for unequal width: md:grid-cols-5 */}
      <div className="relative grid grid-cols-1 md:grid-cols-5 gap-0">
        {/* Left Side: Image (Now first) - Added p-8 back, kept col-span-2 */}
        <div className="p-10 relative w-full h-auto md:h-full min-h-[300px] md:col-span-2 flex items-center justify-center"> 
          <div className="relative w-full h-full aspect-square md:aspect-auto"> 
            <Image
              src="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Hero+Section/01.jpg"
              alt="College Building"
              fill
              className="object-cover rounded-md" 
            />
          </div>
        </div>

        {/* Right Side: Content (Now second) - Kept col-span-3 */}
        <div className="p-8 md:p-12 md:col-span-3">
          {/* Added lg:text-4xl */}
          <h2 className="text-3xl lg:text-4xl font-bold text-custom-green mb-2 font-ramilas">Contact Information</h2>
          {/* Added lg:text-base */}
          <p className="text-gray-600 mb-8 font-poppins lg:text-base">Say something to start a live chat!</p>

          {/* Contact Info Grid - Changed to sm:grid-cols-1 */}
          <div className="grid grid-cols-1 sm:grid-cols-1 gap-x-8 gap-y-6">
            {/* Column 1 - Now takes full width */}
            <div className="space-y-4">
              {contactDetails1.map((item, index) => (
                <a key={`col1-${index}`} href={item.href} className="flex items-start space-x-3 lg:space-x-7 group"> {/* Changed items-center to items-start */} 
                  <span className="flex-shrink-0 pt-1">{item.icon}</span> {/* Added pt-1 for alignment */} 
                  {/* Added lg:text-base */}
                  <span className="text-sm lg:text-base text-gray-700 group-hover:text-custom-green transition-colors font-poppins">
                    {item.text}
                  </span>
                </a>
              ))}
            </div>
            {/* Column 2 Removed */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactInfoCard;