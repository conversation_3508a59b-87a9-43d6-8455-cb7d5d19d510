"use client";

// Removed Image, Link imports if no longer needed directly here
import { useState, useEffect } from "react";
import Navbar from "@/components/Navbar";
import LifeAtJamal from "./sections/LifeAtJamal";
import DepartmentsSection from "./sections/DepartmentsSection";
import AdmissionSection from "./sections/AdmissionSection";
import TopResearch from "./sections/TopResearch";
import StudentSupportFacility from "./sections/StudentSupportFacility";
import PlacementSection from "./sections/PlacementSection";
import EventsSection from "./sections/EventsSection";
import NoticeAndUpdates from "./sections/NoticeAndUpdates";
import AlumniAssociation from "./sections/AlumniAssociation";
import Footer from "./sections/Footer";
import CollegeTourSection from "./sections/CollegeTourSection";
import Modal from "@/components/ui/modal";
import HeroSection from "./sections/HeroSection"; // Import the new component

// Removed imageUrls array and currentImageIndex state/useEffect

export default function Home() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  // Removed currentImageIndex state

  useEffect(() => {
    // Keep modal logic
    const timer = setTimeout(() => {
      setIsModalOpen(true);
    }, 100);

    // Removed image cycling interval logic

    return () => {
      clearTimeout(timer);
      // Removed clearInterval
    };
  }, []);

  const handleCloseModal = () => {
    console.log("Parent: handleCloseModal called"); // Add logging
    setIsModalOpen(false);
  };

  return (
    <div className="min-h-screen">
      <Navbar />
      <Modal isOpen={isModalOpen} onClose={handleCloseModal} />

      <main>
        {/* Render the new HeroSection component */}
        <HeroSection />

        {/* Pass isModalOpen state down */}
        <LifeAtJamal isModalOpen={isModalOpen} />

        {/* College Tour Section */}
        <CollegeTourSection />
        {/* Departments Section */}
        <DepartmentsSection />
        {/* Admission Section */}
        <AdmissionSection />
        {/* Top Research Section */}
        {/* <TopResearch /> */}
        {/* Student Support & Facility Section */}
        <StudentSupportFacility />
        {/* Placement Section */} 
        <PlacementSection />
        {/* Events Section */}
        <EventsSection />
        {/* Alumni Association Section */}
        <AlumniAssociation />
        {/* Notice Section */}
        <NoticeAndUpdates />
      </main>
      <Footer />
    </div>
  );
}
