"use client";

import { useState, useEffect } from "react";
import { Loader2 } from "lucide-react"; // Import Loader icon
import { getAnnualReports, AnnualReportData } from "@/sanity/lib/getAnnualReports"; // Import fetch function and type

// Define the component props if any (none needed for now)
// interface AnnualReportProps {}

const AnnualReport = () => {
  const [activeReport, setActiveReport] = useState<string | null>(null); // Changed to string for _id
  const [reports, setReports] = useState<AnnualReportData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Remove the hardcoded reports array
  // const reports = [
  //   { year: "2024–25", label: "Men & Women", url: "/reports/2024-25.pdf" },
  //   ...
  // ];

  useEffect(() => {
    const fetchReports = async () => {
      setIsLoading(true);
      try {
        const fetchedReports = await getAnnualReports();
        setReports(fetchedReports);
      } catch (error) {
        console.error("Failed to fetch annual reports:", error);
        // Optionally set an error state here
      } finally {
        setIsLoading(false);
      }
    };

    fetchReports();
  }, []); // Empty dependency array ensures this runs once on mount

  return (
    <section className="bg-white pt-20 pb-12 px-8 min-h-[300px]" id="annual-report"> {/* Added min-height */}
      <div className="container mx-auto max-w-7xl">
        <h2 className="text-custom-green text-center mb-12 text-3xl md:text-4xl font-ramilas"> {/* Increased heading size and margin */}
          Annual Report
        </h2>

        {isLoading ? (
          // Loading State
          <div className="flex justify-center items-center h-60">
            <Loader2 className="w-12 h-12 animate-spin text-custom-green" />
          </div>
        ) : reports.length === 0 ? (
          // Empty State
          <div className="flex justify-center items-center h-60">
            <p className="text-xl font-poppins text-gray-500">No Annual Reports Available.</p>
          </div>
        ) : (
          // Reports Display
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-10 md:gap-6 lg:gap-12 mx-auto">
            {reports.map((report) => (
              <div
                key={report._id} // Use _id from Sanity as key
                onClick={() => setActiveReport(report._id)} // Use _id to track active report
                className={`rounded-xl p-6 h-72 w-80 sm:w-72 sm:h-64 md:w-56 md:h-72 mx-auto shadow-[0_0_15px_rgba(0,0,0,0.1)] hover:shadow-[0_0_20px_rgba(0,0,0,0.15)] transition-all duration-300 text-center cursor-pointer flex items-center justify-center
                  ${
                    report._id === activeReport
                      ? "bg-custom-green text-white scale-105" // Add slight scale on active
                      : "bg-[#F7F8F7] text-custom-green hover:bg-custom-green hover:text-white"
                  }`}
              >
                {/* Use target="_blank" to open PDF in new tab, remove download if not needed */}
                <a href={report.url} target="_blank" rel="noopener noreferrer" className="block w-full h-full flex items-center justify-center">
                  <div className="flex flex-col items-center justify-center">
                    <div className="font-poppins font-semibold text-2xl sm:text-xl md:text-2xl">
                      {report.label}
                    </div>
                    <div className="font-poppins font-medium text-xl sm:text-lg md:text-xl mt-3">
                      {report.year}
                    </div>
                  </div>
                </a>
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default AnnualReport;
