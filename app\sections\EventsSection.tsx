"use client"; // Add this directive for client-side hooks

import React, { useState, useEffect } from "react";
import EventsDisplay from "../components/EventsDisplay";
import { getEvents, Event } from "@/sanity/lib/getEvents"; // Import the new fetch function and type

const EventsSection = () => {
  const [events, setEvents] = useState<Event[]>([]); // State for fetched data
  const [isLoading, setIsLoading] = useState(true); // Loading state
  const [error, setError] = useState<string | null>(null); // Error state

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const data = await getEvents();
        console.log("Fetched events for section:", data);
        
        // Optional: Format date here if needed before setting state
        const formattedData = data.map(event => ({
          ...event,
          date: new Date(event.date + 'T00:00:00').toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          }),
        }));

        setEvents(formattedData);
      } catch (err) {
        console.error("Failed to fetch events:", err);
        setError("Failed to load events. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, []); // Empty dependency array ensures this runs once on mount

  return (
    <section className=" pt-16 pb-4 bg-white">
      {isLoading ? (
        <div className="text-center py-10">Loading events...</div>
      ) : error ? (
        <div className="text-center py-10 text-red-600">{error}</div>
      ) : events.length > 0 ? (
        <EventsDisplay
          // Pass the fetched and formatted events
          // Ensure EventsDisplay props match the Event interface keys (imageUrl -> image, altText -> alt)
          events={events.map(e => ({
            image: e.imageUrl, // Correctly uses imageUrl from state
            title: e.title,
            date: e.date,
            description: e.description,
            alt: e.altText // Correctly uses altText from state
          }))}
          title="Upcoming College Events"
          description="Get ready for an exciting year ahead at Jamal Mohamed College! We have a range of upcoming events, from academic workshops and cultural celebrations to sports tournaments and inspiring guest lectures. These events provide our students with opportunities to learn, connect, and showcase their talents."
        />
      ) : (
        <div className="text-center py-10">No upcoming events found.</div>
      )}
    </section>
  );
};

export default EventsSection;
