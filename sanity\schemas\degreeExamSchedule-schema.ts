const degreeExamSchedule = {
  name: "degreeExamSchedule",
  title: "Examination - Degree Examination Schedule",
  type: "document",
  fields: [
    {
      name: "title",
      title: "Exam Title (e.g. UG EXAM)",
      type: "string",
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: "date",
      title: "Exam Month & Year (e.g. April 2025)",
      type: "string",
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: "pdfUrl",
      title: "Download PDF URL",
      type: "url",
      validation: (Rule: any) =>
        Rule.required().uri({
          allowRelative: false,
          scheme: ["http", "https"],
        }),
    },
  ],
};

export default degreeExamSchedule;
