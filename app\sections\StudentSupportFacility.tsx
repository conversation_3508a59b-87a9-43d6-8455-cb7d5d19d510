import React from "react";
import Image from "next/image";
import Link from "next/link"; // Import Link here

const StudentSupportFacility = () => {
  // Array of facility cards with their vertical text and links
  const facilityCards = [
    {
      image:
        "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/StudentSupportAndFacility/01.jpg",
      text: "Students Facilities",
      href: "/students-corner/students-facilities", // Added href
    },
    {
      image:
        "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/StudentSupportAndFacility/02.jpg",
      text: "Students Activities",
      href: "/students-corner/students-activities", // Added href
    },
    {
      image:
        "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/StudentSupportAndFacility/03.png",
      text: "News Letters",
      href: "/students-corner/newsletters", // Added href
    },
    {
      image:
        "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/StudentSupportAndFacility/04.jpg",
      text: "Students Amenities",
      href: "/students-corner/students-amenities", // Added href
    },
    {
      image:
        "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/StudentSupportAndFacility/05.jpg",
      text: "Library",
      href: "/students-corner/library", // Added href (Assuming this path, please verify)
    },
    // {
    //   image: "",
    //   text: "More",
    //   isTransparent: true
    // },
  ];

  return (
    <section className="py-12 bg-white">
      <div className="container mx-auto max-w-7xl px-10">
        {/* Row 1: Title */}
        <h2 className="text-3xl md:text-4xl font-bold text-center text-custom-green mb-10">
          Student Support & Facility
        </h2>

        {/* Row 2: Image Cards with Vertical Text */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:flex lg:flex-nowrap justify-center gap-6 mb-10">
          {facilityCards.map((card, index) => (
            <Link key={index} href={card.href} className="block">
              <div
                className="relative h-80 md:h-64 lg:h-72 w-full sm:w-[150px] overflow-hidden rounded-2xl bg-gray-200 mx-auto cursor-pointer group" // Added group and changed default bg
              >
                {card.image && (
                  <>
                    <Image
                      src={card.image}
                      alt={card.text}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105" // Removed opacity-80, added hover effect
                    />
                    {/* Green Overlay */}
                    <div className="absolute inset-0 bg-custom-green opacity-60 group-hover:opacity-70 transition-opacity duration-300"></div>
                  </>
                )}
                <div className="absolute inset-0 flex items-center justify-center z-10">
                  {" "}
                  {/* Added z-10 */}
                  <div className="text-white font-medium text-2xl md-vertical-text text-center px-2">
                    {" "}
                    {/* Added text-center and px-2 */}
                    {card.text}
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Row 3: Paragraph */}
        <p className="text-gray-700 text-center max-w-6xl mx-auto mb-6">
          At Jamal Mohamed College, we are committed to supporting every
          student's journey. From well-equipped libraries and modern
          laboratories to career guidance, scholarships, and mentorship
          programs, we provide a wide range of facilities and services to help
          our students succeed.
        </p>

        {/* Row 4: Button */}
        <div className="flex justify-center">
          <Link href="/students-corner/students-facilities">
            {" "}
            {/* Added Link component */}
            <button className="text-xl bg-white text-custom-green px-8 py-3 rounded-full font-bold border-2 border-custom-green">
              Explore Student Corner
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default StudentSupportFacility;
