import React from "react";
import Image from "next/image";

interface TeamMemberProps {
  image: string;
  name: string;
  title: string;
  description: string;
  currentPosition?: string;
  email?: string;
}

const TeamMemberCard: React.FC<TeamMemberProps> = ({
  image,
  name,
  title,
  description,
  currentPosition,
  email,
}) => {
  return (
    <div className="flex flex-col md:flex-row overflow-hidden mb-3 sm:mb-4 px-2 sm:px-4 ">
      <div className="w-full md:w-1/4 lg:w-1/5 h-74 md:h-64 sm:h-64 md:h-auto relative">
        <Image
          src={image}
          alt={name}
          width={300}
          height={300}
          className="object-cover object-center w-full h-full rounded-lg"
        />
      </div>
      <div className="w-full md:w-3/4 lg:w-4/5 p-4 sm:p-6">
        <h3 className="text-xl sm:text-2xl font-bold text-[#002E00] mb-2">{title}</h3>
        <p className="text-sm sm:text-base text-[#555555] mb-2">
          <span className="font-semibold">{name}, </span>
          {description}
        </p>
        {currentPosition && (
          <p className="text-sm sm:text-base text-[#555555] mb-2">
            <span className="font-semibold">Currently serving as: </span>
            {currentPosition}
          </p>
        )}
        {email && (
          <p className="text-sm sm:text-base text-[#555555]">
            <span className="font-semibold">Email: </span>
            {email}
          </p>
        )}
      </div>
    </div>
  );
};

const ResearchTeam = () => {
  const teamMembers: TeamMemberProps[] = [
    {
      image: "/research/team/placeholder.png",
      name: "Dr. A. Shajahan",
      title: "Dean of Research and Consultancy",
      description: "Associate Professor of Botany, Jamal Mohamed College, Tiruchirappalli.",
      currentPosition: "Dean of Research and Consultancy",
      email: "<EMAIL>",
    },
    {
      image: "/research/team/placeholder.png",
      title: "Research Advisor",
      name: "Dr. S. Mohamed Salique",
      description: "M.Sc., M.Phil., Ph.D., Associate Professor & Head, Department of Botany, Jamal Mohamed College (Autonomous), Tiruchirappalli.",
      currentPosition: "Research Advisor for Botany Department",
    },
    {
      image: "/research/team/placeholder.png",
      title: "Research Advisor",
      name: "Dr. A. Mohamed Ibraheem",
      description: "M.Sc., M.Phil., Ph.D., Associate Professor & Head, Department of Chemistry, Jamal Mohamed College (Autonomous), Tiruchirappalli.",
      currentPosition: "Research Advisor for Chemistry Department",
    },
    {
      image: "/research/team/placeholder.png",
      title: "Coordinator: Tamil Innovation and Incubation Centre (TIIC & IPFC)",
      name: "Dr. S. Mohamed Rafique",
      description: "M.A., M.Phil., Ph.D., Associate Professor, Department of Tamil, Jamal Mohamed College (Autonomous), Tiruchirappalli.",
      currentPosition: "Coordinator of Tamil Innovation and Incubation Centre",
    },
    {
      image: "/research/team/placeholder.png",
      title: "Dean of Research (Women)",
      name: "Dr. A. Noor Jahan",
      description: "M.Sc., M.Phil., Ph.D., Associate Professor & Head, Department of Botany, Jamal Mohamed College (Autonomous), Tiruchirappalli.",
      currentPosition: "Dean of Research for Women's Section",
    },
    {
      image: "/research/team/placeholder.png",
      title: "Coordinator JMIC",
      name: "Dr. K. Syed Nizamudeen",
      description: "M.Sc., M.Phil., Ph.D., Associate Professor, Department of Chemistry, Jamal Mohamed College (Autonomous), Tiruchirappalli.",
      currentPosition: "Coordinator of Jamal Mohamed Innovation Centre",
    },
    {
      image: "/research/team/placeholder.png",
      title: "Research Core Committee Member",
      name: "Dr. M. Sheik Ali Rowthar",
      description: "M.Sc., M.Phil., Ph.D., Associate Professor, Department of Chemistry, Jamal Mohamed College (Autonomous), Tiruchirappalli.",
      currentPosition: "Member of Research Core Committee",
    },
    {
      image: "/research/team/placeholder.png",
      title: "Junior Assistant",
      name: "Mr. S. Sikkander",
      description: "B.A., Junior Assistant, Research & Consultancy, Jamal Mohamed College (Autonomous), Tiruchirappalli.",
      currentPosition: "Junior Assistant in Research & Consultancy Department",
    },
  ];

  return (
    <section className="py-8 sm:py-12 md:py-16 bg-[#EBEBEB]">
      <h2 className="text-2xl sm:text-3xl font-bold text-center text-[#002E00] mb-4 sm:mb-8 px-4">
        Controller of Examinations
      </h2>
      <div className="container max-w-7xl mx-auto px-2 sm:px-4">
        <div className="space-y-3 sm:space-y-4">
          {teamMembers.map((member, index) => (
            <TeamMemberCard
              key={index}
              image={member.image}
              name={member.name}
              title={member.title}
              description={member.description}
              currentPosition={member.currentPosition}
              email={member.email}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default ResearchTeam;
