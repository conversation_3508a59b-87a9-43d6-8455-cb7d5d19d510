"use client";

import Image from 'next/image';

interface MilestonesProps {
  className?: string;
}

const Milestones = ({ className }: MilestonesProps) => {
  return (
    <div className="relative w-full px-4 sm:px-6 md:px-8">
      {/* Background Wavy Pattern */}
      <div className="absolute left-0 right-0 top-1/2 -translate-y-1/2 z-0">
        <Image
          src="/Vector (Stroke).svg"
          alt="Wavy Background"
          width={0}
          height={0}
          sizes="100vw"
          className="w-full h-auto"
        />
      </div>
      <div
        className={`w-full max-w-sm sm:max-w-xl md:max-w-3xl lg:max-w-6xl mx-auto rounded-2xl overflow-hidden font-ramilas font-medium relative border border-custom-green bg-custom-light-green shadow-none md:shadow-milestone ${className}`}
      >
        {/* Milestones List */}
        <div className="relative z-10 text-center text-custom-green">
          {[
            "1957: Ranked among India's top 30 colleges by the Danforth Foundation, USA.",
            "1972: Recognized by UGC under sections 2(F) & 12(B).",
            '1977: Named a "Lead College" by the UGC.',
            "2002 - 2023: Accredited with top NAAC grades, achieving A++ (CGPA 3.69) in the 2023 cycle.",
            "2023: Ranked 56th in NIRF Rankings among top institutions.",
          ].map((milestone, index, arr) => (
            <div
              key={index}
              className={`p-3 sm:p-4 md:p-6 text-base sm:text-lg md:text-xl border-green-300 hover:bg-custom-green hover:text-white transition duration-300 ease-in-out ${index !== arr.length - 1 ? "border-b" : ""}`}
              style={{ borderBottom: "1px solid #00000033" }}
            >
              {milestone}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Milestones;
