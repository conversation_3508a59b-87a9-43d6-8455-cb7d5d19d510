"use client";

import Image from "next/image";
import { useEffect, useState } from "react";
import { client } from "@/lib/sanity";
import { JMC_STUDENT_FACILITIES_QUERY } from "@/sanity/lib/queries";

interface FacilityImage {
  name: string;
  imageUrl: string;
}

interface JmcStudentFacilitiesData {
  _id: string;
  studentCounsellingImages: FacilityImage[];
  cafeteriaImages: FacilityImage[];
  dayCareCentreImages: FacilityImage[];
  transportImages: FacilityImage[];
  communicationLabImages: FacilityImage[];
}

const Transport = () => {
  const [transportImages, setTransportImages] = useState<FacilityImage[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    client.fetch(JMC_STUDENT_FACILITIES_QUERY).then((data: JmcStudentFacilitiesData[]) => {
      if (data.length > 0 && data[0].transportImages?.length > 0) {
        // Filter out images with empty URLs
        const validImages = data[0].transportImages.filter(img =>
          img.imageUrl && img.imageUrl.trim() !== ""
        );
        setTransportImages(validImages);
      }
      setIsLoading(false);
    }).catch(() => {
      setIsLoading(false);
    });
  }, []);

  return (
    <section className="bg-white pb-10 px-4">
      <div className="container max-w-7xl mx-auto text-center space-y-6">
        {/* Title */}
        <h2 className="text-custom-green">Transport</h2>

        {/* Image */}
        {isLoading ? (
          <div className="rounded-2xl overflow-hidden shadow-md bg-gray-200 animate-pulse">
            <div className="w-full h-[300px] md:h-[450px] lg:h-[600px] bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-shimmer"></div>
          </div>
        ) : transportImages.length > 0 ? (
          <div className="rounded-2xl overflow-hidden shadow-md">
            <div className="relative w-full h-[300px] md:h-[450px] lg:h-[600px]">
              <Image
                src={transportImages[0].imageUrl}
                alt={transportImages[0].name}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 1024px"
              />
            </div>
          </div>
        ) : (
          <div className="text-center text-gray-500 py-8">
            <p>No transport images available at the moment.</p>
          </div>
        )}

        <p className="text-custom-new-green">
          The College provides transport facility for the women students at
          nominal rates. About 15 college buses help the students to commute to
          the college from various places in the Tiruchirappalli City. All the
          buses reach the campus at 01:50 p.m. so that students get ready to
          attend the classes at 02.00 p.m. The buses leave the campus at 06:30
          p.m.
        </p>
      </div>
    </section>
  );
};

export default Transport;
