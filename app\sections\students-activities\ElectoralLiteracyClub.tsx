"use client";

import { ExternalLink, FileText } from "lucide-react";

const ElectoralLiteracyClub = () => {
  const objectives = [
    "To promote awareness of ‘Right to vote’ among students, faculty members and community at large.",
    "To develop a culture of electoral participation and maximize ethical voting. Uphold principles like ‘Every vote counts’ and ‘No voters to be left behind’.",
    "To enable critical thinking on election rights, democracies and its processes.",
    "To facilitate voter registration for eligible members not yet registered.",
    "To educate future voters about enrollment and electoral processes like EVM/VVPAT.",
  ];

  const links = [
    { label: "Search in Electoral Roll", href: "#" },
    { label: "Guidelines For Filling Up The Application Form-6", href: "#" },
    {
      label: "Systematic Voters’ Education and Electoral Participation Program",
      href: "#",
    },
    { label: "ELC Resource guide", href: "#" },
  ];

  const members = [
    "Dr<PERSON> <PERSON><PERSON>, Assistant Professor (Maths)",
    "Dr<PERSON> <PERSON><PERSON>, Assistant Professor",
  ];

  const reports = [
    "ELECTORAL LITERACY CLUB 2023-2024",
    "ELECTORAL LITERACY CLUB 2022-2023",
    "ELECTORAL LITERACY CLUB 2021-2022",
  ];

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-custom-green">Electoral Literacy Clubs</h3>
        <p className=" text-custom-new-green leading-relaxed">
          Electoral Literacy Clubs (ELCs) are platforms to engage students and
          people in villages through interesting activities and hands-on
          experience to sensitise them with their electoral rights. Constituted
          at the behest of the Election Commission of India (ECI) in all
          educational institutions, the ELCs aim to ensure participation of
          youth & future voters in Indian democracy.
        </p>
      </div>

      <div>
        <h3 className="text-custom-green">Vision</h3>
        <p className="mt-2 text-custom-new-green">
          Electoral literacy club aims at strengthening the culture of electoral
          participation among young and future voters.
        </p>
      </div>

      <div>
        <h3 className="text-custom-green">Objectives</h3>
        <ul className="font-poppins mt-2 list-disc list-outside pl-6 text-custom-new-green space-y-1">
          {objectives.map((point, idx) => (
            <li key={idx}>{point}</li>
          ))}
        </ul>
      </div>

      <div>
        <h3 className="text-custom-green">Important Links</h3>
        <ul className=" font-poppins mt-2 space-y-2">
          {links.map((link, idx) => (
            <li key={idx}>
              <a
                href={link.href}
                className="flex items-center text-blue-600 hover:underline text-sm sm:text-base"
                target="_blank"
                rel="noopener noreferrer"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                {link.label}
              </a>
            </li>
          ))}
        </ul>
      </div>

      <div>
        <h3 className="text-custom-green">Members</h3>
        <ul className="font-poppins mt-2 list-decimal list-inside text-gray-700 space-y-1">
          {members.map((member, idx) => (
            <li key={idx}>{member}</li>
          ))}
        </ul>
      </div>

      <div>
        <h3 className="text-custom-green">Reports</h3>
        <ul className="font-poppins mt-2 space-y-2">
          {reports.map((report, idx) => (
            <li
              key={idx}
              className="flex items-center text-red-600 text-sm sm:text-base"
            >
              <span className="mr-2 text-xs bg-red-600 text-white px-2 py-0.5 rounded-full font-bold">
                NEW
              </span>
              <FileText className="w-4 h-4 mr-2" />
              {report}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default ElectoralLiteracyClub;
