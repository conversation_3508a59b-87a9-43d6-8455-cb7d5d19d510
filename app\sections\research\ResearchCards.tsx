import React from "react";
import Image from "next/image";

const ResearchCards = () => {
  // Research cards data
  const researchCards = [
    {
      type: "image", // Full image card
      image: "/homepage/otrbgimg1.png",
      title: "Biotechnology Research",
      description: "Our cutting-edge biotechnology research focuses on sustainable solutions for agricultural challenges, with emphasis on drought-resistant crop varieties and eco-friendly pest management systems."
    },
    {
      type: "content", // Content card
      image: "/homepage/otrbgimg3.png",
      title: "Environmental Science",
      description: "Our environmental research team is pioneering studies on local ecosystem preservation, water quality improvement, and developing innovative approaches to reduce carbon footprint on campus."
    },
    {
      type: "content", // Content card
      image: "/homepage/otrbgimg2.png",
      title: "Computer Science Innovation",
      description: "Our computer science department is developing advanced machine learning algorithms to address real-world problems, including healthcare diagnostics and educational technology solutions."
    },
    {
      type: "content", // Content card
      image: "/homepage/otrbgimg3.png",
      title: "Social Science Research",
      description: "Our social science research focuses on community development, cultural preservation, and addressing socioeconomic challenges through evidence-based policy recommendations."
    }
  ];

  return (
    <section className="py-12">
      <div className="container mx-auto max-w-7xl">
        <h2 className="text-3xl font-bold text-center text-custom-green mb-8">
          Our Research Areas
        </h2>
        
        <p className="text-gray-700 text-center max-w-6xl mx-auto mb-12">
          At Jamal Mohamed College, our research spans across multiple disciplines, contributing to knowledge advancement and societal development. Our faculty and students collaborate on innovative projects that address real-world challenges.
        </p>
        
        {/* First row - One full image card and one content card */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          {/* Full image card */}
          <div className="relative h-[450px] rounded-3xl overflow-hidden shadow-lg">
            <Image 
              src={researchCards[0].image} 
              alt={researchCards[0].title}
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black via-black/20 to-transparent"></div>
            <div className="absolute bottom-0 left-0 w-full p-6 text-white">
              <h3 className="text-2xl font-semibold mb-4">{researchCards[0].title}</h3>
              <p className="mb-4">{researchCards[0].description}</p>
              <button className="bg-white text-custom-green px-6 py-2 rounded-full font-bold">
                Learn More
              </button>
            </div>
          </div>
          
          {/* Content card */}
          <div className="bg-white p-8 rounded-3xl shadow-lg h-[450px] flex flex-col">
            <h3 className="text-2xl font-semibold text-custom-green mb-4">{researchCards[1].title}</h3>
            <p className="text-gray-700 mb-6 flex-grow">{researchCards[1].description}</p>
            <div className="relative h-48 rounded-xl overflow-hidden mb-6">
              <Image 
                src={researchCards[1].image} 
                alt={researchCards[1].title}
                fill
                className="object-cover"
              />
            </div>
            <button className="bg-custom-green text-white px-6 py-2 rounded-full font-bold self-start">
              Learn More
            </button>
          </div>
        </div>
        
        {/* Second row - Two content cards with slightly smaller height */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {researchCards.slice(2).map((card, index) => (
            <div key={index} className="bg-white p-8 rounded-3xl shadow-lg h-[400px] flex flex-col">
              <h3 className="text-2xl font-semibold text-custom-green mb-4">{card.title}</h3>
              <p className="text-gray-700 mb-6 flex-grow">{card.description}</p>
              <div className="relative h-40 rounded-xl overflow-hidden mb-6">
                <Image 
                  src={card.image} 
                  alt={card.title}
                  fill
                  className="object-cover"
                />
              </div>
              <button className="bg-custom-green text-white px-6 py-2 rounded-full font-bold self-start">
                Learn More
              </button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ResearchCards;
