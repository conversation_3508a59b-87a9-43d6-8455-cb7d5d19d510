"use client";

import Navbar from "@/components/Navbar";
import Footer from "../sections/Footer";
import Link from "next/link";
import { usePathname } from 'next/navigation';
import { menuItems } from "@/components/Navbar";
import ContactInfoCard from '../components/ContactInfoCard';

const ContactUsPage = () => {
  const pathname = usePathname();
  const currentMainSection = menuItems.find(item => item.href === pathname);
  const currentSubSection = currentMainSection?.subSections?.find(
    sub => pathname + location.hash === sub.href
  );


  return (
    // This outer fragment doesn't need flex, the body handles it
    <>
      <Navbar fixed={true} border={true} />
      <header
        className="relative pt-40 pb-16 px-4 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        {/* ... header content ... */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex flex-col md:flex-row md:justify-between md:items-center">
          <h1 className="font-ramilas text-center md:text-left mb-4 md:mb-0">
            {currentMainSection?.title || "Contact Us"}
          </h1>
          {/* Dynamic Breadcrumbs */}
          <ul className="font-poppins flex flex-row justify-center md:justify-end gap-8 md:gap-8 md:pr-16">
             {/* ... breadcrumb list items ... */}
             <li className="list-disc">
              <Link href="/">Home</Link>
            </li>
            {currentMainSection && (
              <li className="list-disc">
                {currentSubSection ? (
                  <Link href={currentMainSection.href}>{currentMainSection.title}</Link>
                ) : (
                  currentMainSection.title
                )}
              </li>
            )}
            {currentSubSection && (
              <li className="list-disc">
                {currentSubSection.title}
              </li>
            )}
          </ul>
        </div>
      </header>

      {/* Add flex-grow to the main element */}
      <main className="flex-grow py-10 md:py-20 px-4 bg-gray-50">
        <div className="container max-w-7xl mx-auto">
          <ContactInfoCard />

          {/* You can add other sections like a contact form below if needed */}
          {/* <div className="mt-16"> */}
          {/*   <h2 className="text-3xl font-bold text-center mb-8">Send us a Message</h2> */}
          {/*   <YourContactFormComponent /> */}
          {/* </div> */}
        </div>
      </main>
      <Footer />
    </>
  );
};

export default ContactUsPage;