"use client";

import Navbar from "@/components/Navbar";
import Footer from "../sections/Footer";
import Link from "next/link";
import DigitalLibrary from "../components/DigitalLibrary";
import PlacementPolicy from "../components/PlacementPolicy";
import GlobalAlumniSearchEngine from "../components/GlobalAlumniSearchEngine";
import PlacementTeam from "../components/PlacementTeam";
import CommunicationLab from "../components/CommunicationLab";
import PlacementDetails from "../components/PlacementDetails";
import OurRecruiters from "../components/OurRecruiters";
import Gallery from "../components/Gallery";
import CTAComponent from "../components/CTAComponent";

const PlacementPage = () => {
  return (
    <>
      <Navbar fixed={true} border={true} />
      <header
        className="relative pt-40 pb-16 px-4 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex flex-col md:flex-row md:justify-between md:items-center">
          <h1 className="font-ramilas text-center md:text-left mb-4 md:mb-0">
            Placement
          </h1>
          <ul className="font-poppins flex flex-row justify-center md:justify-end gap-8 md:gap-8 md:pr-16">
            <li className="list-disc">
              <Link href="/">Home</Link>
            </li>
            <li className="list-disc">Placement</li>
          </ul>
        </div>
      </header>

      <main>
        {/* Digital Library Component */}
        <DigitalLibrary />

        {/* Placement Policy Component */}
        <PlacementPolicy />

        {/* Global Alumni Search Engine Component */}
        <GlobalAlumniSearchEngine />

        {/* Placement Team Component */}
        <PlacementTeam />

        {/* Communication Lab Component */}
        <CommunicationLab />

        {/* Placement Details Component */}
        <PlacementDetails />

        {/* Our Recruiters Component */}
        <OurRecruiters />

        {/* Gallery Component */}
        <Gallery />

        {/* CTA Component */}
        <section className="bg-white py-10 px-4">
          <div className="container max-w-7xl mx-auto">
            <CTAComponent />
          </div>
        </section>
      </main>

      <Footer />
    </>
  );
};

export default PlacementPage;
