import { defineType, defineField } from "sanity";
import React from 'react'; // Import React

export default defineType({
  name: "faculty",
  title: "Faculties",
  type: "document",
  fields: [
    defineField({
      name: "name",
      title: "Full Name",
      type: "string",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "qualifications",
      title: "Qualifications",
      type: "string",
    }),
    define<PERSON>ield({
      name: "designation",
      title: "Designation",
      type: "string",
    }),
    defineField({
      name: "email",
      title: "Email",
      type: "string",
      validation: (Rule) => Rule.email(),
    }),
    defineField({
      name: "profileUrl",
      title: "Profile Download URL",
      type: "url",
      description: "Link to download the full profile PDF or page",
    }),
    defineField({
      name: "image",
      title: "Photo URL",
      type: "url",
      description: "URL of the faculty photo (e.g., from S3)",
    }),
    defineField({
      name: "category",
      title: "Category",
      type: "string",
      options: {
        list: [
          { title: "MEN-AIDED", value: "MEN-AIDED" },
          { title: "MEN-UNAIDED", value: "MEN-UNAIDED" },
          { title: "WOMEN-UNAIDED", value: "WOMEN-UNAIDED" },
        ],
        layout: "dropdown",
      },
    }),
  ],
  preview: {
    select: {
      title: 'name',
      subtitle: 'designation',
      media: 'image', // Select the image URL field
    },
    // Re-add prepare function to handle URL media
    prepare(selection: { title: string; subtitle: string; media?: string }) {
      const { title, subtitle, media } = selection;
      return {
        title: title,
        subtitle: subtitle,
        // Render an img element if the media URL exists
        media: media ? React.createElement('img', { src: media, alt: title, style: { objectFit: 'cover', height: '100%', width: '100%' } }) : undefined
      };
    }
  },
});
