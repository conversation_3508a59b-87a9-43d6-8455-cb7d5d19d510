export const coursesQuery = `*[_type == "courses"]{
  _id,
  departmentName,
  "slug": slug.current,
  description,
  studyLevel,
  studyPattern,
  fieldOfStudy,
  tags
}`;

export const getNewslettersQuery = `
  *[_type == "newsletters"]{
    _id,
    name,
    category,
    "url": pdfUrl // Changed from pdfFile.asset->url to pdfUrl
  }
`;

export const clubsQuery = `*[_type == "club"] | order(_createdAt asc){
  _id,
  name,
  description,
  image, // Changed from "image": image.asset->url
  objectives,
  members,
  buttonLabel
}`;

export const HOSTEL_INFRA_QUERY = `
  *[_type == "hostelinfrastructure"]{
    name,
    imageUrl,
    alt
  }
`;

export const HOSTEL_FACILITIES_QUERY = `
  *[_type == "hostelfacilities"]{
    name,
    imageUrl,
    alt
  }
`;

export const studentEnrichmentProgrammesQuery = `
  *[_type == "studentenrichmentprogrammes"]{
    _id,
    name,
    imageUrl,
    description
  }
`;

export const generalInformationQuery = `
  *[_type == "generalinformation"]{
    _id,
    name,
    image{
      asset->{
        url
      },
    }
  }`;

export const getAccreditation = `*[_type == "accreditation"]{
  name,
  imageUrl,
  category
}`;

export const HANDBOOKS_QUERY = `*[_type == "handbook"] | order(date desc) {
  _id,
  volume,
  edition,
  date,
  pdfUrl
}`;

export const ANNUAL_REPORTS_QUERY = `*[_type == "annualReportOfExamination"] | order(period desc) {
  _id,
  period,
  pdfUrl
}`;

export const CALENDAR_QUERY = `*[_type == "calendar"] | order(period desc) {
  _id,
  period,
  pdfUrl
}`;

export const DEGREE_EXAM_SCHEDULE_QUERY = `*[_type == "degreeExamSchedule"] | order(date desc){
  _id,
  title,
  date,
  pdfUrl
}`;
