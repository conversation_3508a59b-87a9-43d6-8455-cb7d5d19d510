"use client";

import { useState, useEffect, useRef } from "react";
import { ChevronRight, ChevronLeft } from "lucide-react";

interface SidebarProps {
  subsections: { title: string; href: string }[];
}

const Sidebar = ({ subsections }: SidebarProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeSection, setActiveSection] = useState<string | null>(
    subsections[0]?.href || null
  );
  const toggleButtonRef = useRef<HTMLButtonElement>(null);
  const sidebarRef = useRef<HTMLDivElement>(null);

  const handleClick = (href: string) => {
    setActiveSection(href);
    const hashIndex = href.indexOf("#");
    if (hashIndex !== -1) {
      const sectionId = href.substring(hashIndex + 1);
      const sectionEl = document.getElementById(sectionId);
      const navbarHeight = document.getElementById('main-navbar')?.offsetHeight || 0;
      
      if (sectionEl) {
        // Calculate the position to scroll to, accounting for the fixed navbar
        const elementPosition = sectionEl.getBoundingClientRect().top + window.pageYOffset;
        const offsetPosition = elementPosition - navbarHeight;
        
        window.scrollTo({
          top: offsetPosition,
          behavior: "smooth"
        });
      }
    }
  
    // Removed the conditional close based on window width
    setIsOpen(false); // Now sidebar will close automatically on link click
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isOpen &&
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target as Node) &&
        toggleButtonRef.current &&
        !toggleButtonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        setIsOpen(false);
      }
    };

    document.addEventListener("keydown", handleEscKey);
    return () => {
      document.removeEventListener("keydown", handleEscKey);
    };
  }, [isOpen]);

  if (!subsections || subsections.length === 0) return null;

  const [firstSection, ...otherSections] = subsections;

  return (
    <>
      {/* Toggle button */}
      <button
        ref={toggleButtonRef}
        className={`fixed top-80 max-[375px]:top-80 md:top-72 z-40 bg-white border-t border-r border-b rounded-tr-lg rounded-br-lg py-4 px-2 shadow-md hover:bg-gray-100 transition duration-300 border-custom-green ${ // Default top-1/2, lg:top-24
          isOpen ? "opacity-0 pointer-events-none" : "opacity-100"
        }`}
        onClick={() => setIsOpen(true)}
        aria-label="Open navigation sidebar"
        style={{ left: 0 }}
      >
        <ChevronRight size={20} className="text-custom-green" />
      </button>

      {/* Sidebar overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-30 bg-black bg-opacity-30 transition-opacity duration-300"
          onClick={() => setIsOpen(false)}
          aria-hidden="true"
        />
      )}

      {/* Sidebar content */}
      <div
        ref={sidebarRef}
        className={`fixed max-[375px]:top-44 top-80 md:top-72 lg:top-60 z-50 w-72 bg-white shadow-lg p-5 rounded-lg border border-custom-green transition-all duration-300 transform ${ // Added max-[375px]:top-44
          isOpen
            ? "translate-x-0 opacity-100"
            : "-translate-x-full opacity-0 pointer-events-none"
        }`}
        style={{ left: isOpen ? "40px" : "0" }}
        aria-hidden={!isOpen}
      >
        {/* Map over all subsections */}
        <ul className="space-y-2 text-sm">
          {subsections.map((section, idx) => (
            <li
              key={idx}
              className="border-b border-gray-100 pb-2 last:border-0"
            >
              {/* Flex container for title and conditional close button */}
              <div className="flex justify-between items-center">
                <button
                  onClick={() => handleClick(section.href)}
                  className={`flex-grow text-left transition font-poppins py-1 ${ // Use flex-grow
                    activeSection === section.href
                      ? "text-lg font-bold text-custom-green"
                      : "text-gray-700 hover:text-black"
                  }`}
                >
                  {section.title}
                </button>
                {/* Conditionally render close button next to active section */}
                {activeSection === section.href && (
                  <button
                    onClick={() => setIsOpen(false)}
                    className="p-1.5 rounded-full hover:bg-gray-100 transition ml-2" // Added margin-left
                    aria-label="Close Sidebar"
                  >
                    <ChevronLeft
                      size={20}
                      className="text-gray-600 hover:text-custom-green"
                    />
                  </button>
                )}
              </div>
            </li>
          ))}
        </ul>
      </div>
    </>
  );
};

export default Sidebar;
