import React from "react";
import Image from "next/image";

interface OurRecruitersProps {
  recruiters?: string[];
}

const OurRecruiters: React.FC<OurRecruitersProps> = ({ recruiters = [] }) => {
  // Only show section if we have recruiters
  if (!recruiters || recruiters.length === 0) {
    return null;
  }


  return (
    <section className="py-16 bg-[#D1D9D1]">
      {/* Title and Description with padding */}
      <div className="px-4 md:px-8 text-center mb-12">
        {/* Title */}
        <h2 className="text-3xl md:text-5xl font-ramilas text-custom-green mb-6">
          Our Recruiters
        </h2>
        
        {/* Description */}
        <p className="text-gray-700 text-base md:text-lg leading-relaxed px-8">
          At Jamal Mohamed College, we are deeply committed to the career success of our students. Our dedicated placement cell 
          works tirelessly to ensure that students are well-prepared for the professional world.
        </p>
      </div>

      {/* Recruiter Logos Grid */}
      <div className="px-4">
        <div className={`grid ${recruiters.length === 1 ? 'grid-cols-1 max-w-md mx-auto' : 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4'} gap-6 justify-items-center`}>
          {recruiters.map((logoUrl, index) => (
            <div 
              key={index}
              className="w-[150px] h-[80px] flex items-center justify-center"
            >
              <div className="relative w-full h-full bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                <Image
                  src={logoUrl}
                  alt="Recruiter Logo"
                  fill
                  className="object-contain"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default OurRecruiters;
