import React from "react";

// Define interfaces for our data structure
export interface PatentEntry {
  serialNo: number;
  name: string;
  applicationNumber: string;
  title: string;
  dateOfGrantOrFiling: string;
}

export interface PatentTableData {
  title: string;
  entries: PatentEntry[];
}

interface PatentTableProps {
  patentData: PatentTableData[];
}

const PatentTable: React.FC<PatentTableProps> = ({ patentData }) => {
  return (
    <div className=" bg-[#D1D9D1]">
      {patentData.map((data, tableIndex) => (
        <div key={`patent-table-${tableIndex}`} >
          <h3 className="text-lg sm:text-xl font-bold text-[#002E00] pt-6 sm:pt-8 px-4 sm:px-8">
            {data.title}
          </h3>

          <div className="rounded-lg overflow-hidden">
            {/* Add overflow container for mobile responsiveness */}
            <div className="overflow-x-auto">
              <div className="min-w-[768px] max-w-full"> {/* Minimum width to prevent squishing on mobile */}
                {/* Header with margin */}
                <div className="mx-4 my-4">
                  <div className="bg-[#002E00] text-white rounded-full">
                    <table className="w-full">
                      <thead>
                        <tr>
                          <th className="py-4 px-6 text-center font-medium w-[4.17%] text-xs sm:text-sm md:text-base">S.NO.</th>
                          <th className="py-4 px-6 text-left font-medium w-[25%] text-xs sm:text-sm md:text-base whitespace-normal break-words">NAME</th>
                          <th className="py-4 px-6 text-center font-medium w-[20%] md:w-[30%] lg:w-[18%] text-xs sm:text-sm md:text-base whitespace-normal break-words">
                            <span className="md:hidden">APP. NO.</span>
                            <span className="hidden md:inline">APPLICATION NUMBER</span>
                          </th>
                          <th className="py-4 px-6 text-left font-medium w-[37.5%] text-xs sm:text-sm md:text-base whitespace-normal break-words hyphens-auto" style={{ wordBreak: 'break-word' }}>TITLE</th>
                          <th className="py-4 px-6 text-center font-medium w-[16.67%] md:w-[25%] text-xs sm:text-sm md:text-base whitespace-normal break-words">
                            <span className="md:hidden">DATE</span>
                            <span className="hidden md:inline">
                              {data.title.includes("AWARDED") ? "DATE OF GRANT" : "DATE OF FILING"}
                            </span>
                          </th>
                        </tr>
                      </thead>
                    </table>
                  </div>
                </div>

                {/* Table content */}
                <table className="min-w-full">
                  <tbody>
                    {data.entries.map((entry) => (
                      <tr
                        key={`patent-${data.title}-${entry.serialNo}`}
                        className="border-b border-[#555555]"
                      >
                        <td className="py-3 px-10  lg:px-12 text-center text-[#555555] w-[4.17%]  text-xs sm:text-sm md:text-base">{entry.serialNo}</td>
                        <td className="py-3 px-6 text-left text-[#555555] w-[25%] text-xs sm:text-sm md:text-base whitespace-normal break-words">{entry.name}</td>
                        <td className="py-3 px-6 text-center text-[#555555] w-[16.67%] text-xs sm:text-sm md:text-base whitespace-normal break-words">{entry.applicationNumber}</td>
                        <td className="py-3 px-6 text-left text-[#555555] w-[37.5%] text-xs sm:text-sm md:text-base whitespace-normal break-words hyphens-auto" style={{ wordBreak: 'break-word' }}>{entry.title}</td>
                        <td className="py-3 px-6 text-center text-[#555555] w-[16.67%] text-xs sm:text-sm md:text-base whitespace-normal break-words">{entry.dateOfGrantOrFiling}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default PatentTable;
