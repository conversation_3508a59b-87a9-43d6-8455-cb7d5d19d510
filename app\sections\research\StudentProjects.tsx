import React from "react";
import Image from "next/image";
import Link from "next/link";

interface ProjectButtonProps {
  title: string;
  year: string;
  href: string;
}

const ProjectButton: React.FC<ProjectButtonProps> = ({ title, year, href }) => {
  return (
    <Link href={href} className="block">
      <div className="bg-custom-green text-white p-4 text-center rounded-md hover:bg-custom-green-dark transition duration-300">
        <p className="font-semibold">
          {title} <br />
          {year}
        </p>
      </div>
    </Link>
  );
};

const StudentProjects = () => {
  return (
    <section className="pt-16">
      <div className="container mx-auto max-w-7xl px-4">
        {/* Full-width image - responsive dimensions */}
        <Image
          src="/research/team/student-projects.png"
          alt="Student Projects"
          width={1200}
          height={600}
          className="w-full h-auto max-h-[300px] sm:max-h-[400px] md:max-h-[500px] lg:max-h-[600px] rounded-xl mb-4 object-cover"
          priority
        />

        {/* Caption/Subheading */}
        <p className="text-center text-gray-700 mb-8 max-w-4xl mx-auto">
          Secretary & Correspondent of Jamal Mohamed College distributing the Cheques to the
          students who have selected for TNSCST Student Project 2023.
        </p>

        {/* Buttons Section */}
        <div className="border border-custom-green rounded-lg p-6 max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ProjectButton
              title="Student Innovative Research And Startup Proposal"
              year="(2022-2023)"
              href="/research/projects/2022-2023"
            />
            <ProjectButton
              title="Student Innovative Research And Startup Proposal"
              year="(2023-2024)"
              href="/research/projects/2023-2024"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default StudentProjects;
