"use client";

import Footer from "@/app/sections/Footer";
import HostelAdministration from "@/app/sections/students-facilities/HostelAdministration";
import HostelAdmission from "@/app/sections/students-facilities/HostelAdmission";
import HostelFacilities from "@/app/sections/students-facilities/HostelFacilities";
import HostelForMen from "@/app/sections/students-facilities/HostelForMen";
import HostelInfrastructure from "@/app/sections/students-facilities/HostelInfrastructure";
import HostelSportsActivities from "@/app/sections/students-facilities/HostelSportsActivities";
import StudentEnrichmentProgramme from "@/app/sections/students-facilities/StudentEnrichmentProgramme";
import TheWomenHostel from "@/app/sections/students-facilities/TheWomenHostel";
import Navbar, { menuItems } from "@/components/Navbar";
import Link from "next/link";
import { wardens } from "@/app/helper/helper";
import {
  mensHostelRules,
  mensHostelMessTimings,
  mensHostelAdmissionRules,
  womensHostelAdmissionRules,
  womenHostelWardens,
  hostelAmenities,
  dailyLifeInTheHostel,
  womensHostelMessTimings,
} from "@/app/helper/helper";
import WomensHostelAdmission from "@/app/sections/students-facilities/WomensHostelAdmission";
import GeneralInformation from "@/app/sections/students-facilities/GeneralInformation";
import Mosque from "@/app/sections/students-facilities/Mosque";
import MenPrayerHall from "@/app/sections/students-facilities/MenPrayerHall";
import WomenPrayerHall from "@/app/sections/students-facilities/WomenPrayerHall";
import StudentCounsellingCentre from "@/app/sections/students-facilities/StudentCounsellingCentre";
import Cafeteria from "@/app/sections/students-facilities/Cafeteria";
import CivilServicesCoachingCentre from "@/app/sections/students-facilities/CivilServicesCoachingCentre";
import DayCareCentre from "@/app/sections/students-facilities/DayCareCentre";
import Transport from "@/app/sections/students-facilities/Transport";
import CommunicationLab from "@/app/sections/students-facilities/CommunicationLab";
import { Sidebar } from "lucide-react";
import { usePathname } from "next/navigation";
import Image from "next/image";
import TableCommon from "@/app/components/TableCommon";
import Stats from "@/app/components/Stats";


const page = () => {

  const pathname = usePathname();
  // const currentSection = menuItems.find(
  //   (item) => pathname.startsWith(item.href) && item?.subSections
  // );

  const libraryOverview = {
    "departmentTitle": "General Library",
    "description": "The college has a modern general library with a robust collection of over 2,37,779 volumes of books in 1,37,616 titles on many disciplines covering all major fields of Science, Humanities, and Management. Besides the general library, each department has its own library, containing a major portion of the collection of books. The Library subscribes to about 162 periodicals which include national & international journals, and magazines subscribed by various departments. Many English, Tamil and Malayalam daily newspapers are also subscribed.",
    "image": "https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Home/Hero+Section/01.jpg",
    "mission": [
        "To provide well-equipped and functional spaces for students, staff and researchers with resources to achieve their highest academic potential.",
        "To facilitate the learners with a well-organized collection of books, journals, periodicals, electronic resources which meet the needs of learners in order to support their intellectual pursuit.",
        "To provide effective, responsive and innovative services as the storehouse of knowledge."
    ],
    "vision": [
        "To offer comprehensive resources and services in support of teaching, learning and research needs of the academic community."
    ]
  }

  const programmes = [
    {
      name: "Class Room",
      sections: 10,
    },
    {
      name: "Master Of Arts (Aided)",
      sections: 1,
    },
    {
      name: "Bachelor of Arts (SF Women)",
      sections: 1,
    },
    {
      name: "Master of Arts (SF Women)",
      sections: 2,
    },
  ];

  const libraryTable = [
    {
      s_no: "1.",
      particulars: "No. of Volumes in the General Library (Including 3000 Vol. Under the head of Book Bank)",
      quantity: "78,067",
    },
    {
      s_no: "2.",
      particulars: "No. of Titles in the General Library",
      quantity: "41,353",
    },
    {
      s_no: "3.",
      particulars: "No. of Volumes in the Department Libraries",
      quantity: "1,59,712",
    },
    {
      s_no: "4.",
      particulars: "No. of Titles in the Department Libraries",
      quantity: "96,263",
    },
    {
      s_no: "5.",
      particulars: "No. of National Journals",
      quantity: "85",
    },
  ];

  const dumlibraryTable = {
    header: [
      { head: "Name", text_align: "text-left" },
      { head: "Course", text_align: "text-center" },
      { head: "Year", text_align: "text-right" }
    ],
    line: [
      ["Alice", "B.Sc. Computer Science", "2025"],
      ["Bob", "B.A. English", "2024"],
      ["Charlie", "B.Com", "2026"]
    ]
  };

  const libraryRules = [
    "All the students of the College are the members of the library.",
    "All the students can access the books in the Open Access System.",
    "Students are entitled, subject to the rules herein mentioned, to keep a book for a fortnight from the date of issue.",
    "If the book is not returned on or before the due date, a fine of Re.1 per day including holidays will be levied.",
    "The student will not be allowed to use the library till the fine is paid and the book returned.",
    "Books due on holidays may be returned without fine on the working day immediately following the holidays.",
    "Books borrowed for the vacation should be returned within three working days after the reopening of the college. Otherwise, the usual fine will be collected.",
    "No one is allowed to sub-lend the books taken out by him.",
    "On receiving a book, students must examine and report to the Librarian any damage found therein. Otherwise, they will be held responsible for any damage that may be detected afterwards.",
    "A book returned to the Library should contain a return slip showing the roll number of the student returning it and the catalogue number of the book.",
    "Absence from the college will not ordinarily be admitted as an excuse for delay in the return of the books.",
    "All payments including fine towards the library shall be paid at the college office and the receipt must be shown to the librarian.",
    "The Librarian may recall any book at any time.",
    "Strict silence should be observed in the reading room.",
    "Without previous notice, students may call for books which are marked 'Reference' or a 'Periodical' for consultation, which will not be lent out.",
    "All foreign and Indian magazines and newspapers will be displayed for perusal in the reading room.",
    "There is no fee for our alumni to take the books.",
    "A nominal fee of Rs. 200/- is collected from the general public to take the books for reference."
  ];

  const EResources = [
    {
      heading: "INFLIBNET – N-LIST",
      content: "Interface URL : INFLIBNET Click Here to Login and Apply Your e-mail ID As User ID & Password",
      link: "",
    },
    {
      heading: "DELNET",
      content: 'Interface URL : DELNET Click on to "New Discovery Portal"',
      link: "",
    },
    {
      heading: "PROQUEST",
      content: "Access Link : PROQUEST",
      link: "",
    },
    {
      heading: "NPTEL",
      content: "Access Link : NPTEL",
      link: "",
    },
    {
      heading: "National Digital Library of India",
      content: "Access Link : NDLI",
      link: "",
    },
    {
      heading: "Open Access Educational Resources",
      content: "Access Link :  e-PG Pathshala",
      link: "",
    },
    {
      heading: "Open Access Educational Resources",
      content: "Access Link : Vidya-Mitra Portal",
      link: "",
    },
    {
      heading: "Open Access Educational Resources",
      content: "Access Link :  SWAYAM MOOC Courses",
      link: "",
    },
    {
      heading: "OPAC",
      content: "Access Link : OPAC",
      link: "",
    },
  ];
  

  return (
    <>
      <Navbar fixed={false} border={false} />
      <header
        className="relative mt-4 px-4 py-8 md:py-16 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex justify-between items-center">
          <h1 className="font-ramilas">Student's Corner</h1>
          <ul className="font-poppins flex flex-col md:flex-row md:gap-8 md:pr-16">
            <li className="list-disc">
              <Link href="/">Home</Link>
            </li>
            <li className="list-disc">Library</li>
          </ul>
        </div>
      </header>
      <main>
      {/* <TableCommon table={dumlibraryTable} /> */}
      {/* {currentSection && (
          <Sidebar subsections={currentSection.subSections || []} />
        )} */}
      <div className="relative flex flex-col items-center justify-center text-center py-10 px-4 bg-[#D1D9D1] rounded-bl-3xl border-b-2 border-custom-green shadow-stacked-cards">
        <h3 className="text-4xl sm:text-2xl md:text-5xl font-bold text-custom-green mb-4">
            Digital library
        </h3>
        <p className="text-custom-new-green font-poppins font-normal max-w-6xl">
         Admissions Open 2025-2026 | Jamal Mohamed College (Autonomous), Tiruchirappalli Accredited with NAAC A++ (CGPA 3.69/4.0), Jamal Mohamed College offers a wide range of UG, PG, MBA, and MCA programmes across Arts, Science, Commerce, Management, and Computer Science streams. Benefit from top-notch academics, scholarships, excellent infrastructure, and strong placement support.
        </p>
        <div className="mt-6 space-x-4">
          <Link href="/aboutUs" className=" hover:bg-custom-green text-custom-green hover:text-white px-8 py-3 rounded-full w-fit text- font-ramilas font-bold border-2 border-custom-green transition-colors duration-300">  Know More</Link>
        </div>

      </div> 
      <section className="px-8 py-10 mt-20 bg-white">
        <div className="container max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 items-stretch">
            {/* Image */}
            <div className="h-[400px] md:h-[500px] border-2 border-transparent hover:border-custom-green transition rounded-2xl">
              {libraryOverview?.image ? ( // Changed condition to check for libraryOverview.image directly
                <Image
                  src={libraryOverview.image} // Changed src to use libraryOverview.image directly
                  alt="library Overview Image"
                  width={700}
                  height={600}
                  className="rounded-2xl shadow-lg object-cover w-full h-full"
                />
              ) : (
                <div className="w-full h-full bg-gray-100 rounded-2xl flex items-center justify-center">
                  <span className="text-gray-400">No image available</span>
                </div>
              )}
            </div>
  
            {/* Description */}
            <div className="bg-white rounded-2xl p-6 md:p-12 shadow-md h-full flex flex-col border-2 border-transparent hover:border-custom-green transition">
              <div>
                <h2 className="text-3xl font-semibold text-green-900 mb-4">
                  {libraryOverview?.departmentTitle}
                </h2>
                <p className="text-custom-new-green text-justify">
                  {libraryOverview?.description || "No description available"}
                </p>
              </div>
            </div>
          </div>
  
          <div className="mt-10 grid grid-cols-1 md:grid-cols-2 gap-6 items-stretch">
            {/* Mission */}
            <div className="bg-white p-6 md:p-12 rounded-2xl shadow-md h-full flex flex-col border-2 border-transparent hover:border-custom-green transition">
              <h3 className="text-2xl font-semibold text-custom-green mb-3">
                Mission
              </h3>
              <ul className="font-poppins list-disc pl-5 text-custom-new-green space-y-2 flex-grow">
                {libraryOverview?.mission?.map((item: string, index: number) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            </div>
  
            {/* Vision */}
            <div className="bg-white p-6 md:p-12 rounded-2xl shadow-md h-full flex flex-col border-2 border-transparent hover:border-custom-green transition">
              <h3 className="text-2xl font-semibold text-custom-green mb-3">
                Vision
              </h3>
              <ul className="font-poppins list-disc pl-5 text-custom-new-green space-y-2 flex-grow">
                {libraryOverview?.vision?.map((item: string, index: number) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      <section className=" pb-10 px-4 bg-white">
        <div className="container max-w-7xl mx-auto space-y-6 text-center">
          <h2 className="text-custom-green">Library Information</h2>
          <p className="text-custom-new-green">
          Our state-of-the-art infrastructure provides a modern, dynamic environment for academic and personal growth. Discover well-equipped facilities designed to support excellence in learning and research.
          </p>
          <div className="w-full max-w-7xl mx-auto lg:px-8 lg:pr-12">
            <div
              className="overflow-x-auto rounded-2xl shadow-lg border border-custom-green programmes-table-shadow">
              <table className="w-full table-auto text-left font-ramilas font-bold text-xs sm:text-base md:text-lg lg:text-xl">
                <thead>
                  <tr className="bg-custom-green text-white rounded-t-2xl">
                    <th className="px-6 md:px-8 py-4 text-left">
                      S. No
                    </th>
                    <th className="px-6 md:px-8 py-4 text-center">
                      Particulars
                    </th>
                    <th className="px-6 md:px-8 py-4 text-right">Quantity</th>
                  </tr>
                </thead>
                <tbody className="bg-custom-light-green text-custom-green">
                  {libraryTable?.map((item: any, index: any) => (
                    <tr
                      key={index}
                      className={
                        index !== libraryTable.length - 1
                          ? "border-b border-gray-300"
                          : ""
                      }
                    >
                      <td className="px-6 md:px-8 py-4">{item.s_no}</td>
                      <td className="px-6 md:px-8 py-4 text-left">
                        {item.particulars}
                      </td>
                      <td className="px-6 md:px-8 py-4 text-right">
                        {item.quantity}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              </div>
            </div>
        </div>
      </section>

      <section className="bg-white py-10 px-4">
      <div className="container max-w-7xl mx-auto space-y-6">
        <div className="rounded-2xl border border-gray-200 p-4 md:p-10 lg:px-16 lg:py-10 shadow-sm bg-white space-y-6">
          <div className="space-y-4">
            <h3 className="text-custom-green text-fluid-base font-poppins font-semibold">
              Rules & Regulations
            </h3>
            <ul className="list-disc list-outside pl-8 space-y-2 text-custom-new-green font-poppins">
              {libraryRules?.map((rule: any, idx: any) => (
                <li key={idx}>{rule}</li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </section>

     <section
      className="relative text-white bg-cover bg-center px-4 py-8"
      style={{ backgroundImage: "url('/jamal_college.jpeg')" }}
    >
      {/* Overlay */}
      <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

      <div className="relative p-4 container mx-auto z-10 max-w-6xl lg:max-w-7xl">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div>
            <h2 className="mb-4">Librarian</h2>
            <p className="max-w-3xl mb-8 md:mb-0 font-poppins">
            Founded in 1951, Jamal Mohamed College, Tiruchirappalli, began as an affiliated institution of the University of Madras and later joined Bharathidasan University in 1982. Established as a minority institution, its mission has been to provide quality education to 
            the underprivileged.
            </p>
          </div>
          {/* Images Section */}
          <div className="flex flex-row justify-center gap-8">
            <div className="flex flex-col items-center">
              <Image
                src="https://jamal-mohamed-college.s3.ap-south-2.amazonaws.com/Static/Library/librarian.jpg"
                alt="Dr. A. ABUL HUSSAIN"
                width={160}
                height={200}
                className="rounded-lg shadow-lg object-cover"
              />
              <p className="mt-2 text-sm md:text-base text-center whitespace-pre-line">
              Dr. A. ABUL HUSSAIN,
              {"\n"}M.A., MLIS., M.Phil., Ph.D, NET
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section className="p-10 px-4 bg-white">
      <div className="container max-w-7xl mx-auto space-y-6 text-center">
        <h2 className="text-custom-green">Books / Magazines & Periodicals</h2>
        <p className="text-custom-new-green">
        Our state-of-the-art infrastructure provides a modern, dynamic environment for academic and personal growth. Discover well-equipped facilities designed to support excellence in learning and research.
        </p>
        <div className="w-full max-w-7xl mx-auto lg:px-8 lg:pr-12">
          <div
            className="overflow-x-auto rounded-2xl shadow-lg border border-custom-green programmes-table-shadow">
            <table className="w-full table-auto text-left font-ramilas font-bold text-xs sm:text-base md:text-lg lg:text-xl">
              <thead>
                <tr className="bg-custom-green text-white rounded-t-2xl">
                  <th className="px-6 md:px-8 py-4 text-left">
                    S. No
                  </th>
                  <th className="px-6 md:px-8 py-4 text-center">
                    Departments
                  </th>
                  <th className="px-6 md:px-8 py-4 text-right">Quantity</th>
                </tr>
              </thead>
              <tbody className="bg-custom-light-green text-custom-green">
                {libraryTable?.map((item: any, index: any) => (
                  <tr
                    key={index}
                    className={
                      index !== libraryTable.length - 1
                        ? "border-b border-gray-300"
                        : ""
                    }
                  >
                    <td className="px-6 md:px-8 py-4">{item.s_no}</td>
                    <td className="px-6 md:px-8 py-4 text-left">
                      {item.particulars}
                    </td>
                    <td className="px-6 md:px-8 py-4 text-right">
                      {item.quantity}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
            </div>
          </div>
      </div>
    </section>

    <section className=" p-10 px-4 bg-white">
      <div className="container max-w-7xl mx-auto space-y-6 text-center">
        <h2 className="text-custom-green mb-5">Number Of Journal Availibility</h2>
        <Stats isModalOpen={false} statsData={[
          { value: "24", label: "International" },
          { value: "85", label: "National" },
        ]} />
      </div>
    </section>

    <section className=" p-10 px-4 bg-white">
      <div className="container max-w-7xl mx-auto space-y-6 text-center">
        <h2 className="text-custom-green mb-5">Link to Access  To E-Resource Database</h2>

        {EResources?.map((item: any, index: number) => ( 
          <div key={index} className="rounded-2xl border-2 border-white bg-custom-light-green hover:border-custom-green flex justify-between p-4 overflow-hidden transition duration-300">
          <div className="py-8 pl-2">
            <h3 className="text-custom-green mb-5 text-left">{item.heading}</h3>
            <p>{item.content}</p>
          </div>
          <div className="group text-white flex justify-between items-center cursor-pointer">
            <span className="rounded-l-2xl min-h-full bg-custom-green py-5 px-5 flex items-center w-0 translate-x-[50%] overflow-hidden group-hover:w-full transition group-hover:translate-x-[5%] transition duration-500"><h3 className="group-hover:stroke-white -translate-full text-custom-green text-white">Enter</h3></span>
            <span className="rounded-r-2xl min-h-full bg-custom-green py-5 pl-4 pr-8 flex items-center z-10">
              {/* <img src="/library/arr.png" alt="Arrow" /> */}
              <Image
                  src={'/library/arr.png'}
                  alt="Arrow"
                  width={17}
                  height={30}
                  className="!w-[17px] !h-[30px] !max-w-[17px] transition duration-500"
                />
            </span>
          </div>
          </div>
        ))}
       
      </div>
    </section>
      </main>
      <Footer />
    </>
  );
};

export default page; 
