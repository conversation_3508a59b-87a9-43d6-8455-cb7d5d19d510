interface PaginationProps {
  currentPage: number;
  totalPages: number;
  setCurrentPage: (page: number) => void;
}

export default function Pagination({
  currentPage,
  totalPages,
  setCurrentPage,
}: PaginationProps) {
  if (totalPages <= 1) return null;

  const isFirstPage = currentPage === 1;
  const isLastPage = currentPage === totalPages;

  return (
    <section className="flex justify-center items-center gap-3 pt-4 font-ramilas font-semibold">
      {/* Previous Button */}
      <button
        onClick={() => setCurrentPage(currentPage - 1)}
        disabled={isFirstPage}
        className={`border border-custom-green px-4 md:px-10 py-1 rounded-full text-[#454545] ${ // Increased padding for medium screens
          isFirstPage
            ? "opacity-50 cursor-not-allowed"
            : "hover:bg-custom-green hover:text-white"
        }`}
      >
        Previous
      </button>

      {/* Page Numbers */}
      {(() => {
        const pagesToShow = [];
        const pagesAroundCurrent = 1; // Show 1 page before and after current

        // Always add the first page
        pagesToShow.push(1);

        // Calculate start and end for the middle section
        let startPage = Math.max(2, currentPage - pagesAroundCurrent);
        let endPage = Math.min(
          totalPages - 1,
          currentPage + pagesAroundCurrent
        );

        // Add ellipsis before the middle section if needed
        if (startPage > 2) {
          pagesToShow.push("...");
        }

        // Add pages around the current page
        for (let i = startPage; i <= endPage; i++) {
          pagesToShow.push(i);
        }

        // Add ellipsis after the middle section if needed
        if (endPage < totalPages - 1) {
          pagesToShow.push("...");
        }

        // Always add the last page (if totalPages > 1)
        if (totalPages > 1) {
          pagesToShow.push(totalPages);
        }

        // Remove duplicates that might occur if currentPage is near 1 or totalPages
        const uniquePagesToShow = [...new Set(pagesToShow)];

        return uniquePagesToShow.map((page, index) =>
          typeof page === "number" ? (
            <button
              key={page}
              onClick={() => setCurrentPage(page)}
              className={`border px-4 py-1 rounded-full ${
                currentPage === page
                  ? "bg-custom-green text-white"
                  : "border-custom-green text-[#454545] hover:bg-custom-green hover:text-white"
              }`}
            >
              {page}
            </button>
          ) : (
            <span key={`ellipsis-${index}`} className="px-2 py-1 text-[#454545]">
              ...
            </span>
          )
        );
      })()}

      {/* Next Button */}
      <button
        onClick={() => setCurrentPage(currentPage + 1)}
        disabled={isLastPage}
        className={`border border-custom-green px-4 md:px-10 py-1 rounded-full text-[#454545] ${ // Increased padding for medium screens
          isLastPage
            ? "opacity-50 cursor-not-allowed"
            : "hover:bg-custom-green hover:text-white"
        }`}
      >
        Next
      </button>
    </section>
  );
}
