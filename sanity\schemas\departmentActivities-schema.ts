import { defineType, defineField } from "sanity";
import React from 'react'; // Import React

const departmentActivitiesSchema = defineType({
  name: "departmentActivity",
  title: "Department Activity",
  type: "document",
  fields: [
    defineField({
      name: "title",
      title: "Activity Title",
      type: "string",
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "date",
      title: "Date",
      type: "date",
      options: {
        dateFormat: "YYYY-MM-DD",
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: "description",
      title: "Description",
      type: "text",
    }),
    defineField({
      name: "image",
      title: "Image URL",
      type: "url",
      description: "URL of the activity image (e.g., from S3)",
    }),
    // Optional: Add a reference back to the course if needed
    // defineField({
    //   name: 'course',
    //   title: 'Associated Course',
    //   type: 'reference',
    //   to: [{ type: 'courses' }]
    // })
  ],
  preview: {
    select: {
      title: "title",
      subtitle: "date",
      media: "image", // Select the image URL field
    },
    // Add the prepare function to render the image URL correctly
    // Explicitly type the return value and use React.createElement
    prepare(selection: { title: string; subtitle: string; media?: string }): { title: string; subtitle: string; media?: React.ReactNode } {
      const { title, subtitle, media } = selection;
      return {
        title: title,
        subtitle: subtitle,
        // Use React.createElement for the img tag
        media: media ? React.createElement('img', { src: media, alt: title, style: { objectFit: 'cover', height: '100%' } }) : undefined,
      };
    }
  },
});

export default departmentActivitiesSchema;