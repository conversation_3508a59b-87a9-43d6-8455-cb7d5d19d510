"use client";

import Navbar from "@/components/Navbar";
import Footer from "../sections/Footer";
import Link from "next/link";
import Sidebar from "../components/Sidebar";
import { menuItems } from "@/components/Navbar";
import { usePathname } from "next/navigation";
import CTAComponent from "../components/CTAComponent";
import IQACMembersTable from "../components/IQACMembersTable";
import IQACFunctionsCard from "../components/IQACFunctionsCard";
import StudentRepresentativesTable from "../components/StudentRepresentativesTable";
import ControllerExaminationsTeam from "../components/ControllerExaminationsTeam";
import YearButtonsSection from "../components/YearButtonsSection";

const Page = () => {
  const pathname = usePathname();

  const currentSection = menuItems.find(
    (item) => pathname.startsWith(item.href) && item?.subSections
  );

  // IQAC Members Data
  const iqacMembers = [
    { sno: 1, nameDesignation: "Dr. <PERSON>. <PERSON>", category: "Chairperson" },
    { sno: 2, nameDesignation: "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", category: "Management Representative" },
    { sno: 3, nameDesignation: "Dr. A. K. Khaja Nazeerudeen Secretary & Correspondent", category: "Management Representative" },
    { sno: 4, nameDesignation: "Hajee. M.J. Jamal Mohamed Treasurer", category: "Management Representative" },
    { sno: 5, nameDesignation: "Dr. K. Abdus Samad Assistant Secretary", category: "Management Representative" },
    { sno: 6, nameDesignation: "Dr.K.H. Abdul Kader Nihad Member & Hon. Director", category: "Management Representative" },
    { sno: 7, nameDesignation: "Dr. N. Johir Hussain Vice Principal HOD of Mathematics", category: "Senior Administrative Officer" },
    { sno: 8, nameDesignation: "Dr. A. Ishaq Ahmed Additional Vice Principal", category: "Senior Administrative Officer" },
    { sno: 9, nameDesignation: "Dr. A.J. Haja Mohideen Additional Vice Principal HOD of Economics", category: "Senior Administrative Officer" },
    { sno: 10, nameDesignation: "S. Sheik Ismail Burser & HOD of English", category: "Senior administrative officer" },
    { sno: 11, nameDesignation: "Dr. M. Syed Ali Padusha Assoc. Prof. of Chemistry", category: "Coordinator of IQAC & Convenor" },
    { sno: 12, nameDesignation: "Dr. J. Sirajudeen Assoc. Prof. of Chemistry", category: "Teacher Representative & IQAC Member" },
    { sno: 13, nameDesignation: "Dr. M. Salebudeen Asst. Prof. of Zoology", category: "Teacher Representative & IQAC Member" },
    { sno: 14, nameDesignation: "Mr. M. Kumaran Asst. Prof. of English", category: "Teacher Representative & IQAC Member" },
  ];

  // Functions and Objectives Data
  const functionsObjectives = [
    {
      title: "Functions:",
      items: [
        "To focus on institutional functioning towards quality enhancement and facilitate internalization of the quality culture in the College.",
        "To the enhancement and integration among the various activities of the College and institutionalize the healthy practices.",
        "To provide a sound basis for decision making to improve the functioning of the College.",
        "To build internal Communication system."
      ]
    },
    {
      title: "Objective",
      items: [
        "To develop an action plan for each academic year",
        "To interact with management for introducing new academic courses in the College.",
        "Improving infrastructural facilities.",
        "To have departmental interactions with IQAC.",
        "Research and Development Committee.",
        "Conducting Internal and external Quality Audit (Academic Audit).",
        "Conducting seminars / workshops / Faculty Development Programme.",
        "ICT as Teaching-learning Process",
        "Interaction with parents",
        "Organizing cultural and sports activities.",
        "Training of Non-teaching Staff for Automation Process.",
        "Participation in Sports",
        "Soft Skills / Employability skills",
        "Advancing IT/ICT-need System",
        "Organizing Seminars / Workshops",
        "Students Participation in Decision Making",
        "Self Assessment with Performance Appraisal form for the teaching staff",
        "Feedback from Students",
        "Guidance and Counseling Centre",
        "Coaching and Remedial Classes",
        "Students as important stakeholders in Quality Initiatives.",
        "Acquiring feedback from faculties and students",
        "To ensure the adequacy, maintenance and proper allocation of support structure and services for the Institution",
        "working improvement.",
        "Submission of the Annual Quality Assurance Report, UGC Progress Report, NAAC Self Study Report and such other reports as may be declared from time to time."
      ]
    },
    {
      title: "Composition",
      items: [
        "Chairperson Head of the Institution",
        "Three senior administrative officers",
        "Three to eight teachers",
        "One member from the Management",
        "One/two nominees from local society, Students and Alumni",
        "One/two nominees from Employers /Industrialists/stakeholders",
        "One of the senior teachers as the coordinator/Director of the IQAC"
      ]
    }
  ];

  // Student Representatives Data
  const studentRepresentatives = [
    { sno: 1, rollNoName: "MALAVIKA SURESH", category: "STUDENT REPRESENTATIVE" },
    { sno: 2, rollNoName: "M. MOHAMMED DANISH", category: "STUDENT REPRESENTATIVE" },
    { sno: 3, rollNoName: "AAFIYA SHAREEF", category: "STUDENT REPRESENTATIVE" },
    { sno: 4, rollNoName: "MOHAMMED ASHIF", category: "STUDENT REPRESENTATIVE" },
    { sno: 5, rollNoName: "M. RAVI SHANKAR S", category: "STUDENT REPRESENTATIVE" },
    { sno: 6, rollNoName: "HAFSA A", category: "STUDENT REPRESENTATIVE" },
    { sno: 7, rollNoName: "AAFIYA A", category: "STUDENT REPRESENTATIVE" },
  ];

  // Year Buttons Sections Data - Array for mapping
  const yearButtonsSections = [
    {
      title: "The Annual Quality Assurance Report (AQAR)",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
      yearButtons: [
        { year: "AQAR 2023-24" },
        { year: "AQAR 2022-23" },
        { year: "AQAR 2021-22" },
        { year: "AQAR 2020-21" },
        { year: "AQAR 2019-20" },
        { year: "AQAR 2018-19" },
        { year: "AQAR 2017-18" },
        { year: "AQAR 2016-17" },
        { year: "AQAR 2015-16" },
        { year: "AQAR 2014-15" },
        { year: "AQAR 2013-14" },
        { year: "AQAR 2012-13" },
        { year: "AQAR 2011-12" },
        { year: "AQAR 2010-11" },
        { year: "AQAR 2009-10" },
        { year: "AQAR 2008-09" }
      ]
    },
    {
      title: "Action Taken Report",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
      yearButtons: [
        { year: "2023-24" },
        { year: "2022-23" },
        { year: "2021-22" },
        { year: "2020-21" },
        { year: "2019-20" },
        { year: "2018-19" },
        { year: "2017-18" }
      ]
    },
    {
      title: "Student Satisfaction Survey",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
      yearButtons: [
        { year: "2023-24" },
        { year: "2022-23" },
        { year: "2021-22" },
        { year: "2020-21" },
        { year: "2019-20" },
        { year: "2018-19" },
        { year: "2017-18" }
      ]
    },
    {
      title: "The Minutes of Meeting",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
      yearButtons: [
        { year: "2023-24" },
        { year: "2022-23" },
        { year: "2021-22" },
        { year: "2020-21" },
        { year: "2019-20" },
        { year: "2018-19" },
        { year: "2017-18" },
        { year: "2016-17" }
      ]
    },
    {
      title: "Feedback Analysis",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
      yearButtons: [
        { year: "2023-24" },
        { year: "2022-23" },
        { year: "2021-22" },
        { year: "2020-21" },
        { year: "2019-20" },
        { year: "2018-19" },
        { year: "2017-18" },
        { year: "2016-17" }
      ]
    },
    {
      title: "External Academic and Administrative Audit",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
      yearButtons: [
        { year: "2022" },
        { year: "2019" }
      ]
    },
    {
      title: "External Peer Review Report",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
      yearButtons: [
        { year: "2009" }
      ]
    },
    {
      title: "Internal Academic Audit",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
      yearButtons: [
        { year: "2020-21" }
      ]
    },
    {
      title: "Best Practices",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
      yearButtons: [
        { year: "AQAR 2023-24" },
        { year: "AQAR 2022-23" },
        { year: "AQAR 2021-22" },
        { year: "AQAR 2020-21" },
        { year: "AQAR 2019-20" },
        { year: "AQAR 2018-19" },
        { year: "AQAR 2017-18" },
        { year: "AQAR 2016-17" },
        { year: "AQAR 2015-16" },
        { year: "AQAR 2014-15" }
      ]
    },
    {
      title: "Download Links",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.",
      yearButtons: [
        { year: "ICT TOOLS (2023-2024)" },
        { year: "ICT TOOLS (2022-2023)" },
        { year: "ICT TOOLS (2021-2022)" },
        { year: "ICT TOOLS (2020-2021)" }
      ]
    }
  ];

  return (
    <>
      <Navbar fixed={true} border={true} />
      <header
        className="relative pt-40 pb-16 px-4 text-white bg-cover bg-center"
        style={{
          backgroundImage: "url('/bg_image.webp')",
        }}
      >
        {/* Overlay */}
        <div className="absolute inset-0 bg-custom-green bg-opacity-[0.83]"></div>

        <div className="relative container max-w-7xl mx-auto z-10 flex flex-col md:flex-row md:justify-between md:items-center">
          <h1 className="font-ramilas text-center md:text-left mb-4 md:mb-0">
            IQAC
          </h1>
          <ul className="font-poppins flex flex-row justify-center md:justify-end gap-8 md:gap-8 md:pr-16">
            <li className="list-disc">
              <Link href="/">Home</Link>
            </li>
            <li className="list-disc">IQAC</li>
          </ul>
        </div>
      </header>

      <main>
        {currentSection && (
          <Sidebar subsections={currentSection.subSections || []} />
        )}

        {/* First Section - IQAC Introduction with Double Shadow */}
        <section className="bg-[#D1D9D1]">
          <div className="relative px-4 md:px-24 py-8 md:py-12 shadow-stacked-cards mb-20 rounded-xl border-b border-custom-green">
            <div className="text-center mb-8">
              <h2 className="text-3xl md:text-4xl font-bold text-custom-green mb-8">
                Internal Quality Assurance Cell (IQAC)
              </h2>
              <p className="text-gray-700 text-lg leading-relaxed">
                Internal Quality Assurance Cell (IQAC) has been established in Jamal Mohamed College as per the guidelines of NAAC for post accreditation quality sustenance measures for performance and quality assessment and accreditation and quality upgradation of the College. IQAC broadly encompasses a continuous process. The IQAC has become a part of the institution and works towards realisation of the goals of quality enhancement and sustenance. The prime task of the IQAC is to develop a system for conscious, consistent and catalytic action to improve the academic and administrative performance of the institution. IQAC will facilitate / contribute for Credibility enhancement, Performance evaluation, Clarity and focus in institutional functioning towards quality enhancement, Internalization of the quality culture, Institutionalization of best practices, Better internal communication in the post accreditation phase of institutions.
              </p>
            </div>
          </div>
        </section>

        {/* Second Section - Quality Policy and Objective in Single Card (PlacementPolicy Style) */}
        <section className="bg-[#D1D9D1]">
          {/* Main wrapper with white background and padding */}
          <div className="relative px-8 md:px-16 py-8 md:py-4 bg-white">
            {/* Title */}
            <div className="text-center mb-8">
              <h2 className="text-3xl md:text-4xl font-bold text-custom-green mb-6">
                Quality Policy
              </h2>
            </div>

            {/* Policy Card - Single card with glowing shadow */}
            <div className="bg-white px-8 lg:px-24 py-16 rounded-2xl shadow-[0_0_24px_rgba(0,0,0,0.3)]">
              <div className="space-y-6">
                {/* Quality Policy Introduction */}
                <div className="space-y-3">
                  <p className="text-gray-700 leading-relaxed text-base md:text-lg">
                    Jamal Mohamed College endeavours to provide a unique experience which will enable the individuals to realize their innate potentials and mould their overall personality by:
                  </p>
                </div>

                {/* Quality Policy Points */}
                <div className="space-y-4">
                  <ul className="space-y-4 text-gray-700 text-lg leading-relaxed">
                    <li className="flex items-start space-x-3 px-4">
                      <span className="text-custom-green font-bold text-lg mt-1">•</span>
                      <span>offering world class curriculum at affordable cost.</span>
                    </li>
                    <li className="flex items-start space-x-3 px-4">
                      <span className="text-custom-green font-bold text-lg mt-1">•</span>
                      <span>inculcating and nurturing ethical, spiritual, moral and human values among the students.</span>
                    </li>
                    <li className="flex items-start space-x-3 px-4">
                      <span className="text-custom-green font-bold text-lg mt-1">•</span>
                      <span>providing the state-of-the-art infrastructure to facilitate Teaching & Learning and to foster Research & Consultancy.</span>
                    </li>
                    <li className="flex items-start space-x-3 px-4">
                      <span className="text-custom-green font-bold text-lg mt-1">•</span>
                      <span>creating interest, instilling confidence and amplifying competency among the teachers through modern methods of teaching, learning and evaluation.</span>
                    </li>
                    <li className="flex items-start space-x-3 px-4">
                      <span className="text-custom-green font-bold text-lg mt-1">•</span>
                      <span>collaborating with National / International Institutions / Organizations of high repute to provide quality education.</span>
                    </li>
                  </ul>
                </div>

                {/* Objective Section */}
                <div className="pt-4">
                  <div className="space-y-3 mb-6">
                    <h3 className="text-xl md:text-2xl font-bold text-custom-green">
                      Objective
                    </h3>
                    <div className="px-4 flex items-start space-x-3">
                      <span className="text-custom-green font-bold text-lg mt-1">•</span>
                      <p className="text-gray-700 leading-relaxed text-base md:text-lg">
                        To establish facilities that offer the following objectives:
                      </p>
                    </div>
                  </div>

                  {/* Objective Points */}
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3 px-4">
                      <span className="text-custom-green font-bold text-lg mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed text-base md:text-lg">To focus on institutional functioning towards quality enhancement and facilitate internalization of the quality culture.</span>
                    </div>
                    <div className="flex items-start space-x-3 px-4">
                      <span className="text-custom-green font-bold text-lg mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed text-base md:text-lg">To the enhancement and integration among the various activities of the College and institutionalize the healthy practices.</span>
                    </div>
                    <div className="flex items-start space-x-3 px-4">
                      <span className="text-custom-green font-bold text-lg mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed text-base md:text-lg">To provide a sound basis for decision making to improve the functioning of to act as a change agent in the College.</span>
                    </div>
                    <div className="flex items-start space-x-3 px-4">
                      <span className="text-custom-green font-bold text-lg mt-1">•</span>
                      <span className="text-gray-700 leading-relaxed text-base md:text-lg">To build internal communication system.</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* IQAC Members Table */}
        <IQACMembersTable
          title="IQAC Members"
          members={iqacMembers}
        />

        {/* Functions and Objectives Card */}
        <IQACFunctionsCard
          title="Functions & Objectives"
          sections={functionsObjectives}
        />

        {/* Student Representatives Table */}
        <StudentRepresentativesTable
          title="Student Representatives"
          representatives={studentRepresentatives}
        />

        {/* Controller of Examinations Team */}
        <ControllerExaminationsTeam title="Controller of Examinations" />

        {/* Year Buttons Sections - Mapped from array */}
        {yearButtonsSections.map((section, index) => (
          <YearButtonsSection
            key={index}
            title={section.title}
            description={section.description}
            yearButtons={section.yearButtons}
            backgroundColor={"bg-white"}
          />
        ))}

        <section className="bg-white py-12 px-4">
          <div className="container max-w-7xl mx-auto">
            <CTAComponent />
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
};

export default Page;
